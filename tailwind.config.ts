/** @type {import('tailwindcss').Config} */

module.exports = {
  darkMode: "class",
  content: ["./src/**/*.{js,ts,jsx,tsx}"],

  theme: {
    fontFamily: {
      yekan: ["yekan-bakh"]
    },
    extend: {
      colors: {
        borderBorderAndDivider: "#545454",
        backgroundBodyBackground: "#28282C",
        backgroundCardBackground: "#343438",
        semanticPrimary: "#0C82F9",
        backgroundSidebar: "#B9B4AB",
        mainBlue: "#0C82F9",
        error: "#ff4da5",
        light: "#e4e9f5",
        disabled: "#525563",
        primary: "#0a84ff",
        mediumRed: "#F83B3B",
        mediumBlack: "#161616",
        darkBlack: "#00000F",
        mediumGray: "#BDBDBD",
        smokeWhite: "#F4F4F4",
        mediumWhite: "#FAFCFB",
        mediumBlue: "#CCE6FD",
        mediumBlue4: "#2B4766",
        mainText: "#F4F4F4",
        secondaryText: "#BDBDBD",
        cardBackground: "#343438",
        backgroundDarkRow: "#1F1F22",
        secondaryCardBackground: "#3D3D3D",
        white50: "#ffffff1a",
        white100: "#EFEFEF",
        white200: "#DCDCDC",
        bodyBackground: "#28282C",
        black1: "#1F1F22",
        textDisabled: "#676767",
        backgroundLightRow: "#252529",
        textDescending: "#FF5E5E",
        textAscending: "#A5F539",
        cardDarkBlue: "#1F3253",
        cardDarkGreen: "#1C2F2F",
        cardDarkestBlue: "#152137",
        cardDarkestGreen: "#112122",
        darkGreen: "#006C17",
        marketGreen: "#29B547",
        lightGreen: "#71DB88",
        disable: "#76787A",
        lightRed: "#DB7171",
        marketRed: "#B52929",
        marketDarkRed: "#6C0000",
        warningBorder: "#F83B3B",
        warningText: "#FFA0A0",
        radiance500: "#2EA6FF",
        radiance600: "#0C82F9",
        radiance800: "#1458B9",
        radiance900: "#164D92",
        Firefly400: "#5EA3A0",
        azureRadiance400: "#55C3FF",
        azureRadiance800: "#1458b9",
        navyBlue50: "#F1F4FF",
        radiance200: "#BAE7FF80",
        grayWhite: "#ABABAB",
        inputFill: "#3d3d3d4d",
        textUnchanged: "#39C8F5",
        darkRed: "#190000",
        mediumYellow: "#F1C21B",
        newHeaderAndSidebar: "#1F2122",
        lightYellow: "#C7DA41",
        cyan: "#26D5C0",
        semanticWithoutCoupon: "#C7DA41",
        semanticWithCoupon: "#26D5C0",
        carnation900: "#841818",
        jade800: "#126945",
        japaneseLaurel700: "#00A216",
        dark_black: "#1F1F22",

        backgroundDisable: "#76787A",

        semanticPrimary2: "#0C82F9",
        textNegative: "#F8FAFF",

        borderColor: "#8f94a2",
        greenYellow: "#7DDA08",
        saffron: "#E1AB11",
        background: "#F8FAFF",
        lightBlue: "#0A84FFB3",
        lighterBlue2: "#C2DEEA",
        bgBlue: "#0A84FF1A",
        lightBgBlue: "#e6f0fa",
        lightGray: "#E3E3E3",
        lightGray2: "#3D3D3D4D",
        lightGrayBg: "#F7F7F7",
        grayBorder: "#6C757D80",

        chevronLight: "#ABBBCB",
        chevronHover: "#CCE6FF",
        pink: "#E62B88",
        pinkBg: "#FFEDF6",
        primaryHover: "#086ACC",
        textWhite: "#FAFCFB",
        lightBlack: "#525563",
        headerBlack: "#080707",
        primaryBorder: "#033160",
        primaryFocus: "#064F99",
        secondaryBorder: "#C5C5C5",
        textSecondary: "#BDBDBD",
        categoryCardBorder: "#8F94A23D",
        backgroundBlue: "#E4EFFF",
        scrollBar: "#5ea1fb",
        whiteSmoke: "#FBFBFB",
        primaryOrange: "#FF8B36",
        borderLightGray: "#F4F4F4",
        gray1: "#EFEFEF",
        dangerRed: "#C71812",
        bgHeader: "#F6F6F6",
        lightPurple: "#9B20FC",
        lightPurple2: "#8871BA",
        lightPurple3: "#5c4391",
        darkPurple1: "#862CA5",
        blurWhite: "#ffffff66",
        silverBtn: "#B7BBBE80",
        light_blue1: "#0F5CC6",
        light_blue2: "#55C3FF",
        withCouponCyan: "#26D5C0",
        cyan1: "#2A9AB0",
        withCouponYellow: "#C7DA41",
        background_New_Header_and_sidebar: "#1F2122",
        scrollBlack: "rgba(4, 12, 21, 0.80)",
        jade_700: "#108554",
        tall_poppy_500: "#E35050",
        safforn_200: "#FAEB8E",
        jade_800: "#126945",
        jade_900: "#11563a",

        // new colors
        primary_Blue01: "#E0EDFF",
        primary_Blue02: "#ADD0FF",
        primary_Blue05: "#1477FF",
        primary_Blue06: "#005FE0",
        primary_Blue07: "#0049AD",
        primary_Blue08: "#00347A",
        primary_BrandBlue1: "#EAEAEC",
        primary_BrandBlue2: "#D8DFEA",
        primary_BrandBlue3: "#6AA8FC",
        primary_BrandBlue4: "#2976E0",
        primary_BrandBlue5: "#0B428D",
        primary_BrandBlue6: "#00376E",

        secondary_orang: "#F99144",
        secondary_orang2: "#DD803C",
        secondary_orang3: "#C17034",
        secondary_orang05: "#F8791C",
        secondary_orang06: "#ED6907",
        secondary_orang07: "#C15506",

        light_white: "#FBFCFE",
        light_white1: "#F1F2F4",
        light_white2: "#E9EAEC",
        light_white3: "#E5E7EB",
        light_white4: "#DADCDE",
        light_white5: "#CACCD0",
        light_white7: "#D9D9D9",
        light_white03: "#EBEDEF",
        light_white04: "#E3E5E9",
        light_white05: "#FFFFFF",

        dark_black01: "#B7BBBE",
        dark_black02: "#8F94A2",
        dark_black03: "#4E5868",
        dark_black04: "#343B49",
        dark_black05: "#212529",
        dark_black06: "#262D38",
        dark_black6: "#171C23",
        dark_black7: "#28282C",
        dark_black8: "#545454",
        dark_black9: "#252529",
        dark_black10: "#3D3D3D",
        dark_black11: "#14141c",
        dark_black12: "#404040",
        dark_black13: "#858585",

        accept_green01: "#ECFEF7",
        accept_green03: "#EDFCF6",
        accept_green05: "#12C781",
        accept_green06: "#04BC76",
        accept_green07: "#43E5A0",
        accept_green08: "#1ACD81",
        accept_green09: "#0FA968",

        danger_red01: "#FEECF1",
        danger_red05: "#EE1B57",
        danger_red06: "#C70F43",

        warning_yellow01: "#FFF7ED",
        warning_yellow06: "#FB3",

        error_red01: "#EE2662",
        error_red05: "#FDE3EA",

        text_error_default: "#F83B3B",

        primary_brown1: "#9B5E0D",
        primary_brown2: "#6d4208",
        background_Dark_row: "#F8F8F8"
      },
      animation: {
        marquee: "marquee 3s linear infinite"
      },
      keyframes: {
        marquee: {
          "0%": { transform: "translateX(-110%)" },
          "100%": { transform: "translateX(-0%)" }
        }
      },
      backgroundImage: {
        mackBook: "url('/images/login-image.png')",
        noData: "url('/images/NoStockData.svg')"
      },
      boxShadow: {
        tooltip: "0 4px 15px 1px #1013243d",
        "card-hover": "10px 20px 30px #4A4A4A33",
        tradeChipsSelect: "0px 0px 10px 0px rgba(0, 0, 0, 0.25) inset",
        ytmChipsSelect: "0px 0px 10px 0px rgba(0, 0, 0, 0.25) inset",
        shadow3px: "inset 0 0 0 3px",
        shadow2px: "inset 0 0 0 2px",
        shadow1px: "inset 0 0 0 1px",
        warningShadow: "0px 0px 2px 2px rgba(255, 160, 160, 0.50)",
        ascendingShadow: "0px 0px 2px 2px rgba(165, 245, 57, 0.50)"
      },
      fontSize: {
        ".8xs": ["0.625rem", "1.7"], // 10px
        ".9xs": ["0.687rem", "1.7"], // 11px
        ".9sm": ["0.8125rem", "1.7"], // 13px
        ".9base": ["0.9375rem", "1.7"] // 15px
      },
      borderWidth: {
        1: "1px" // 1px
      },
      spacing: {
        ".5": "0.125rem", // 2px
        "3px": "3px",
        1.25: "0.3125rem", // 5px
        2.25: "0.5625rem", // 9px
        110: "27.5rem" // 440px
      }
    }
  },
  // eslint-disable-next-line global-require
  plugins: []
};
