import { AgGridReact } from "ag-grid-react";
import { MutableRefObject, useCallback, useEffect, useRef } from "react";

// maximum time to show loading. after this period data not found will be displayed
const delay = 60;

function useValidateChartData<T>(data: any, gridRef: MutableRefObject<AgGridReact<T> | null>) {
  const timer = useRef<undefined | NodeJS.Timeout>(undefined);
  const showLoading = useCallback(() => {
    gridRef.current?.api?.showLoadingOverlay();
  }, []);

  const showNoData = useCallback(() => {
    gridRef.current?.api?.showNoRowsOverlay();
  }, []);

  const hideLayers = useCallback(() => {
    gridRef.current?.api?.hideOverlay();
  }, []);

  useEffect(() => {
    if (data && Object?.keys(data)?.length) {
      if (timer.current) {
        hideLayers();
        clearInterval(timer.current);
        timer.current = undefined;
      }
    } else {
      showLoading();
      timer.current = setTimeout(() => showNoData(), delay * 1000);
    }
    return () => {
      clearInterval(timer.current);
    };
  }, [data]);
}

export default useValidateChartData;
