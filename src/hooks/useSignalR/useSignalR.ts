/* eslint-disable no-underscore-dangle */
/* eslint-disable react-hooks/rules-of-hooks */
/* eslint-disable consistent-return */
/* eslint-disable no-console */
/* eslint-disable react-hooks/exhaustive-deps */

import { startSignalRConnection } from "@/utils/helpers";
import { Optional } from "@/utils/typeHelpers";
import { HubConnection, HubConnectionBuilder, IStreamSubscriber } from "@microsoft/signalr";
import { useEffect, useRef, useState } from "react";

export const DELAY_TO_UPDATE_SOCKET = 300;
export type TSubscriber<T> = Optional<IStreamSubscriber<T>, "complete">;

interface ConnectionInfo<T> {
  connection: HubConnection;
  subscribers: Set<TSubscriber<T>>;
}

const connectionMap = new Map<string, ConnectionInfo<any>>();

const useSignalR = () =>
  function SignalR<T>(
    url: string,
    streamName: string,
    subscriber: TSubscriber<T>,
    id?: string,
    isEnabled = true,
    isInvokable = false,
    isUpdatable = false
  ): string {
    const args = id ? [streamName, id] : [streamName];
    const argId = id ? [id] : [];
    const [status, setStatus] = useState("disconnected");
    // const accessToken = getCookie(cookies.access_token) ?? "";
    // const options = {
    //   accessTokenFactory: () => accessToken
    // };

    const subscribersRef = useRef(new Set<TSubscriber<T>>());
    subscribersRef.current.add(subscriber);

    useEffect(() => {
      if (!isEnabled && !isUpdatable) {
        return;
      }

      const connectionKey = `${url}-${streamName}-${id || ""}`;
      let connectionInfo = connectionMap.get(connectionKey);

      if (!connectionInfo || isUpdatable) {
        const connection = new HubConnectionBuilder()
          .withUrl(`${process.env.NEXT_PUBLIC_SOCKET_BASE_URL || ""}${url}`)
          .build();

        connectionInfo = { connection, subscribers: new Set<TSubscriber<T>>() };
        connectionMap.set(connectionKey, connectionInfo);

        setStatus("connecting");

        startSignalRConnection(connection, streamName)
          .then(() => {
            setStatus("connected");
            if (isInvokable) {
              if (connection?.state === "Connected") {
                connection.invoke(streamName, ...argId).then(res => {
                  subscriber.next(res);
                });
              }
            } else {
              connection.stream(...(args as [string])).subscribe({
                next: (item: T) => {
                  connectionInfo!.subscribers.forEach(sub => sub.next?.(item));
                },
                error: (error: any) => {
                  connectionInfo!.subscribers.forEach(sub => sub.error?.(error));
                },
                complete: () => {
                  connectionInfo!.subscribers.forEach(sub => sub.complete?.());
                }
              });
            }
          })
          .catch((error: any) => {
            setStatus("error");
            connectionInfo!.subscribers.forEach(sub => sub.error?.(error));
          });

        connection.on(streamName, (item: T) => {
          setTimeout(() => {
            connectionInfo!.subscribers.forEach(sub => sub.next?.(item));
          }, DELAY_TO_UPDATE_SOCKET);
        });
      }

      connectionInfo.subscribers.add(subscriber);

      return () => {
        connectionInfo!.subscribers.delete(subscriber);
        if (connectionInfo!.subscribers.size === 0) {
          connectionInfo!.connection.stop();
          connectionMap.delete(connectionKey);
        }
      };
    }, [url, streamName, id, isEnabled]);

    return status;
  };

export default useSignalR();
