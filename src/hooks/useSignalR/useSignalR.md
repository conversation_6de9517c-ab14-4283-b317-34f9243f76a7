# useSignalR Hook

The `useSignalR` hook is a custom React hook used for managing SignalR connections in React components.

## Code

```javascript
import useSignalR from "@/hooks/useSignalR";
import socketUrls from "@/constants/socketUrls";
import { useQueryClient } from "@tanstack/react-query";

useSignalR(socketUrls.priceChangeYtmUrl, socketUrls.priceChangeYtmStreamName, {
  next: (item:Type) => {
    const cacheData = queryClient.getQueryData < Type > [KEY];

    const updatedData = item;
    queryClient.setQueryData([KEY], updatedData);
  },
  error: () => {}
});
```
