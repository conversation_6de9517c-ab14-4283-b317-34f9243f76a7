import { AxiosError } from "axios";
import { useEffect, useState } from "react";
import useSignalR from "../useSignalR/useSignalR";

interface ISocketInitialData {
  url: string;
  streamName: string;
  id?: string;
  isEnabled?: boolean;
  isInvoke?: boolean;
  isUpdatable?: boolean;
}

const useSocketInitialData = <T>({
  streamName,
  url,
  id,
  isEnabled = true,
  isInvoke = true,
  isUpdatable = false
}: ISocketInitialData) => {
  const [isLoading, setIsLoading] = useState(true);
  const [openConnection, setConnection] = useState(true);
  const [data, setData] = useState<T | undefined>(undefined);
  const [error, setError] = useState<AxiosError>();

  const isEnabledSocket = isEnabled && openConnection;

  useSignalR<T>(
    url,
    streamName,
    {
      next: (value: T) => {
        setData(value);
        setIsLoading(false);
        setConnection(false);
      },
      error: (err: AxiosError) => {
        setError(err);
        setIsLoading(false);
      }
    },
    id,
    isEnabledSocket,
    isInvoke,
    isUpdatable
  );

  useEffect(() => {
    if (isEnabled) {
      setIsLoading(true);
    }
  }, [isEnabled]);

  return { isLoading, data, error, setData };
};

export default useSocketInitialData;
