import useDelayedStack from "@reactutils/use-delayed-stack";
import { TMarketMapData, TMarketMapResponse } from "@/queries/marketMapAPI";
import endpoints from "@/constants/api";
import _ from "lodash";
import { useQueryClient } from "@tanstack/react-query";

function useUpdateMarketMap(update: (val: TMarketMapData[]) => void) {
  const queryClient = useQueryClient();

  const [pushToStack] = useDelayedStack<TMarketMapData>(stackData => {
    const cacheData = queryClient.getQueryData<TMarketMapResponse>([endpoints.getMarketMap]);
    const oldData: any = {};
    const newData: any = {};
    cacheData?.data?.forEach(item => {
      oldData[item?.isin] = item;
    });
    stackData?.forEach(item => {
      newData[item?.isin] = { ...newData?.[item?.isin], ...item };
    });
    const mergedData = _.merge(oldData, newData);

    queryClient.setQueryData([endpoints.getMarketMap], {
      ...cacheData,
      data: Object.values(mergedData)
    });

    update(Object.values(mergedData));
  }, 2000);

  return pushToStack;
}

export default useUpdateMarketMap;
