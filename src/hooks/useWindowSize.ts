import { useLayoutEffect, useState } from "react";

export interface ISize {
  width?: number;
  height?: number;
}

export function useWindowSize(func?: () => void): ISize {
  const [size, setSize] = useState<ISize>({
    width: 0,
    height: 0
  });

  useLayoutEffect(() => {
    function updateSize() {
      if (func) {
        func();
      }
      setSize({
        width: window.innerWidth,
        height: window.innerHeight
      });
    }

    window.addEventListener("resize", updateSize);
    updateSize();

    return () => {
      window.removeEventListener("resize", updateSize);
    };
  }, []);

  return size;
}

export function IsMobileSize(): boolean {
  const { width } = useWindowSize();
  if (typeof window === "undefined" || !width) {
    return false;
  }

  return width < 720;
}
