import cookies from "@/constants/cookies";
import routs from "@/constants/routes";
import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import { pathToRegexp } from "path-to-regexp";

export const config = {
  matcher: ["/((?!api|_next/static|_next/image|favicon.ico|manifest.json|assets).*)"]
};

const authRequiredPaths = ["/((?!api|_next/static|_next/image|favicon.ico|manifest.json|assets|images|hc).*)"]; // means all paths except /api, /_next/static, /_next/image, /favicon.ico, /assets, /images
const authRequiredPathsWhiteList = [routs.login];

/* ------- This function can be marked `async` if using `await` inside ------ */
export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  const isAuthRequiredPath =
    pathToRegexp(authRequiredPaths).test(pathname || "") &&
    !pathToRegexp(authRequiredPathsWhiteList).test(pathname || "");

  const isLoggedIn = request.cookies.get(cookies.isLoggedIn)?.value;
  const loginPath = routs.login;

  // if user is not logged in and tries to access restricted area, redirect to login page
  if (isAuthRequiredPath && !isLoggedIn) {
    return NextResponse.redirect(new URL(loginPath, request.url));
  }

  // if user is logged in and tries to access login page, redirect to home page
  if (isLoggedIn && pathToRegexp([routs.login]).test(pathname || "")) {
    return NextResponse.redirect(new URL(routs.home, request.url));
  }

  return NextResponse.next();
}
