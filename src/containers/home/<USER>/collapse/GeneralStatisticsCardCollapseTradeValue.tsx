/* eslint-disable no-unsafe-optional-chaining */
import Pattern from "@/assets/icons/Pattern.svg";
import ArrowUp from "@/assets/icons/arrow-up.svg";
import { useServerTime } from "@/components/organisms/Header/components/utils";
import { commaSeparator, toDecimals } from "@/utils/helpers";
import Link from "next/link";
import { twMerge } from "tailwind-merge";
import { ShowTradeValuePercent } from "../utils";
import { IGeneralStatisticsCardProps } from "./type";

function GeneralStatisticsCardCollapseTradeValue({ title, className, link, tradeValues }: IGeneralStatisticsCardProps) {
  const { time } = useServerTime();
  const ShowTradeValuePercentIncludeTime = time && ShowTradeValuePercent(time);

  return (
    <Link href={link} replace data-test="4c2f72d7-847b-4432-9686-9b6b81d6bcb3" className="h-full overflow-hidden">
      <div
        className={twMerge(
          "pt-3 px-3 pb-2  relative rounded-lg outline outline-1 outline-transparent hover:outline hover:outline-1 h-full  ",
          className
        )}
      >
        <Pattern className="absolute left-0 top-0" />
        <div className="flex items-center justify-between leading-4 pb-2">
          <div className="text-xs text-mainText">{title}</div>
        </div>
        <div className="flex items-center leading-6">
          <div className="flex gap-2 border-l-1 border-l-secondaryText items-center w-1/2">
            <div className="flex items-center gap-2">
              <div className="text-xs text-mainText whitespace-nowrap">کوپن دار</div>

              {ShowTradeValuePercentIncludeTime &&
                (tradeValues?.data?.couponTotalValueChangePercent ? (
                  <div className="flex items-center text-xs gap-1 bg-[rgba(31,31,34,0.6)] px-2 py-1 rounded h-6">
                    <div
                      className={
                        tradeValues && tradeValues?.data?.couponTotalValueChangePercent > 0
                          ? "text-textAscending ltr"
                          : "text-textDescending ltr"
                      }
                    >
                      %{tradeValues && toDecimals(tradeValues?.data?.couponTotalValueChangePercent, 2, false)}
                    </div>
                    {tradeValues && tradeValues?.data?.couponTotalValueChangePercent > 0 ? (
                      <ArrowUp className="text-textAscending h-3 w-3" />
                    ) : (
                      <ArrowUp className="text-textDescending rotate-180 h-3 w-3" />
                    )}
                  </div>
                ) : (
                  "---"
                ))}
            </div>

            {tradeValues?.data?.couponTotalValue ? (
              <div className="text-mainText flex">
                <div className="relative top-[2px] ml-[2px]">B</div>
                {Number(tradeValues?.data?.couponTotalValue) > 1000000000
                  ? commaSeparator(Number(tradeValues?.data?.couponTotalValue) / 1000000000, 1, false, 1)
                  : commaSeparator(tradeValues?.data?.couponTotalValue, 1, false, 1)}
              </div>
            ) : (
              <div className="text-mainText">---</div>
            )}
          </div>
          <div className="flex items-center gap-2  w-1/2 pr-6">
            <div className="flex items-center gap-2">
              <div className="text-xs text-mainText whitespace-nowrap">بدون کوپن</div>

              {ShowTradeValuePercentIncludeTime &&
                (tradeValues?.data?.zeroCouponTotalValueChangePercent ? (
                  <div className="flex items-center text-xs gap-1 bg-[rgba(31,31,34,0.6)] px-2 py-1 rounded h-6 ">
                    <div
                      className={
                        tradeValues && tradeValues?.data?.zeroCouponTotalValueChangePercent > 0
                          ? "text-textAscending ltr"
                          : "text-textDescending ltr"
                      }
                    >
                      %{tradeValues && toDecimals(tradeValues?.data?.zeroCouponTotalValueChangePercent, 2, false)}
                    </div>
                    {tradeValues && tradeValues?.data?.zeroCouponTotalValueChangePercent > 0 ? (
                      <ArrowUp className="text-textAscending h-3 w-3" />
                    ) : (
                      <ArrowUp className="text-textDescending rotate-180 h-3 w-3" />
                    )}
                  </div>
                ) : (
                  "---"
                ))}
            </div>

            {tradeValues?.data?.zeroCouponTotalValue ? (
              <div className="text-mainText flex">
                <div className="ml-[2px]">B</div>
                {Number(tradeValues?.data?.zeroCouponTotalValue) > 1000000000
                  ? commaSeparator((Number(tradeValues?.data?.zeroCouponTotalValue) / 1000000000)?.toFixed(1), 1, false)
                  : commaSeparator(tradeValues?.data?.zeroCouponTotalValue, 1, false)}
              </div>
            ) : (
              <div className="text-mainText ">---</div>
            )}
          </div>
        </div>
      </div>
    </Link>
  );
}

export default GeneralStatisticsCardCollapseTradeValue;
