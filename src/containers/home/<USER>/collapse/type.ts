import { TTodayAverageResponse, TTotalValueDataResponse, TTotalValueDurationResponse } from "@/queries/homeCards/types";
import { ReactNode } from "react";

export interface IGeneralStatisticsCardProps {
  title: string;
  className: string;
  link: string;
  tradeValues?: TTotalValueDataResponse;
  todayAverage?: TTodayAverageResponse;
  tooltipContent?: ReactNode;
}
export interface IGeneralDurationValuesProps {
  title: string;
  className: string;
  link: string;
  durationValues?: TTotalValueDurationResponse;
  todayAverage?: TTodayAverageResponse;
  tooltipContent?: ReactNode;
}

export interface IGeneralStatisticsCollapseProps {
  collapseClassName?: string;
  tradeValues?: TTotalValueDataResponse;
  todayAverage?: TTodayAverageResponse;
  tradeValuesLoading?: boolean;
  averageLoading?: boolean;
  activeBox?: "market" | "ytm" | "duration";
  durationValues?: TTotalValueDurationResponse;
  duraionLoading: boolean;
}
