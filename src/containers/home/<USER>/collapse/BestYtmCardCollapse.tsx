import { IGetTopYtmItem } from "@/queries/topYtmAPI/type";
import React from "react";
import { twMerge } from "tailwind-merge";

function BestYtmCardCollapse({ item, index }: IGetTopYtmItem) {
  return (
    <div
      className={twMerge(
        "flex items-center justify-between w-full px-2 bg-backgroundLightRow py-2 border-l-1 border-l-borderBorderAndDivider ",
        index === 4 && "border-none"
      )}
    >
      {item?.symbol ? <div className="text-sm leading-6 font-bold text-mainText">{item?.symbol}</div> : "---"}

      <div className="text-[10px] text-grayWhite">بهترین مظنه فروش</div>
      {item?.bestSellYtmToPercent ? (
        <div className="text-mainText  text-[14px]">%{item?.bestSellYtmToPercent}</div>
      ) : (
        "---"
      )}
    </div>
  );
}

export default BestYtmCardCollapse;
