import Info from "@/assets/icons/Info.svg";
import Pattern from "@/assets/icons/Pattern.svg";
import ArrowUp from "@/assets/icons/arrow-up.svg";
import { Tooltip } from "@/components/atoms/tooltip";
import Link from "next/link";
import { twMerge } from "tailwind-merge";
import { IGeneralStatisticsCardProps } from "./type";

function GeneralStatisticsCardCollapseTodayAverage({
  title,
  className,
  link,
  todayAverage,
  tooltipContent
}: IGeneralStatisticsCardProps) {
  return (
    <Link href={link} data-test="f7936358-2ec3-41ff-95cb-16b2de68c079" className="h-full overflow-hidden">
      <div
        className={twMerge(
          "pt-3 px-3 pb-2  relative rounded-lg outline outline-1 outline-transparent hover:outline hover:outline-1 h-full ",
          className
        )}
      >
        <Pattern className="absolute left-0 top-0 pointer-events-none" />
        <div className="flex items-center justify-between pb-2">
          <div className="text-xs leading-5 text-mainText">{title}</div>

          {tooltipContent && (
            <Tooltip
              content={tooltipContent}
              placement="bottom"
              // eslint-disable-next-line react/no-children-prop
              children={
                <div data-test="b84c76af-8db3-4907-b713-2b3ea11fe7d0">
                  <Info className="h-5 w54" />
                </div>
              }
            />
          )}
        </div>
        <div className="flex items-center">
          <div className="flex gap-2 border-l-1 border-l-secondaryText items-center w-1/2 ">
            <div className="flex items-center gap-2">
              <div className="text-xs text-mainText whitespace-nowrap">کوپن دار</div>

              {todayAverage?.data.couponBondAverageYtmChangeInPercent ? (
                <div className="flex items-center text-xs gap-1 bg-[rgba(31,31,34,0.6)] px-2 py-1 rounded h-6">
                  <div
                    className={
                      todayAverage && todayAverage.data.couponBondAverageYtmChangeInPercent > 0
                        ? "text-textAscending ltr"
                        : "text-textDescending ltr"
                    }
                  >
                    %{todayAverage && Number(todayAverage.data.couponBondAverageYtmChangeInPercent).toFixed(2)}
                  </div>
                  {todayAverage && todayAverage.data.couponBondAverageYtmChangeInPercent > 0 ? (
                    <ArrowUp className="text-textAscending h-3 w-3" />
                  ) : (
                    <ArrowUp className="text-textDescending rotate-180 h-3 w-3" />
                  )}
                </div>
              ) : (
                "---"
              )}
            </div>

            {todayAverage?.data.couponBondTodayAverageYtmInPercent ? (
              <div className="text-mainText ">{`%${
                todayAverage && Number(todayAverage.data.couponBondTodayAverageYtmInPercent).toFixed(1)
              }`}</div>
            ) : (
              <div className="text-mainText">---</div>
            )}
          </div>

          <div className="flex items-center w-1/2 gap-2 pr-6">
            <div className="flex items-center gap-2">
              <div className="text-xs text-mainText whitespace-nowrap ">بدون کوپن</div>

              {todayAverage?.data.zeroCouponBondAverageYtmChangeInPercent ? (
                <div className="flex items-center text-xs gap-1 bg-[rgba(31,31,34,0.6)] px-2 py-1 rounded h-6">
                  <div
                    className={
                      todayAverage && todayAverage.data.zeroCouponBondAverageYtmChangeInPercent > 0
                        ? "text-textAscending ltr"
                        : "text-textDescending ltr"
                    }
                  >
                    %{todayAverage && Number(todayAverage.data.zeroCouponBondAverageYtmChangeInPercent).toFixed(2)}
                  </div>

                  {todayAverage && todayAverage.data.zeroCouponBondAverageYtmChangeInPercent > 0 ? (
                    <ArrowUp className="text-textAscending h-3 w-3" />
                  ) : (
                    <ArrowUp className="text-textDescending rotate-180 h-3 w-3" />
                  )}
                </div>
              ) : (
                "---"
              )}
            </div>

            {todayAverage?.data.zeroCouponBondTodayAverageYtmInPercent ? (
              <div className="text-mainText">
                {`%${todayAverage && Number(todayAverage.data.zeroCouponBondTodayAverageYtmInPercent).toFixed(1)}`}
              </div>
            ) : (
              <div className="text-mainText">---</div>
            )}
          </div>
        </div>
      </div>
    </Link>
  );
}

export default GeneralStatisticsCardCollapseTodayAverage;
