"use client";

import Skeleton from "@/components/atoms/skeleton";
import { twMerge } from "tailwind-merge";
import Calender from "@/assets/icons/calendarArrow.svg";
import { IBestYtmProps } from "../expand/type";
import BestYtmCardCollapse from "./BestYtmCardCollapse";

function BestYtmCollapse({ data, isPending, isError }: IBestYtmProps) {
  const categorySkeleton = Array.from({ length: 5 });

  return (
    <div className={twMerge("bg-cardBackground items-center gap-4 flex py-2 w-full rounded-lg pe-2 ps-4")}>
      <div className="flex gap-2">
        <Calender className="w-6 h-6" />
        <div className=" text-mainText font-bold min-w-[75px]">برترین YTM</div>
      </div>

      <div className="rounded-[4px] flex-row -mr-[3px] flex w-[calc(100%-75px)] overflow-hidden">
        {isError && categorySkeleton && categorySkeleton.map(() => <BestYtmCardCollapse />)}

        {isPending
          ? categorySkeleton && categorySkeleton.map(() => <Skeleton className="h-[40px] rounded-none" />)
          : data && data?.slice(0, 5).map((item, index) => <BestYtmCardCollapse item={item} index={index} />)}
      </div>
    </div>
  );
}

export default BestYtmCollapse;
