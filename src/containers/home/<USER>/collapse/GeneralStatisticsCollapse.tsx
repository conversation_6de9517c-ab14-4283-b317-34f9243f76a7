import Spinner from "@/assets/spinner.svg";
import { twMerge } from "tailwind-merge";
import GeneralStatisticsCardCollapseTodayAverage from "./GeneralStatisticsCardCollapseTodayAverage";
import GeneralStatisticsCardCollapseTradeValue from "./GeneralStatisticsCardCollapseTradeValue";
import { IGeneralStatisticsCollapseProps } from "./type";
import GeneralStatisticsCardCollapseDurationValue from "./GeneralStatisticsCardCollapseDurationValue";

function GeneralStatisticsCollapse({
  collapseClassName,
  tradeValues,
  todayAverage,
  tradeValuesLoading,
  averageLoading,
  activeBox,
  duraionLoading,
  durationValues
}: IGeneralStatisticsCollapseProps) {
  const tooltipContent = (
    <ul className="list-disc leading-normal pr-2">
      <li>در محاسبه میان گین ساده YTM اوراق تا سررسید زیر 6 ماه در نظر گرفته نشده اند</li>
      <li>تغییرات به واحد درصد</li>
    </ul>
  );

  return (
    <div className={twMerge("grid grid-cols-3 gap-2 mb-1.5 mt-px", collapseClassName)}>
      <div className="h-[72px]">
        {averageLoading ? (
          <div className="flex items-center justify-center h-full bg-[#3B324D] rounded-lg">
            <Spinner className="animate-spin h-6 w-6 " />
          </div>
        ) : (
          <GeneralStatisticsCardCollapseTodayAverage
            title="میانگین ساده YTM"
            className={twMerge("bg-[#3B324D] hover:outline-[#B4ABDA]", activeBox === "ytm" && "outline-[#B4ABDA]")}
            link="/ytmHistory"
            todayAverage={todayAverage}
            tooltipContent={tooltipContent}
          />
        )}
      </div>

      <div className="h-[72px]">
        {tradeValuesLoading ? (
          <div className="flex items-center justify-center h-full bg-cardDarkGreen rounded-lg">
            <Spinner className="animate-spin h-6 w-6 " />
          </div>
        ) : (
          <GeneralStatisticsCardCollapseTradeValue
            title="ارزش معاملات (امروز)"
            className={twMerge(
              "bg-cardDarkGreen hover:outline-Firefly400",
              activeBox === "market" && "outline-Firefly400"
            )}
            link="/marketValues"
            tradeValues={tradeValues}
          />
        )}
      </div>

      <div className="h-[72px]">
        {duraionLoading ? (
          <div className="flex items-center justify-center h-full bg-[#1F3253] rounded-lg">
            <Spinner className="animate-spin h-6 w-6 " />
          </div>
        ) : (
          <GeneralStatisticsCardCollapseDurationValue
            title="میانگین ساده دیرش (امروز)"
            className={twMerge("bg-[#1F3253] hover:outline-[#55C3FF]", activeBox === "duration" && "outline-[#55C3FF]")}
            link="/durationValues"
            durationValues={durationValues}
          />
        )}
      </div>
    </div>
  );
}

export default GeneralStatisticsCollapse;
