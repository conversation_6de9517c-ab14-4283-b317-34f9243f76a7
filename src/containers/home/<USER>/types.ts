export interface IBoundTableLimitOrder {
  ytm: number;
  buyAmount: number;
  buyPrice: number;
  buyTime: string;
  buyVolume: number;
  buyYtm: number;
  buyYtmInPercent: number;
  index: number;
  sellAmount: number;
  sellPrice: number;
  sellTime: string;
  sellVolume: number;
  sellYtm: number;
  sellYtmInPercent: number;
}

export interface IBondTableSocket {
  marketUpdateType: number;
  message: {
    bestBuyPrice: number;
    bestBuyPriceYtm: number;
    bestSellPrice: number;
    bestSellPriceYtm: number;
    bondTypeName: string;
    closePrice: number;
    closingPriceYtm: number;
    closePriceYtmYesterdayChange: number;
    faceValue: number;
    firstPaymentDate: string;
    instrumentName: string;
    isin: string;
    issuanceDate: string;
    lastTradePrice: number;
    lastTradePriceYtm: number;
    lastTradePriceYtmYesterdayChange: number;
    nominalInterestRate: number;
    paymentCount: number;
    publisher: string;
    symbol: string;
    totalSheetsAmount: number;
    lastTradeDate: string;
    lastTradeTime: string;
    totalTradedVolume: number;
    duration: number;
    modifiedDuration: number;
    convexity: number;
    dayHighPrice: number;
    dayLowPrice: number;
    totalNumberOfTrades: number;
    totalTradeValue: number;
    openPriceYtmInPercent: number;
    closePriceYtmInPercent: number;
    lastTradePriceYtmInPercent: number;
    bestBuyYtmInPercent: number;
    bestSellYtmInPercent: number;
    dayHighPriceYtmInPercent: number;
    dayLowPriceYtmInPercent: number;
    instrument: {
      isin: string;
      symbol: string;
      title: string;
    };
    limitOrders?: IBoundTableLimitOrder[];
  };
}

export type TBoundTableSocket = {} & Omit<IBondTableSocket["message"], "limitOrders"> & IBoundTableLimitOrder;
