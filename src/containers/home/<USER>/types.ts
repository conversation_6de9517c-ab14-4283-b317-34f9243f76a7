// // eslint-disable-next-line import/prefer-default-export

export type FormData = {
  buyingPrice?: number | null;
  fullDate: Date[];
  sellingPrice?: number | null;
  ytmInPercent?: number | string | null;
  isin: string;
};

export interface IResultProps {
  priceResult?: number;
  text?: string;
  ytm: boolean;
  isLoadingCalculateYtm?: boolean;
  isLoadingCalculateBuyPrice?: boolean;
  isLoadingCalculateSellPrice?: boolean;
  compoundInterestResult?: number;
  netPriceResult?: number;
  calculateBuyPrice?: boolean;
}

export interface IRadioButtonProps {
  radioState: string | number;
}

export interface ICalcFormProps {
  isShow: boolean;
  onClose: () => void;
}
