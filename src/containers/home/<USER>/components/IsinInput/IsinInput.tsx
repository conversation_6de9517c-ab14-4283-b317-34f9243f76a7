/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import SearchFilterIcon from "@/assets/icons/search.svg";
import Input from "@/components/atoms/input";
import { PopOver } from "@/components/atoms/popper";
import { Spinner } from "@/components/atoms/spinner";
import styles from "@/components/organisms/filter/Filter.module.scss";
import { useGetIsinByNameQuery } from "@/queries/calculatorAPI";
import { ISearchIsinByNameData } from "@/queries/calculatorAPI/types";
import { ConvertEnToFa } from "@/utils/helpers";
import { debounce } from "lodash";
import { useEffect, useMemo, useState } from "react";
import { twMerge } from "tailwind-merge";
import { IIsinInputProps } from "./type";

function IsinInput(props: IIsinInputProps) {
  const { onChange, value, preserveErrorMessage } = props;
  const [isOpen, setIsOpen] = useState(false);

  const [filterText, setFilterText] = useState<string | undefined>();
  const { mutateAsync: getIsins, data: IsinsData, isPending } = useGetIsinByNameQuery();

  useEffect(() => {
    setFilterText(value);
  }, [value]);

  const onChangeInp = useMemo(
    () =>
      debounce(e => {
        getIsins({ FilterText: ConvertEnToFa(e) })
          .then(() => {
            setIsOpen(true);
          })
          .catch(() => {
            setIsOpen(false);
          });
      }, 1000),
    []
  );

  useEffect(() => {
    if (!value) setFilterText("");
  }, [value]);

  const [title, setTitle] = useState("جستجو(نام نماد)");

  const content = (
    <div className="relative z-50 w-[316px]">
      <div
        className={twMerge(
          "w-[102%] -mr-[4px] -mt-[14px] rounded bg-backgroundBodyBackground py-2 h-[431px] overflow-auto",
          styles?.customScrollBar
        )}
      >
        <div className="flex flex-col text-mainText">
          {isPending && <Spinner />}
          {IsinsData?.data?.data?.length ? (
            IsinsData?.data?.data?.map((item: ISearchIsinByNameData) => (
              <div
                key={item?.isin}
                className="w-full cursor-pointer text-xs leading-4 p-2 pr-3 text-mainText hover:bg-cardDarkestBlue"
                onClick={() => {
                  setIsOpen(false);
                  setFilterText(item?.symbol);
                  onChange?.(item);
                }}
                data-test={`${item?.isin}-155a33e8-1c2d-4f61-bbf1-2684a82660be`}
              >
                {item?.symbol}
              </div>
            ))
          ) : (
            <div className="justify-center text-xs flex pt-2"> موردی یافت نشد</div>
          )}
        </div>
      </div>
    </div>
  );
  return (
    <div>
      <Input
        title={title}
        className="py-2"
        startAdornment={<SearchFilterIcon className="w-4 h-4" />}
        onChange={(e: any) => {
          if (!e) onChange?.();
          setFilterText(e);
          onChangeInp(e);
        }}
        value={filterText}
        preserveErrorMessage={preserveErrorMessage}
        data-test="155a33e8-1c2d-4f61-bbf1-2684a82660be"
        inputWrapperProps={{ labelClass: "text-xs", borderClass: "border-transparent rounded" }}
        wrapperClassName="bg-black1 rounded"
        inputSize="medium"
        onFocus={() => setTitle("")}
        onBlur={() => {
          if (filterText === "") {
            setTitle("جستجو(نام نماد)");
          }
        }}
      />

      <PopOver setIsOpen={setIsOpen} isOpen={isOpen} content={content} placement="bottom" />
    </div>
  );
}

export default IsinInput;
