import { commaSeparator } from "@/utils/helpers";
import React from "react";
import { IResultProps } from "./types";

function ResultCard({
  priceResult,
  text,
  ytm,
  compoundInterestResult,
  netPriceResult,
  calculateBuyPrice = false
}: IResultProps) {
  return (
    <div className="bg-cardDarkestBlue py-[7px] px-2 text-xs leading-4 rounded-[4px]  flex flex-col -mt-1.5 border border-semanticPrimary">
      {!ytm ? (
        <div className="flex justify-between text-mainText">
          <div>نرخ مطلوب YTM:</div>
          <div>{`%${priceResult}`}</div>
        </div>
      ) : (
        <div className="flex flex-col gap-1">
          <div className="text-mainText">{`  ${text}  :  ${priceResult && commaSeparator(priceResult, 0, false)}`}</div>
          {calculateBuyPrice && (
            <div className="flex flex-col gap-1">
              <div className="text-textUnchanged">{` بهای معامله هر برگه :  ${
                netPriceResult && commaSeparator(netPriceResult, 0, false)
              }`}</div>
              <div className="text-textUnchanged">{`سود روزشمار تا امروز :  ${
                compoundInterestResult && commaSeparator(compoundInterestResult, 0, false)
              }`}</div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

export default ResultCard;
