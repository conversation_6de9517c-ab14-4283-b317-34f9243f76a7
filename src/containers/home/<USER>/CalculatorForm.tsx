"use client";

/* eslint-disable react-hooks/exhaustive-deps */
import CalcIcon from "@/assets/icons/calculator-black.svg";
import CloseIcon from "@/assets/icons/close-skinny.svg";
import Button from "@/components/atoms/button";
import Input from "@/components/atoms/input";
import RadioGroup from "@/components/molecules/radioGroup/RadioGroup";
import { IRadioButtonItem } from "@/components/molecules/radioGroup/type";
import TwoInputsRangePicker from "@/components/organisms/datePicker/TwoInputsRangePicker";
import { IDatePickerWrapperRefProps } from "@/components/organisms/datePicker/types";
import {
  useGetIsinByNameQuery,
  usePostCalculateBuyPriceMutation,
  usePostCalculateSellPriceMutation,
  usePostCalculateYtmMutation
} from "@/queries/calculatorAPI";
import {
  ICalculateBuyPriceFormFields,
  ICalculateSellPriceFormFields,
  ICalculateYtmFormFields
} from "@/queries/calculatorAPI/types";
import useCalculatorStore from "@/store/CalendarStore";
import { dateConverter } from "@/utils/DateHelper";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect, useMemo, useRef, useState } from "react";
import Draggable from "react-draggable"; // The default
import { Controller, useForm } from "react-hook-form";
import { twMerge } from "tailwind-merge";
import ResultCard from "./ResultCard";
import IsinInput from "./components/IsinInput/IsinInput";
import { FormData, ICalcFormProps } from "./types";
import { CalculatorSchema, radioItems } from "./utils";

function CalculatorForm(props: ICalcFormProps) {
  const { isShow, onClose } = props;

  const {
    mutateAsync: postCalculateYtm,
    data: postCalculateYtmData,
    reset: resetCalculateYtm,
    error: errorCalculateYtm,
    isPending: isLoadingCalculateYtm
  } = usePostCalculateYtmMutation();
  const {
    mutateAsync: postCalculateBuyPrice,
    data: postCalculateBuyPriceInfo,
    reset: resetCalculateBuyPrice,
    error: errorCalculateBuyPrice,
    isPending: isLoadingCalculateBuyPrice
  } = usePostCalculateBuyPriceMutation();
  const {
    mutateAsync: postCalculateSellPrice,
    data: postCalculateSellPriceInfo,
    reset: resetCalculateSellPrice,
    error: errorCalculateSellPrice,
    isPending: isLoadingCalculateSellPrice
  } = usePostCalculateSellPriceMutation();

  const { isin: zusIsin, symbol: zusSymbol, setTickerValues } = useCalculatorStore();
  const [radioState, setRadioState] = useState<string | number>("YTMRadio");

  const switchRadio = (value: IRadioButtonItem) => {
    setRadioState(value.id);
  };

  const { mutateAsync: getIsins } = useGetIsinByNameQuery();
  const zodCalculatorSchema = useMemo(() => CalculatorSchema({ radioState }), [radioState]);

  const {
    handleSubmit,
    control,
    reset: resetForm,
    watch,
    setValue,
    getValues
  } = useForm<FormData>({
    resolver: zodResolver(zodCalculatorSchema)
  });

  const onSubmit = async (data: FormData) => {
    const newData = {
      buyingPrice: data.buyingPrice,
      sellingPrice: data.sellingPrice,
      ytmInPercent: data.ytmInPercent,
      isin: data.isin,
      buyingDate: data.fullDate?.[0]?.toISOString(),
      sellingDate: data.fullDate?.[1]?.toISOString()
    };

    if (radioState === "YTMRadio") {
      postCalculateYtm(newData as ICalculateYtmFormFields);
    } else if (radioState === "buyingPriceRadio") {
      postCalculateBuyPrice(newData as ICalculateBuyPriceFormFields);
    } else if (radioState === "sellingPriceRadio") {
      postCalculateSellPrice(newData as ICalculateSellPriceFormFields);
    }
  };

  const [maturityDateState, setMaturityDate] = useState("");
  const [BuyingDateState, setBuyingDate] = useState("");
  const [faceValue, setFaceValue] = useState<number>();
  const datePickerRef = useRef<IDatePickerWrapperRefProps>(null);

  const reset = (params: { isin?: string; canResetZustand?: boolean }) => {
    const { isin = "", canResetZustand = true } = params || {};

    resetForm({
      isin,
      fullDate: [],
      sellingPrice: undefined,
      buyingPrice: undefined,
      ytmInPercent: undefined
    });

    resetCalculateYtm();
    resetCalculateBuyPrice();
    resetCalculateSellPrice();
    setMaturityDate("");
    setBuyingDate("");
    setFaceValue(undefined);

    if (canResetZustand) {
      setTickerValues("", "");
    }
  };

  const setFormValues = (v: any) => {
    if (v?.maturityDate) {
      const today = new Date();
      const maturityDate = new Date(`${v.maturityDate}`);
      const issuanceDate = new Date(`${v.issuanceDate}`);

      setValue("fullDate", [today, maturityDate]);
      setMaturityDate(maturityDate.toISOString());
      setBuyingDate(issuanceDate.toISOString());
    }

    if (v?.faceValue) {
      setValue("sellingPrice", v?.faceValue);
      setFaceValue(v.faceValue);
    }
  };

  useEffect(() => {
    if (zusSymbol) {
      reset({ isin: zusIsin, canResetZustand: false });

      getIsins({ FilterText: zusSymbol }).then((res: any) => {
        const v = res.data.data?.find((item: { isin: string }) => item?.isin === getValues("isin"));
        setFormValues(v);
      });
    }
  }, [zusSymbol]);

  return (
    <Draggable handle=".handle" bounds="body">
      <form
        onSubmit={handleSubmit(onSubmit)}
        className={twMerge("absolute rounded-t-lg bottom-6 right-[52px] z-[99]", !isShow && "hidden")}
      >
        <div className="handle cursor-move flex justify-between max-h-10 bg-smokeWhite text-mediumBlack leading-4 border border-b-0 rounded-t-lg px-1 pb-2 pt-[7.5px]">
          <span className="flex gap-1 text-sm">
            <CalcIcon className="mt-0" />
            ماشین حساب
          </span>
          <button
            onClick={onClose}
            type="button"
            aria-label="close"
            className="w-[24px] h-[24px] border border-transparent flex items-center justify-center cursor-pointer"
            data-test="18c2f0b9-d5e5-4cc8-a0d3-f8c272a60cf8"
          >
            <CloseIcon />
          </button>
        </div>
        <div className="bg-cardBackground flex flex-col flex-1 overflow-auto px-[8px] py-[7px] relative h-[538px] border border-t-0 rounded-b-lg">
          <div className="flex-1 flex flex-col max-w-[320px]">
            <Controller
              name="isin"
              control={control}
              render={({ field }) => (
                <IsinInput
                  {...field}
                  value={zusSymbol}
                  preserveErrorMessage={false}
                  onChange={v => {
                    field.onChange(v?.isin);
                    setTickerValues(v ? v?.isin : "", v ? v?.symbol : "");
                    setFormValues(v);
                  }}
                />
              )}
            />
            <div className="flex mt-[5px] gap-2 pb-1">
              <Controller
                name="fullDate"
                control={control}
                render={({ field, fieldState: { error } }) => (
                  <TwoInputsRangePicker
                    // buyingDate
                    ref={datePickerRef}
                    id="fullDate"
                    config={{
                      minDate: new Date(BuyingDateState),
                      maxDate: new Date(maturityDateState), // sellingDate
                      yearRangeFrom: 1395,
                      yearRangeTo:
                        parseInt(dateConverter(new Date()).calendar("persian").locale("en-US").format("YYYY"), 10) + 10
                    }}
                    value={field.value}
                    onChange={v => {
                      field.onChange(
                        v?.map(d => {
                          // add 3 hours and 30 minutes to the date
                          const date = new Date(d);
                          date.setHours(date.getHours() + 3);
                          date.setMinutes(date.getMinutes() + 30);
                          return date;
                        })
                      );

                      if ((v[0], v[1])) {
                        datePickerRef.current?.close();
                      }
                    }}
                    labels={["تاریخ خرید", "تاریخ فروش"]}
                    input1props={{
                      className: "leading-10 h-10",
                      inputWrapperProps: { labelClass: "text-mediumBlue" },
                      inputSize: "small",
                      errorMessage: error?.message
                    }}
                    input2props={{
                      className: "leading-10 h-10",
                      inputWrapperProps: { labelClass: "text-mediumBlue" },
                      inputSize: "small"
                    }}
                    disabled={!watch("isin")}
                    rootClassName="gap-2"
                  />
                )}
              />
            </div>

            <div className="flex-col flex gap-[9px] pb-2">
              <div className="text-xs leading-4 text-disable">محاسبه:</div>
              <RadioGroup
                items={radioItems}
                className="flex gap-1 -mt-[1px]"
                onSwitch={v => switchRadio(v)}
                disabled={!watch("isin")}
                size="small"
              />
            </div>

            {/* -------------------------------------------------------------------------- */
            /*                                 بر اساس YTM                                */
            /* -------------------------------------------------------------------------- */}
            {radioState === "YTMRadio" && (
              <div className="flex flex-col justify-between flex-1 mt-2">
                <div className="flex-1 flex flex-col gap-2 ">
                  <Controller
                    name="buyingPrice"
                    control={control}
                    render={({ field: { onChange, value }, fieldState: { error } }) => (
                      <Input<number>
                        className="leading-10 h-10"
                        inputWrapperProps={{ labelClass: "text-mediumBlue" }}
                        title="قیمت خرید"
                        onChange={e => onChange(Number(e) || null)}
                        value={value ?? undefined}
                        min={0}
                        max={20000000}
                        type="number"
                        errorMessage={error?.message}
                        isDisabled={!watch("isin")}
                        defaultValue={undefined}
                        preserveErrorMessage={false}
                        inputSize="medium"
                        data-test="a954be3b-3af5-4d1e-bbe4-5f2a3012dae7"
                      />
                    )}
                  />

                  <Controller
                    name="sellingPrice"
                    control={control}
                    render={({ field: { onChange, value }, fieldState: { error } }) => (
                      <Input<number>
                        className="leading-10 h-10"
                        inputWrapperProps={{ labelClass: "text-mediumBlue" }}
                        title="قیمت فروش"
                        type="number"
                        onChange={e => {
                          onChange(Number(e) || null);
                        }}
                        value={value ?? undefined}
                        min={0}
                        max={20000000}
                        errorMessage={error?.message}
                        isDisabled={!watch("isin")}
                        defaultValue={faceValue}
                        preserveErrorMessage={false}
                        inputSize="medium"
                        data-test="278812ba-23b0-4698-b738-87a9891cabf8"
                      />
                    )}
                  />
                </div>
                {postCalculateYtmData && (
                  <div className="mt-auto shrink-0">
                    <ResultCard
                      priceResult={postCalculateYtmData?.data?.data?.ytmInPercent}
                      ytm={false}
                      isLoadingCalculateYtm={isLoadingCalculateYtm}
                    />
                  </div>
                )}

                {errorCalculateYtm && (
                  <div className="bg-darkRed border-1 border-mediumRed py-[11px] px-2 rounded-[4px] text-xs">
                    {errorCalculateYtm?.response?.data?.errorMessage}
                    مقادیر وارد شده را بررسی کنید
                  </div>
                )}
              </div>
            )}

            {/* -------------------------------------------------------------------------- */
            /*                                  قیمت خرید                                 */
            /* -------------------------------------------------------------------------- */}
            {radioState === "buyingPriceRadio" && (
              <div className="flex flex-col justify-between flex-1">
                <div className="flex-1 flex flex-col">
                  <Controller
                    name="sellingPrice"
                    control={control}
                    render={({ field: { onChange, value }, fieldState: { error } }) => (
                      <Input<number>
                        inputWrapperProps={{ labelClass: "text-mediumBlue" }}
                        title="قیمت فروش"
                        type="number"
                        onChange={e => onChange(Number(e) || null)}
                        value={value ?? undefined}
                        min={0}
                        max={20000000}
                        errorMessage={error?.message}
                        isDisabled={!watch("isin")}
                        preserveErrorMessage={false}
                        inputSize="medium"
                        data-test="86f67ba1-8bac-4eb0-b086-7232d50d39af"
                      />
                    )}
                  />
                  <Controller
                    name="ytmInPercent"
                    control={control}
                    render={({ field: { onChange, value }, fieldState: { error } }) => (
                      <Input<number>
                        title="نرخ مطلوب(YTM)"
                        onChange={e => onChange(e || null)}
                        value={value ?? undefined}
                        type="number"
                        min={0}
                        max={100}
                        errorMessage={error?.message}
                        isDisabled={!watch("isin")}
                        preserveErrorMessage={false}
                        inputSize="medium"
                        data-test="bdcdc974-481f-4016-b25a-4705fa9144e0"
                      />
                    )}
                  />
                </div>

                {postCalculateBuyPriceInfo && postCalculateBuyPriceInfo?.data && (
                  <ResultCard
                    priceResult={postCalculateBuyPriceInfo?.data?.data?.price}
                    text="قیمت خرید هر برگه"
                    ytm
                    isLoadingCalculateBuyPrice={isLoadingCalculateBuyPrice}
                    calculateBuyPrice
                    compoundInterestResult={postCalculateBuyPriceInfo?.data?.data?.compoundInterest}
                    netPriceResult={postCalculateBuyPriceInfo?.data?.data?.netPrice}
                  />
                )}

                {errorCalculateBuyPrice && (
                  <div className="bg-darkRed border-1 border-mediumRed mt-auto py-3 px-2 rounded-[4px] text-xs">
                    {errorCalculateBuyPrice?.response?.data?.errorMessage}
                  </div>
                )}
              </div>
            )}

            {/* -------------------------------------------------------------------------- */
            /*                                  قیمت فروش                                 */
            /* -------------------------------------------------------------------------- */}
            {radioState === "sellingPriceRadio" && (
              <div className="flex flex-col justify-between flex-1">
                <div className="flex-1 flex flex-col">
                  <Controller
                    name="buyingPrice"
                    control={control}
                    render={({ field: { onChange, value }, fieldState: { error } }) => (
                      <Input<number>
                        title="قیمت خرید"
                        onChange={e => onChange(Number(e) || null)}
                        type="number"
                        value={value ?? undefined}
                        min={0}
                        max={20000000}
                        errorMessage={error?.message}
                        isDisabled={!watch("isin")}
                        preserveErrorMessage={false}
                        inputSize="medium"
                        data-test="8ccb144b-1987-41cf-8ca6-a01a1df379c9"
                      />
                    )}
                  />
                  <Controller
                    name="ytmInPercent"
                    control={control}
                    render={({ field: { onChange, value }, fieldState: { error } }) => (
                      <Input
                        title="نرخ مطلوب(YTM)"
                        onChange={e => onChange(e || null)}
                        value={value ?? undefined}
                        errorMessage={error?.message}
                        isDisabled={!watch("isin")}
                        preserveErrorMessage={false}
                        inputSize="medium"
                        type="number"
                        min={0}
                        max={100}
                        data-test="778a48f7-9231-45b9-a405-6e7aa0826109"
                      />
                    )}
                  />
                </div>

                {postCalculateSellPriceInfo && (
                  <ResultCard
                    priceResult={postCalculateSellPriceInfo?.data?.data?.price}
                    text="قیمت فروش هر برگه"
                    ytm
                    isLoadingCalculateSellPrice={isLoadingCalculateSellPrice}
                  />
                )}
                {errorCalculateSellPrice && (
                  <div className="bg-darkRed border-1 border-mediumRed py-3 px-2 rounded-[4px] text-xs">
                    {errorCalculateSellPrice?.response?.data?.errorMessage}
                  </div>
                )}
              </div>
            )}
          </div>
          <div className="flex justify-center gap-2 pt-2 mt-auto shrink-0">
            <Button
              variant="outLine"
              type="button"
              size="medium"
              className="grow"
              onClick={() => reset({})}
              disabled={!watch("isin")}
              data-test="d0bca983-b85c-4908-9c22-356739ea4947"
            >
              پاک کردن
            </Button>

            <Button
              isLoading={isLoadingCalculateYtm || isLoadingCalculateBuyPrice || isLoadingCalculateSellPrice}
              type="submit"
              size="medium"
              className="grow"
              disabled={
                !watch("isin") || isLoadingCalculateYtm || isLoadingCalculateBuyPrice || isLoadingCalculateSellPrice
              }
              data-test="1d961cf5-96d9-424f-8089-204827bf3f08"
            >
              محاسبه
            </Button>
          </div>
        </div>
      </form>
    </Draggable>
  );
}

export default CalculatorForm;
