import { IGetTopYtmItem } from "@/queries/topYtmAPI/type";
import { commaSeparator } from "@/utils/helpers";
import React from "react";
import { twMerge } from "tailwind-merge";

function BestYtmCard({ item }: IGetTopYtmItem) {
  return (
    <div className="bg-backgroundLightRow rounded-lg py-2 px-3">
      <div className="text-mainText text-sm  leading-[22px] font-bold">{item?.symbol ? item?.symbol : "---"}</div>

      <div className="flex items-end border-borderBorderAndDivider border-b-[0.5px] pt-1 pb-3px">
        <div className="flex flex-col">
          <div className="text-secondaryText text-[10px] leading-4 ">بهترین مظنه فروش</div>
          <div className="text-mainText text-xs -mt-[1.5px]">
            {item?.bestSell ? commaSeparator(item?.bestSell) : "---"}
          </div>
        </div>
        <div className="text-mainText mr-auto text-base pb-3px">
          {item?.bestSellYtmToPercent ? `%${(item?.bestSellYtmToPercent ?? 0).toFixed(3)}` : "---"}
        </div>
      </div>

      <div className="flex items-end border-borderBorderAndDivider border-b-[0.5px]  pt-1 pb-3px">
        <div className="flex flex-col">
          <div className="text-secondaryText text-[10px] leading-4 ">آخرین قیمت</div>
          <div className="text-mainText leading-4 text-xs -mt-[1.5px]">
            {item?.lastTradePrice ? commaSeparator(item?.lastTradePrice) : "---"}
          </div>
        </div>
        <div
          className={twMerge(
            "text-textDescending mr-auto pb-3px",
            item?.lastTradePriceYtmChangeRate === -1 && "text-textDescending",
            item?.lastTradePriceYtmChangeRate === 1 && "text-textAscending",
            item?.lastTradePriceYtmChangeRate === 0 && "text-mainText"
          )}
        >
          {item?.lastTradePriceYtmToPercent ? (
            `%${(item?.lastTradePriceYtmToPercent ?? 0).toFixed(3)}`
          ) : (
            <div className="text-mainText">---</div>
          )}
        </div>
      </div>

      <div className="flex items-end border-borderBorderAndDivider border-b-[0.5px]  pt-1 pb-3px">
        <div className="flex flex-col">
          <div className="text-secondaryText text-[10px] leading-4 ">قیمت پایانی</div>
          <div className="text-mainText leading-4 text-xs -mt-[1.5px]">
            {item?.closePrice ? commaSeparator(item?.closePrice) : "---"}
          </div>
        </div>
        <div
          className={twMerge(
            "text-textDescending mr-auto pb-3px ltr",
            item?.closePriceYtmChangeRate === 1 && "text-textAscending",
            item?.closePriceYtmChangeRate === -1 && "text-textDescending",
            item?.closePriceYtmChangeRate === 0 && "text-mainText"
          )}
        >
          {item?.closePriceYtmToPercent ? (
            `%${(item?.closePriceYtmToPercent ?? 0).toFixed(3)}`
          ) : (
            <div className="text-mainText ">---</div>
          )}
        </div>
      </div>

      <div className="flex items-end py-1 ">
        <div className="flex flex-col">
          <div className="text-secondaryText text-[10px] leading-4 ">بهترین مظنه خرید</div>
          <div className="text-mainText leading-4 text-xs -mt-[1.5px]">
            {item?.bestBuy ? commaSeparator(item?.bestBuy) : "---"}
          </div>
        </div>
        <div className="text-mainText mr-auto text-base pb-3px">
          {item?.bestBuyYtmToPercent ? `%${(item?.bestBuyYtmToPercent ?? 0).toFixed(3)}` : "---"}
        </div>
      </div>
    </div>
  );
}

export default BestYtmCard;
