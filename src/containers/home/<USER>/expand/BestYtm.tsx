import Background from "@/assets/icons/background.svg";
import Calender from "@/assets/icons/calendarArrow.svg";
import Skeleton from "@/components/atoms/skeleton";
import BestYtmCard from "./BestYtmCard";
import { IBestYtmProps } from "./type";

function BestYtm({ data, isPending, isError }: IBestYtmProps) {
  const categorySkeleton = Array.from({ length: 5 });

  return (
    <div className="flex items-center gap-2 bg-cardBackground  py-2 pe-2 rounded-md relative">
      <Background className="absolute right-0" />
      <div className="flex flex-col items-center gap-3 px-[27px] z-10">
        <Calender className="w-6 h-6" />
        <div className="font-bold text-mainText min-w-[72px]">برترین YTM</div>
      </div>
      <div className="w-full gap-2 grid grid-cols-5 z-10 justify-between">
        {isError && categorySkeleton && categorySkeleton.map(() => <BestYtmCard />)}
        {isPending
          ? categorySkeleton && categorySkeleton.map(() => <Skeleton className="h-[200px]" />)
          : data && data?.slice(0, 5).map(item => <BestYtmCard item={item} />)}
      </div>
    </div>
  );
}

export default BestYtm;
