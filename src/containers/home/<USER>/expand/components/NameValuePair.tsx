import { twMerge } from "tailwind-merge";
import Up from "@/assets/icons/arrow-up.svg";
import { memo } from "react";
import { useServerTime } from "@/components/organisms/Header/components/utils";
import { ShowTradeValuePercent } from "@/containers/home/<USER>/utils";
import { NameValuePairProps } from "./type";

function NameValuePair({ label, value, change, className, withTimeCheck, hasCoupon }: NameValuePairProps) {
  const { time } = useServerTime();
  const ShowTradeValuePercentIncludeTime = withTimeCheck ? time && ShowTradeValuePercent(time) : true;

  return (
    <div
      className={twMerge(
        "flex justify-between mx-4 text-mainText py-1.5 border-b border-b-[#676767]",
        !hasCoupon && "border-b-transparent pt-3",
        className
      )}
    >
      <div
        className={twMerge("text-sm font-normal whitespace-nowrap", hasCoupon ? "text-[#26D5C0]" : "text-[#C7DA41]")}
      >
        {label}
      </div>

      <div className="flex items-center gap-3">
        {change && ShowTradeValuePercentIncludeTime ? (
          <div
            className={twMerge(
              "px-2 py-1 rounded bg-[rgba(31,31,34,0.6)] flex items-center gap-1 h-6",
              Number(change) > 0 ? "text-textAscending" : "",
              Number(change) < 0 ? "text-textDescending" : ""
            )}
          >
            <div
              className={twMerge(
                "text-sm",

                Number(change) > 0 ? "text-textAscending" : "",
                Number(change) < 0 ? "text-textDescending" : ""
              )}
            >
              <span className="text-sm">{`${Math.abs(Number(change)) || "---"}`}</span>
              <span className="text-sm">{Math.abs(Number(change)) > 0 && "٪"}</span>
            </div>

            {change && ShowTradeValuePercentIncludeTime && Number(change) > 0 && (
              <Up className="text-textAscending inline h-3 w-3" />
            )}
            {change && ShowTradeValuePercentIncludeTime && Number(change) < 0 && (
              <>
                {Number(change) < 0 ? "-" : ""}
                <Up className="text-textDescending rotate-180 inline h-3 w-3" />
              </>
            )}
          </div>
        ) : (
          <div className="h-6" />
        )}
        <span className="text-xl ltr font-bold text-white whitespace-nowrap">{value}</span>
      </div>

      {/* <div className="text-end">
        <div className="text-xl font-normal ltr text-start leading-[31px] relative top-0.5">{value}</div>
        {change && ShowTradeValuePercentIncludeTime ? (
          <div
            className={twMerge(
              "text-sm font-bold leading-6",
              Number(change) > 0 ? "text-textAscending" : "",
              Number(change) < 0 ? "text-textDescending" : ""
            )}
          >
            <span className="ltr">
              <span>{`${Math.abs(Number(change)) || "---"}`}</span>
              <span>{Math.abs(Number(change)) > 0 && "٪"}</span>
            </span>

            {change && ShowTradeValuePercentIncludeTime && Number(change) > 0 && (
              <Up className="text-textAscending inline mr-1 h-3 w-3" />
            )}
            {change && ShowTradeValuePercentIncludeTime && Number(change) < 0 && (
              <>
                {Number(change) < 0 ? "-" : ""}
                <Up className="text-textDescending rotate-180 inline mr-1 h-3 w-3" />
              </>
            )}
          </div>
        ) : (
          <div className="h-6" />
        )}
      </div> */}
    </div>
  );
}

export default memo(NameValuePair);
