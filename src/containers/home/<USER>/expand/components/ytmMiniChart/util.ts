import { convertIntToCurrency } from "@/utils/helpers";
import { clone } from "lodash";
import Highcharts from "highcharts/highstock";

const lastPoint = {
  marker: {
    symbol: "circle",
    radius: 5
  }
};

if (Highcharts && Highcharts.setOptions) {
  Highcharts.setOptions({
    plotOptions: {
      series: {
        animation: false
      }
    }
  });
}

const getPriceChartOptions = ({
  couponsData,
  noCouponsData
}: {
  couponsData: any;
  noCouponsData: any;
  hasCoupons: boolean;
}) => {
  const modifiedCouponData = clone(couponsData);
  const modifiedZeroCouponData = clone(noCouponsData);
  if (modifiedCouponData?.length) {
    modifiedCouponData[modifiedCouponData.length - 1] = {
      ...lastPoint,
      x: couponsData.length - 1,
      y: couponsData?.[couponsData.length - 1]?.[1],
      name: couponsData?.[couponsData.length - 1]?.[0]
    };
  }

  if (modifiedZeroCouponData?.length) {
    modifiedZeroCouponData[modifiedZeroCouponData.length - 1] = {
      ...lastPoint,
      x: noCouponsData.length - 1,
      y: noCouponsData?.[noCouponsData.length - 1]?.[1],
      name: noCouponsData?.[noCouponsData.length - 1]?.[0]
    };
  }
  return {
    chart: {
      backgroundColor: "transparent",

      height: 136, // Adjusted to match parent height exactly
      marginTop: 10, // Reduced to use more vertical space
      marginBottom: 10, // Reduced to use more vertical space
      marginLeft: 5, // Reduced to use more horizontal space
      marginRight: 5, // Reduced since legend is disabled
      padding: 0,
      spacingTop: 0,
      spacingBottom: 0,
      spacingLeft: 0,
      spacingRight: 0
    },
    title: {
      text: ""
    },
    subtitle: {
      text: ""
    },
    xAxis: {
      type: "category",
      min: 0.5,
      lineWidth: 0,
      gridLineWidth: 0,
      labels: {
        style: {
          display: "none",
          color: "#F4F4F4"
        },
        /* @ts-ignore */
        formatter() {
          // @ts-ignore
          return this?.value;
        }
      }
    },
    yAxis: {
      title: false,
      gridLineColor: "#1F3253",
      gridLineWidth: 1,
      lineWidth: 0,
      labels: {
        style: {
          color: "#F4F4F4",
          display: "none"
        },
        /* @ts-ignore */
        formatter() {
          // @ts-ignore
          const value = this?.value;
          return convertIntToCurrency(value, 0)!.value + convertIntToCurrency(value, 0)!.unit;
        }
      }
    },
    tooltip: { enabled: false },
    series: [
      {
        type: "line",
        name: "کوپن دار",
        data: modifiedCouponData,
        color: "#26D5C0"
      },
      {
        type: "line",
        name: "بدون کوپن",
        data: modifiedZeroCouponData,
        color: "#BCD526"
      }
    ],
    plotOptions: {
      series: {
        lineWidth: 2,
        marker: { radius: 0 },
        enableMouseTracking: false,
        shadow: false,
        states: {
          hover: {
            enabled: false
          }
        },
        threshold: false
      }
    },
    credits: {
      enabled: false
    },
    legend: {
      enabled: false,
      align: "right",
      verticalAlign: "top",
      layout: "vertical",
      x: 21,
      y: -10,
      symbolPadding: 0,
      symbolHeight: 0.1,
      symbolRadius: 0,
      useHTML: true,
      symbolWidth: 0,
      labelFormatter() {
        const item = this as any;
        return `<div> <span style="font-size: 10px;font-weight: 400;color: ${
          item.visible ? "#F4F4F4" : "#b5b5b3"
        };text-decoration: unset; padding-right: 2px">${item?.name}</span> <span style="width: 8px; height: 8px; border-radius: 100%;background-color: ${
          item?.visible ? item.color : "gray"
        };display: inline-block; position: relative; top: 2px"></span></div>`;
      },
      itemStyle: {
        fontSize: 10,
        fontWeight: 400,
        color: "#F4F4F4"
      },
      itemMarginBottom: 8,
      squareSymbol: false,
      rtl: true
    }
  };
};

export default getPriceChartOptions;
