"use client";

import { useMemo, useRef } from "react";
import { twMerge } from "tailwind-merge";
import HighChart from "@/components/molecules/highChart/HighChart";
import HighchartsReact from "highcharts-react-official";
import getYtmTotalChartChartOptions from "./util";
import Styles from "./ytmMiniChart.module.scss";
import { YtmMiniChartProps } from "./type";

function YtmMiniChart({ chartData1, chartData2 }: YtmMiniChartProps) {
  const chartRef = useRef<HighchartsReact.RefObject>(null);
  const options = useMemo(
    () => getYtmTotalChartChartOptions({ couponsData: chartData1, noCouponsData: chartData2, hasCoupons: false }),
    [chartData1, chartData2]
  );

  return (
    <div className={twMerge("m-auto min-h-36", Styles.ytmChartWrapper)}>
      <HighChart containerProps={{ className: "w-full h-full" }} options={options} refChart={chartRef} />
    </div>
  );
}

export default YtmMiniChart;
