"use client";

import { twMerge } from "tailwind-merge";
import Spinner from "@/assets/spinner.svg";
import { IChartSectionProps } from "./type";
import YtmMiniChart from "./ytmMiniChart/YtmMiniChart";

function ChartSection({ className, chartData1, chartData2, loading }: IChartSectionProps) {
  return (
    <div className={twMerge("px-3 rounded w-[142px] m-2 ml-0", className)}>
      {loading ? (
        <div className="flex items-center justify-center h-[152px]">
          <Spinner className="animate-spin p-0.5 h-8 w-8" />
        </div>
      ) : (
        <YtmMiniChart chartData1={chartData1} chartData2={chartData2} />
      )}

      {/* {!loading && (
        <div className="absolute">
          <Link href={link}>
            <Tooltip content="مشاهده چارت" placement="top-end" className="w-[90px]">
              <div
                className="relative bottom-8 pr-0.5 group"
                data-test={`btn-9aad73bd-f22a-46e9-8f2d-b63d7666b353-${link}`}
              >
                <Maximize className="w-5 h-5 group-hover:hidden" />
                <MaximizeFill className="w-5 h-5 hidden group-hover:block" />
              </div>
            </Tooltip>
          </Link>
        </div>
      )} */}
    </div>
  );
}

export default ChartSection;
