"use client";

import { NameValuePair } from "@/containers/home/<USER>/expand/components";
import { IGeneralStatisticsProps, XYType } from "@/containers/home/<USER>/expand/type";
import {
  useGetDashboardTotalValueChartQuery,
  useGetLastYearMonthlyYtmAverageQuery,
  useGetTotalChartValueDurationQuery
} from "@/queries/homeCards";
import { commaSeparator } from "@/utils/helpers";
import MiniChartInfoCard from "./MiniChartInfoCard";

function GeneralStatistics({
  duraionLoading,
  tradeValuesLoading,
  todayAverage,
  averageLoading,
  tradeValues,
  durationValues
}: IGeneralStatisticsProps) {
  const shortYear = (date: string) => (date.length > 2 ? date.substring(2) : date);

  const { data: lastYearAverage, isLoading: averageChartLoading } = useGetLastYearMonthlyYtmAverageQuery();
  const { data: TotalValueChart, isLoading: totalValueChartLoading } = useGetDashboardTotalValueChartQuery();
  const { data: TotalChartValueDuration, isLoading: totalChartValueDurationLoading } =
    useGetTotalChartValueDurationQuery();

  const averageChartWithCoupon = lastYearAverage?.data?.map(({ persianMonth, couponAverageYtm }) => [
    shortYear(persianMonth),
    couponAverageYtm * 100
  ]) as XYType;

  const averageChartWithOutCoupon = lastYearAverage?.data?.map(({ persianMonth, zeroCouponAverageYtm }) => [
    shortYear(persianMonth),
    zeroCouponAverageYtm * 100
  ]) as XYType;

  const valueChartWithCoupon = TotalValueChart?.data?.map(({ persianMonth, totalCouponTradeValue }) => [
    shortYear(persianMonth),
    totalCouponTradeValue
  ]) as XYType;

  const valueChartWithOutCoupon = TotalValueChart?.data?.map(({ persianMonth, totalZeroCouponTradeValue }) => [
    shortYear(persianMonth),
    totalZeroCouponTradeValue
  ]) as XYType;

  const valueChartDurationWithCoupon = TotalChartValueDuration?.data?.map(({ persianMonth, couponAverageDuration }) => [
    shortYear(persianMonth),
    couponAverageDuration
  ]) as XYType;

  const valueChartDurationWithOutCoupon = TotalChartValueDuration?.data?.map(
    ({ persianMonth, zeroCouponAverageDuration }) => [shortYear(persianMonth), zeroCouponAverageDuration]
  ) as XYType;

  const tooltipContent = (
    <ul className="list-disc leading-normal pr-2">
      <li>در محاسبه میان گین ساده YTM اوراق تا سررسید زیر 6 ماه در نظر گرفته نشده اند</li>
      <li>تغییرات به واحد درصد</li>
    </ul>
  );

  return (
    <div className="grid lg:grid-cols-3 gap-2 pb-2">
      <MiniChartInfoCard
        loading={averageLoading || averageChartLoading}
        title="میانگین  ساده YTM"
        link="/ytmHistory"
        chartSectionClassName="bg-[#2F283E]"
        infoSectionClassName="bg-[#3B324D] hover:border-[#B4ABDA]"
        chartData1={averageChartWithCoupon}
        chartData2={averageChartWithOutCoupon}
        tooltipContent={tooltipContent}
        isPercentage
      >
        <>
          <NameValuePair
            label="کوپن دار"
            hasCoupon
            className="mt-8"
            value={
              todayAverage?.data?.couponBondTodayAverageYtmInPercent &&
              todayAverage?.data?.couponBondTodayAverageYtmInPercent !== 0 ? (
                <>
                  {Number(todayAverage?.data?.couponBondTodayAverageYtmInPercent).toFixed(3)}
                  <span className="text-base">%</span>
                </>
              ) : (
                "---"
              )
            }
            change={todayAverage?.data?.couponBondAverageYtmChangeInPercent?.toFixed(3)}
          />
          <NameValuePair
            label="بدون کوپن"
            value={
              todayAverage?.data?.zeroCouponBondTodayAverageYtmInPercent &&
              todayAverage?.data?.zeroCouponBondTodayAverageYtmInPercent !== 0 ? (
                <>
                  {Number(todayAverage?.data?.zeroCouponBondTodayAverageYtmInPercent).toFixed(3)}
                  <span className="text-base">%</span>
                </>
              ) : (
                "---"
              )
            }
            change={todayAverage?.data?.zeroCouponBondAverageYtmChangeInPercent?.toFixed(3)}
          />
        </>
      </MiniChartInfoCard>

      <MiniChartInfoCard
        loading={totalValueChartLoading || tradeValuesLoading}
        title="ارزش معاملات (امروز)"
        link="/marketValues"
        chartData1={valueChartWithCoupon}
        chartData2={valueChartWithOutCoupon}
        chartSectionClassName="bg-cardDarkestGreen"
        infoSectionClassName="bg-cardDarkGreen hover:border-[#5EA3A0]"
      >
        <>
          <NameValuePair
            label="کوپن دار"
            withTimeCheck
            hasCoupon
            className="mt-8"
            value={
              // eslint-disable-next-line no-nested-ternary
              Number(tradeValues?.data?.couponTotalValue) > 1000000000 ? (
                <span>
                  {commaSeparator(Number(Number(tradeValues?.data?.couponTotalValue) / 1000000000), 2, false, 5)}{" "}
                  <span className="pl-px text-base relative top-px">B</span>
                </span>
              ) : tradeValues?.data?.couponTotalValue ? (
                commaSeparator(tradeValues?.data?.couponTotalValue, 2, false, 5)
              ) : (
                "---"
              )
            }
            change={tradeValues?.data?.couponTotalValueChangePercent?.toFixed(3)}
          />
          <NameValuePair
            label="بدون کوپن"
            withTimeCheck
            value={
              // eslint-disable-next-line no-nested-ternary
              Number(tradeValues?.data?.zeroCouponTotalValue) > 1000000000 ? (
                <span>
                  {commaSeparator(
                    (Number(tradeValues?.data?.zeroCouponTotalValue) / 1000000000)?.toFixed(2),
                    2,
                    false,
                    5
                  )}{" "}
                  <span className="pl-px text-base relative top-px">B</span>
                </span>
              ) : tradeValues?.data?.zeroCouponTotalValue ? (
                commaSeparator(tradeValues?.data?.zeroCouponTotalValue, 2, false, 5)
              ) : (
                "---"
              )
            }
            change={tradeValues?.data?.zeroCouponTotalValueChangePercent?.toFixed(3)}
          />
        </>
      </MiniChartInfoCard>

      <MiniChartInfoCard
        loading={totalChartValueDurationLoading || duraionLoading}
        title="میانگین ساده دیرش (امروز)"
        link="/durationValues"
        chartData1={valueChartDurationWithCoupon}
        chartData2={valueChartDurationWithOutCoupon}
        chartSectionClassName="bg-cardDarkestBlue"
        infoSectionClassName="bg-cardDarkBlue hover:border-[#55C3FF]"
      >
        <>
          <NameValuePair
            label="کوپن دار"
            className="mt-8"
            hasCoupon
            // withTimeCheck
            value={
              // eslint-disable-next-line no-nested-ternary
              Number(durationValues?.data?.couponBondTodayAverageDuration) > 1000000000 ? (
                <span>
                  {commaSeparator(
                    Number(durationValues?.data?.couponBondTodayAverageDuration) / 1000000000,
                    2,
                    false,
                    5
                  )}{" "}
                  <span className="pl-px text-base relative top-px">B</span>
                </span>
              ) : durationValues?.data?.couponBondTodayAverageDuration ? (
                commaSeparator(durationValues?.data?.couponBondTodayAverageDuration, 2, false, 5)
              ) : (
                "---"
              )
            }
            change={durationValues?.data?.couponBondAverageDurationChangeInPercent?.toFixed(3)}
          />
          <NameValuePair
            label="بدون کوپن"
            // withTimeCheck
            value={
              // eslint-disable-next-line no-nested-ternary
              Number(durationValues?.data?.zeroCouponBondTodayAverageDuration) > 1000000000 ? (
                <span>
                  {commaSeparator(
                    (Number(durationValues?.data?.zeroCouponBondTodayAverageDuration) / 1000000000)?.toFixed(2),
                    2,
                    false,
                    5
                  )}{" "}
                  <span className="pl-px text-base relative top-px">B</span>
                </span>
              ) : durationValues?.data?.zeroCouponBondTodayAverageDuration ? (
                commaSeparator(durationValues?.data?.zeroCouponBondTodayAverageDuration, 2, false, 5)
              ) : (
                "---"
              )
            }
            change={durationValues?.data?.zeroCouponBondAverageDurationChangeInPercent?.toFixed(3)}
          />
        </>
      </MiniChartInfoCard>
    </div>
  );
}

export default GeneralStatistics;
