import useDelayedStack from "@reactutils/use-delayed-stack";
import socketUrls from "@/constants/socketUrls";
import useSignalR from "@/hooks/useSignalR/useSignalR";
import {
  TTodayAverage,
  TTodayAverageResponse,
  TTotalValueData,
  TTotalValueDataResponse,
  TTotalValueDurationData,
  TTotalValueDurationResponse
} from "@/queries/homeCards/types";
import { useSocketInitialData } from "@/hooks/useSocketInitialData";
import React from "react";
import GeneralStatisticsCollapse from "./collapse";
import GeneralStatistics from "./expand";
import { IGeneralStatisticsContainerProps } from "./type";

function GeneralStatisticsContainer({ isCollapsed, activeBox }: IGeneralStatisticsContainerProps) {
  const {
    data: todayAverage,
    isLoading: averageLoading,
    setData: setTodayAverage
  } = useSocketInitialData<TTodayAverageResponse>({
    url: socketUrls.totalMarketYtmUrl,
    streamName: socketUrls.TodayAverageYtmLastState
  });

  const {
    data: tradeValues,
    isLoading: tradeValuesLoading,
    setData
  } = useSocketInitialData<TTotalValueDataResponse>({
    url: socketUrls.totalMarketValueUrl,
    streamName: socketUrls.TotalValueLastState
  });

  const {
    data: durationValues,
    isLoading: durationLoading,
    setData: setDurationData
  } = useSocketInitialData<TTotalValueDurationResponse>({
    url: socketUrls.durationValueValueUrl,
    streamName: socketUrls.durationValueValueInvokeStream
  });

  function updateReactQueryTradeValueData(socketData: TTotalValueData) {
    const updatedData = { ...tradeValues?.data, ...socketData };

    setData({ data: updatedData } as TTotalValueDataResponse);
  }

  const [pushToStackMarketValue] = useDelayedStack((data: TTotalValueData[]) => {
    updateReactQueryTradeValueData(data[data.length - 1]);
  }, 300);

  useSignalR(socketUrls.totalMarketValueUrl, socketUrls.totalMarketValueStreamName, {
    next: (item: TTotalValueData) => {
      pushToStackMarketValue(item);
    },
    error: () => {}
  });

  function updateReactQueryDurationValueData(socketData: TTotalValueDurationData) {
    const updatedData = { ...durationValues?.data, ...socketData };

    setDurationData({ data: updatedData } as TTotalValueDurationResponse);
  }

  const [pushToStackDurationValue] = useDelayedStack((data: TTotalValueDurationData[]) => {
    updateReactQueryDurationValueData(data[data.length - 1]);
  }, 300);

  useSignalR(socketUrls.durationValueValueUrl, socketUrls.durationValueValueStream, {
    next: (item: TTotalValueDurationData) => {
      pushToStackDurationValue(item);
    },
    error: () => {}
  });

  function updateReactQueryYtmData(socketData: TTodayAverage) {
    const updatedData = { ...todayAverage?.data, ...socketData };

    setTodayAverage({ data: updatedData } as TTodayAverageResponse);
  }

  const [pushToStackYtm] = useDelayedStack((data: TTodayAverage[]) => {
    updateReactQueryYtmData(data[data.length - 1]);
  }, 300);

  useSignalR(socketUrls.totalMarketYtmUrl, socketUrls.totalMarketYtmStreamName, {
    next: (item: TTodayAverage) => pushToStackYtm(item),
    error: () => {}
  });

  return (
    <div aria-hidden="true">
      {isCollapsed ? (
        <GeneralStatisticsCollapse
          tradeValues={tradeValues}
          todayAverage={todayAverage}
          tradeValuesLoading={tradeValuesLoading}
          averageLoading={averageLoading}
          activeBox={activeBox}
          durationValues={durationValues}
          duraionLoading={durationLoading}
        />
      ) : (
        <GeneralStatistics
          tradeValues={tradeValues}
          durationValues={durationValues}
          tradeValuesLoading={tradeValuesLoading}
          todayAverage={todayAverage}
          averageLoading={averageLoading}
          duraionLoading={durationLoading}
        />
      )}
    </div>
  );
}

export default GeneralStatisticsContainer;
