import useSignalR from "@/hooks/useSignalR/useSignalR";
import socketUrls from "@/constants/socketUrls";
import { IGetListOfTopYtm, IGetTopYtmItemDetail } from "@/queries/topYtmAPI/type";
import { useSocketInitialData } from "@/hooks/useSocketInitialData";
import useDelayedStack from "@reactutils/use-delayed-stack";
import _ from "lodash";
import { IBestYtmContainerProps } from "./type";
import BestYtmCollapse from "./collapse";
import BestYtm from "./expand";

function BestYtmContainer({ isCollapsed }: IBestYtmContainerProps) {
  const {
    data: bestYtmData,
    isLoading,
    error,
    setData
  } = useSocketInitialData<IGetListOfTopYtm>({
    url: socketUrls.TopSixYtmUrl,
    streamName: socketUrls.TopSixYtmLastState
  });

  function updateData(socketData: IGetTopYtmItemDetail[]) {
    const updatedData = _.unionBy(socketData, bestYtmData?.data, "isin");
    setData({ data: updatedData } as IGetListOfTopYtm);
  }

  const [pushToStack] = useDelayedStack((data: IGetTopYtmItemDetail[][]) => {
    updateData(data[data.length - 1]);
  }, 3000);

  useSignalR(socketUrls.TopSixYtmUrl, socketUrls.TopSixYtmStreamName, {
    next: (item: IGetTopYtmItemDetail[]) => {
      pushToStack(item);
    },
    error: () => {}
  });

  return (
    <div aria-hidden="true">
      {isCollapsed ? (
        <BestYtmCollapse data={bestYtmData?.data} isPending={isLoading} isError={error} />
      ) : (
        <BestYtm data={bestYtmData?.data} isPending={isLoading} isError={error} />
      )}
    </div>
  );
}

export default BestYtmContainer;
