import { TBondTableData, TBondTableResponse } from "@/queries/bondTableAPI";
import { ColDef, ColGroupDef } from "ag-grid-community";

import useToggleCollapse from "@/app/(home)/CollapseStore";
import Pin from "@/assets/icons/pin-bold.svg";
import { PriceTitle } from "@/components/atoms/priceTitle";
import { PriceYtm } from "@/components/atoms/priceYtm";
import TableLoading from "@/components/organisms/Table/TableLoading";
import { checkValidDate, dateConverter } from "@/utils/DateHelper";
import { commaSeparator } from "@/utils/helpers";
import { QueryClient, QueryKey } from "@tanstack/react-query";
import { TBoundTableSocket } from "./types";

export const columnDefs = ({ pinItems }: { pinItems?: TBondTableData[] }): (ColDef | ColGroupDef)[] => [
  {
    field: "symbol",
    headerName: "نام نماد",
    minWidth: 85,
    unSortIcon: true,
    sortable: true,
    // sort: "asc",
    cellRenderer: ({ data }: { data: TBondTableData }) => {
      const hasPin = pinItems?.find(item => item.isin === data?.isin);
      return (
        <div className="relative">
          {hasPin && <Pin className="absolute -top-2 -right-2.5 w-3 h-3 text-borderBorderAndDivider" />}
          <span className="">{data ? data.symbol : "---"}</span>
        </div>
      );
    },
    comparator: () => 0
  },
  {
    field: "lastTradePrice",
    headerName: "اخرین قیمت",
    minWidth: 130,

    unSortIcon: true,
    sortable: true,
    cellRenderer: ({ data }: { data: TBondTableData }) => (
      // <div className="mr-2">
      <PriceTitle
        changePrice={data?.lastTradePriceYtmYesterdayChange}
        closePrice={data?.lastTradePriceYtmInPercent}
        price={data?.lastTradePrice}
      />
      // </div>
    ),
    comparator: () => 0
  },
  {
    field: "lastTradeDate",
    headerName: " زمان معامله",
    minWidth: 130,

    unSortIcon: true,
    sortable: true,
    comparator: () => 0,
    cellRenderer: ({ data }: { data: TBondTableData }) => (
      <div className=" text-base mt-1 mr-2">
        {data?.lastTradeTime && data?.lastTradeDate ? (
          <div>
            {data?.lastTradeTime && data?.lastTradeDate === "0001-01-01T00:00:00" ? <>---</> : data?.lastTradeTime}
            <br />
            <p className="text-[10px]">
              {data?.lastTradeDate && data?.lastTradeDate === "0001-01-01T00:00:00"
                ? ""
                : dateConverter(data?.lastTradeDate).toString()}
            </p>
          </div>
        ) : (
          "---"
        )}
      </div>
    )
  },
  {
    field: "closePrice",
    headerName: "قیمت پایانی",
    minWidth: 140,
    unSortIcon: true,
    sortable: true,
    cellRenderer: ({ data }: { data: TBondTableData }) => (
      <PriceTitle
        changePrice={data?.closePriceYtmYesterdayChange}
        closePrice={data?.closePriceYtmInPercent}
        price={data?.closePrice}
      />
    ),
    comparator: () => 0
  },

  {
    field: "bestBuyPrice",
    headerName: "بهترین مظنه خرید",
    minWidth: 180,

    unSortIcon: true,
    sortable: true,
    cellRenderer: ({ data }: { data: TBondTableData }) => (
      <PriceYtm price={data?.bestBuyPrice} ytm={data?.bestBuyYtmInPercent} />
    ),
    comparator: () => 0
  },

  {
    field: "bestSellPrice",
    headerName: "بهترین مظنه فروش",
    minWidth: 180,

    unSortIcon: true,
    sortable: true,
    cellRenderer: ({ data }: { data: TBondTableData }) =>
      data ? <PriceYtm price={data?.bestSellPrice} ytm={data?.bestSellYtmInPercent} /> : "---",
    comparator: () => 0
  },
  {
    field: "totalTradedVolume",
    headerName: "حجم معاملات",
    minWidth: 140,
    initialSort: "desc",
    unSortIcon: true,
    sortable: true,
    cellRenderer: "renderYtmPrice",
    comparator: () => 0
  },
  {
    field: "bondType",
    headerName: "دارای کوپن",
    minWidth: 140,
    cellClass: "flex justify-center",
    unSortIcon: true,
    sortable: true,
    comparator: () => 0,
    cellRenderer: "renderCoupon"
  },
  {
    field: "maturityDate",
    headerName: "سررسید",
    unSortIcon: true,
    sortable: true,
    comparator: () => 0,
    minWidth: 84,

    cellRenderer: ({ data }: { data: TBondTableData }) =>
      data ? (
        <div className=" text-base mt-1 ">
          <div className="text-sm">
            {data?.maturityDate ? dateConverter(data?.maturityDate).toString() : ""} <br />
          </div>
          <p className=" text-xs">
            {data?.daysToMaturityDate > 0 ? (
              <div className="flex items-center gap-0.5">
                <span>{commaSeparator(data?.daysToMaturityDate)}</span>{" "}
                <span className="[word-spacing:1px]">روز مانده</span>
              </div>
            ) : (
              ""
            )}
          </p>
        </div>
      ) : (
        "---"
      )
  },
  {
    field: "duration",
    headerName: "دیرش",
    unSortIcon: true,
    sortable: true,
    comparator: () => 0,
    minWidth: 100,

    cellRenderer: "renderFloatNumber"
  },
  {
    field: "modifiedDuration",
    headerName: "دیرش تعدیل شده",
    unSortIcon: true,
    sortable: true,
    comparator: () => 0,
    minWidth: 160,

    cellRenderer: "renderFloatNumber"
  },
  {
    field: "convexity",
    headerName: "تحدب",
    unSortIcon: true,
    sortable: true,
    comparator: () => 0,
    minWidth: 100,

    cellRenderer: "renderFloatNumber"
  }
];

export function CustomLoadingOverlay() {
  const { isCollapsed } = useToggleCollapse();
  const colNumbers = isCollapsed ? 15 : 10;

  return <TableLoading colNumbers={colNumbers} rowNumbers={12} />;
}

export function CustomNoRows() {
  return <div className="flex items-center justify-center text-white h-full">نمادی یافت نشد</div>;
}

export function CustomNetworkError() {
  return <div className="flex items-center justify-center text-white h-full">ارتباط با سرور برقرار نیست</div>;
}

export const sortKeyName: { [key: string]: string } = {
  symbol: "SortOrderBondName",
  closePrice: "SortOrderClosingPriceYtm",
  lastTradePrice: "SortOrderLastTradePriceYtm",
  bestBuyPrice: "SortOrderBestBuyPriceYtm",
  bestSellPrice: "SortOrderBestSellPriceYtm",
  totalTradedVolume: "SortOrderTotalTradedVolume",
  maturityDate: "SortOrderDaysToMaturityDate",
  bondType: "SortOrderBondType",
  duration: "SortOrderDuration",
  modifiedDuration: "SortOrderModifiedDuration",
  convexity: "SortOrderConvexity",
  lastTradeDate: "SortOrderLastTradeDate"
};

type TTableData = {
  queryClient: QueryClient;
  data: TBoundTableSocket[];
  queryKey: QueryKey;
};

export function updateTableData({ queryClient, data, queryKey }: TTableData) {
  const cacheData = queryClient.getQueryData<TBondTableResponse>(queryKey);

  if (!cacheData) return;

  const updatedItems = cacheData.data?.items?.map(item => {
    const matchingData = data.filter(v => v.instrument.isin === item.isin);

    if (matchingData.length === 0) return item;

    const aggregatedData = matchingData.reduce(
      (acc: any, current) => ({
        lastTradePrice: current?.lastTradePrice || acc?.lastTradePrice,
        lastTradePriceYtm: current?.lastTradePriceYtm || acc?.lastTradePriceYtm,
        lastTradePriceYtmInPercent: current?.lastTradePriceYtmInPercent || acc?.lastTradePriceYtmInPercent,
        lastTradeDate: current?.lastTradeDate || acc?.lastTradeDate,
        closePrice: current?.closePrice || acc?.closePrice,
        closePriceYtmInPercent: current?.closePriceYtmInPercent || acc?.closePriceYtmInPercent,
        bestBuyPrice: current?.bestBuyPrice || acc?.bestBuyPrice,
        bestBuyPriceYtm: current.bestBuyPriceYtm || acc?.bestBuyPriceYtm,
        bestBuyYtmInPercent: current?.bestBuyYtmInPercent || acc?.bestBuyYtmInPercent,
        bestSellPrice: current?.bestSellPrice || acc?.bestSellPrice,
        bestSellPriceYtm: current?.bestSellPriceYtm || acc?.bestSellPriceYtm,
        bestSellYtmInPercent: current?.bestSellYtmInPercent || acc?.bestSellYtmInPercent,
        totalTradedVolume: current?.totalTradedVolume || acc?.totalTradedVolume,
        duration: current?.duration || acc?.duration,
        modifiedDuration: current?.modifiedDuration || acc?.modifiedDuration,
        convexity: current?.convexity || acc?.convexity,
        lastTradeTime: current?.lastTradeTime || acc?.lastTradeTime,
        lastTradePriceYtmYesterdayChange:
          current?.lastTradePriceYtmYesterdayChange || acc?.lastTradePriceYtmYesterdayChange
      }),
      {}
    );

    const {
      lastTradePrice,
      lastTradePriceYtm,
      lastTradePriceYtmInPercent,
      lastTradeDate,
      closePrice,
      closePriceYtmInPercent,
      bestBuyPrice,
      bestBuyPriceYtm,
      bestBuyYtmInPercent,
      bestSellPrice,
      bestSellPriceYtm,
      bestSellYtmInPercent,
      totalTradedVolume,
      duration,
      modifiedDuration,
      convexity,
      lastTradeTime,
      lastTradePriceYtmYesterdayChange,
      ...restItem
    } = item;

    return {
      ...restItem,
      lastTradePrice: aggregatedData.lastTradePrice || lastTradePrice,
      lastTradePriceYtm: aggregatedData.lastTradePriceYtm || lastTradePriceYtm,
      lastTradePriceYtmInPercent: aggregatedData.lastTradePriceYtmInPercent || lastTradePriceYtmInPercent,
      lastTradeDate: aggregatedData.lastTradeDate || lastTradeDate,
      closePrice: aggregatedData.closePrice || closePrice,
      closePriceYtmInPercent: aggregatedData.closePriceYtmInPercent || closePriceYtmInPercent,
      bestBuyPrice: aggregatedData.bestBuyPrice || bestBuyPrice,
      bestBuyPriceYtm: aggregatedData.bestBuyPriceYtm || bestBuyPriceYtm,
      bestBuyYtmInPercent: aggregatedData.bestBuyYtmInPercent || bestBuyYtmInPercent,
      bestSellPrice: aggregatedData.bestSellPrice || bestSellPrice,
      bestSellPriceYtm: aggregatedData.bestSellPriceYtm || bestSellPriceYtm,
      bestSellYtmInPercent: aggregatedData.bestSellYtmInPercent || bestSellYtmInPercent,
      totalTradedVolume: aggregatedData.totalTradedVolume || totalTradedVolume,
      duration: aggregatedData.duration || duration,
      modifiedDuration: aggregatedData.modifiedDuration || modifiedDuration,
      convexity: aggregatedData.convexity || convexity,
      lastTradeTime: aggregatedData.lastTradeTime || lastTradeTime,
      lastTradePriceYtmYesterdayChange:
        aggregatedData.lastTradePriceYtmYesterdayChange || lastTradePriceYtmYesterdayChange
    };
  });

  queryClient.setQueryData(queryKey, {
    ...cacheData,
    data: {
      ...cacheData.data,
      items: updatedItems
    }
  });
}

export const selectExcelFields = (res: TBondTableResponse) => {
  const f = "YYYY/M/D";

  return res.data.items.map(i => ({
    "نام نماد": i.symbol,
    "تاریخ انتشار": checkValidDate(dateConverter(i.issuanceDate).format(f)),

    "تاریخ سررسید": checkValidDate(dateConverter(i.maturityDate).format(f)),
    "روز تا سررسید": i.daysToMaturityDate,

    "مبلغ اسمی": i.faceValue,
    "سود اسمی": i.nominalInterestRate,

    "دفعات پرداخت در سال": i.paymentCount,
    "تاریخ اولین پرداخت": checkValidDate(dateConverter(i.firstPaymentDate).format(f)),

    "تعداد انتشار": i.totalSheetsAmount,
    "نوع ورقه": i.bondTypeName,

    "قیمت پایانی": i.closePrice,
    "قیمت پایانی ytm": i.closePriceYtmInPercent,

    "قیمت آخر": i.lastTradePrice,
    "قیمت آخر ytm": i.lastTradePriceYtmInPercent,

    "بهترین مظنه خرید": i.bestBuyPrice,
    "بهترین مظنه خرید ytm": i.bestBuyYtmInPercent,

    "بهترین مظنه فروش": i.bestSellPrice,
    "بهترین مظنه فروش ytm": i.bestSellYtmInPercent,

    "تاریخ آخرین معامله": checkValidDate(dateConverter(i.lastTradeDate).format(f)),
    "ساعت آخرین معامله": i.lastTradeTime,

    "حجم معاملات": i.totalTradedVolume,
    دیرش: i.duration,

    "دیرش تعدیل شده": i.modifiedDuration,
    تحدب: i.convexity,

    "بالاترین قیمت": i.dayHighPrice,
    "بالاترین قیمت ytm": i.dayHighPriceYtmInPercent,

    "کمترین قیمت": i.dayLowPrice,
    "پایین ترین قیمت ytm": i.dayLowPriceYtmInPercent,

    "تعداد معاملات": i.totalNumberOfTrades,
    "ارزش معاملات": i.totalTradeValue
  }));
};
