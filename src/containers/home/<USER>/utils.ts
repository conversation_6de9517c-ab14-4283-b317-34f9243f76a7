/* eslint-disable import/prefer-default-export */

import { z, ZodType } from "zod";
import { FormData, IRadioButtonProps } from "./types";

export const radioItems = [
  { id: "YTMRadio", label: "YTM", checked: true },
  { id: "buyingPriceRadio", label: "قیمت خرید", checked: false },
  { id: "sellingPriceRadio", label: "قیمت فروش", checked: false }
];

export const CalculatorSchema = ({ radioState }: IRadioButtonProps): ZodType<FormData> =>
  z.object({
    isin: z
      .string({ required_error: "نام نماد موردنظر خود را جستجو کنید." })
      .nonempty("نام نماد موردنظر خود را جستجو کنید")
      .describe("isin"),
    fullDate: z
      .array(z.date({ required_error: "مقدار را وارد کنید." }))
      .nonempty("مقدار را وارد کنید.")
      .describe("تاریخ خرید"),

    sellingPrice: z
      .number({ required_error: "مقدار را وارد کنید." })
      .min(0)
      .max(20000000, "مقدار وارد شده صحیح نمی باشد.")
      .optional()
      .nullable()
      .refine(
        data => {
          if (radioState === "YTMRadio" || radioState === "buyingPriceRadio") {
            try {
              z.number().parse(data);
              return true;
            } catch {
              return false;
            }
          }
          return true;
        },
        { message: "مقدار را وارد کنید." }
      ),

    buyingPrice: z
      .number({ required_error: "مقدار را وارد کنید." })
      .min(0)
      .max(20000000, "مقدار وارد شده صحیح نمی باشد.")
      .optional()
      .nullable()
      .refine(
        data => {
          if (radioState === "sellingPriceRadio" || radioState === "YTMRadio") {
            try {
              z.number().parse(data);
              return true;
            } catch {
              return false;
            }
          }
          return true;
        },
        { message: "مقدار را وارد کنید." }
      ),

    ytmInPercent: z
      .string()
      .min(0)
      .max(100)
      .optional()
      .nullable()
      .refine(
        data => {
          if (radioState === "buyingPriceRadio" || radioState === "sellingPriceRadio") {
            try {
              z.string().parse(data);
              return true;
            } catch {
              return false;
            }
          }
          return true;
        },
        { message: "مقدار را وارد کنید." }
      )
  });
