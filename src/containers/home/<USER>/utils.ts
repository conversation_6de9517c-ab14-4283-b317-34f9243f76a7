/* eslint-disable import/prefer-default-export */

/**
 * true => TradeValuePercent is shown
 * false => TradeValuePercent is hidden
 */

export function ShowTradeValuePercent(currentDateTime: number): boolean {
  // if time after 14 and before tomorrow 8

  if (
    new Date().setHours(14, 0, 0) < currentDateTime &&
    new Date(new Date().setDate(new Date().getDate() + 1)).setHours(8, 0, 0) > currentDateTime
  )
    return true;

  return false;
}
