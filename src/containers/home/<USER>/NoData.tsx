import { Table } from "@/components/organisms/Table";
import { ColDef, ColGroupDef, GridOptions } from "ag-grid-community";
import React from "react";
import { CustomNetworkError, CustomNoRows } from "./utils";

function NoData({ columnDefs, isError }: { columnDefs: (ColDef<any> | ColGroupDef<any>)[]; isError?: boolean }) {
  const gridOptions: GridOptions = {
    enableRtl: true,
    sortingOrder: ["asc", "desc"]
  };

  return (
    <Table
      noRowsOverlayComponent={() => (isError ? CustomNetworkError() : CustomNoRows())}
      columnDefs={columnDefs}
      gridOptions={gridOptions}
      data={[]}
    />
  );
}

export default NoData;
