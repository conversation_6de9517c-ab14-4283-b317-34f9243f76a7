import { twMerge } from "tailwind-merge";
import Styles from "./lineChart.module.scss";

export default function ChartWrapper({
  children,
  summary
}: Readonly<{
  children: React.ReactNode;
  summary: React.ReactNode;
}>) {
  return (
    <div className={twMerge(Styles.lineChartWrapper, "flex flex-col grow bg-dark_black rounded-xl")}>
      {summary}
      <div className="relative grow">
        <div className="absolute left-0 top-2  w-full h-full px-1 pt-0">{children}</div>
      </div>
    </div>
  );
}
