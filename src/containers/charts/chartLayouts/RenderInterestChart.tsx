import { FUND_INTEREST } from "@/containers/cards/type";
import YtmChart from "../interestRisk/YtmChart";
// import YtcChart from "../interestRisk/YtcChart";
import ConvexityChart from "../interestRisk/ConvexityChart";
import DurationChart from "../interestRisk/DurationChart";
import InterestWeightedAverageChart from "../interestRisk/InterestWeightedAverageChart";
import MarketCouponDurationChart from "../interestRisk/MarketCouponDurationChart";
import MarketZeroCouponDurationChart from "../interestRisk/MarketZeroCouponDurationChart";
import ModifiedDurationChart from "../interestRisk/ModifiedDurationChart";

function RenderInterestChart({ type }: { type: string }) {
  switch (type) {
    case FUND_INTEREST.YTM:
      return <YtmChart />;
    // case FUND_INTEREST.YTC:
    //   return <YtcChart id={fundId} />;
    case FUND_INTEREST.CONVEXITY:
      return <ConvexityChart />;
    case FUND_INTEREST.DURATION:
      return <DurationChart />;
    case FUND_INTEREST.DURATION_MODIFIED:
      return <ModifiedDurationChart />;
    case FUND_INTEREST.WEIGHTED_AVERAGE:
      return <InterestWeightedAverageChart />;
    case FUND_INTEREST.DURATION_COUPON:
      return <MarketCouponDurationChart />;
    case FUND_INTEREST.DURATION_ZERO_COUPON:
      return <MarketZeroCouponDurationChart />;
    default:
      return <YtmChart />;
  }
}

export default RenderInterestChart;
