.lineChartWrapper {
  :global {
    * {
      font-family: var(--font-yekan);
    }
    .tooltip {
      display: flex;
      flex-direction: column;
      justify-content: center;
      gap: 3px;
      min-width: 158px;
      font-size: 12px;
      color: #cbcbcc;
      line-height: 24px;
      font-size: 12px;

      pre {
        direction: rtl;
      }

      & > span.date {
        text-align: center;
        display: block;
        font-size: 10px;
        line-height: 16px;
        padding-bottom: 1px;
      }

      & > div {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 24px;
      }
    }

    .tooltip {
      span {
        display: flex;
        align-items: center;
        font-size: 12px;
        padding-top: 2px;
        color: #cbcbcc;
        i {
          display: inline-block;
          width: 12px;
          height: 12px;
          margin-left: 8px;
          border-radius: 2px;
        }
      }
      b {
        line-height: 22px;
        font-size: 14px;
        overflow: hidden;
      }
      .short-height {
        height: 16px;
        b {
          line-height: 16px;
        }
      }
      .space {
        margin-top: 8px;
      }
    }
    .chart-billion-label {
      display: flex;
      gap: 10px;
      font-size: 16px;
      align-items: center;
      padding-right: 8px;
    }
  }
}

.modalLineChart {
  :global {
    .rc-dialog-content {
      height: 100%;
      .rc-dialog-body {
        display: flex;
        flex-direction: column;
        height: 100%;
      }
    }
    .rc-dialog {
      margin: 0 auto !important;
    }
  }
}

.modalSensitivityChart {
  :global {
    .rc-dialog-content {
      border: 0 !important;
      border-radius: 12px;
      height: 100%;
      .rc-dialog-body {
        display: flex;
        flex-direction: column;
        height: 100%;
      }
    }
    .rc-dialog {
      margin: 0 auto !important;
    }
  }
}
