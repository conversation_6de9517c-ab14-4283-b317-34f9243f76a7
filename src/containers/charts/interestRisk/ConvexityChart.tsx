"use client";

/* eslint-disable react-hooks/exhaustive-deps */

import { useMemo } from "react";

import { Spinner } from "@/components/atoms/spinner";
import HighChart from "@/components/molecules/highChart/HighChart";
import { useGetFundConvexityHistoryChart } from "@/queries/FundChartsAPI";
import { getSeriesOfInterestRiskFormat, getSummaryInterestOptions } from "@/containers/cards/util";
import ChartWrapper from "../chartLayouts/ChartWrapper";
import SummaryBox from "../chartLayouts/SummaryBox";

function ConvexityChart() {
  const { data: raw, isLoading } = useGetFundConvexityHistoryChart({});
  const { data } = raw || {};

  const { graphPoints = [], yearAverage = 0 } = data || {};

  const colors = ["#8871BA"];
  const summary = getSummaryInterestOptions(yearAverage, 0, colors);

  const options = useMemo(
    () =>
      getSeriesOfInterestRiskFormat({
        graphPoints,
        field1: {
          id: "convexity",
          title: "تحدب پرتفو"
        },
        colors
      }),
    [data]
  );

  return (
    <ChartWrapper summary={<SummaryBox data={summary} />}>
      {raw && <HighChart options={options} showTunedPeriod />}
      {isLoading && (
        <div className="w-full h-full m-auto justify-center items-center flex relative top-[-46px]">
          <Spinner />
        </div>
      )}
    </ChartWrapper>
  );
}

export default ConvexityChart;
