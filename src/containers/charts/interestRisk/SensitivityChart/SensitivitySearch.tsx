/* eslint-disable react-hooks/exhaustive-deps */
import { ChangeEvent, useCallback, useRef, useState } from "react";
import debounce from "lodash/debounce";
import CloseIcon from "@/assets/icons/close-user-modal.svg";

function SensitivitySearch(props: { onChangeSensitivity: (v: number) => void }) {
  const { onChangeSensitivity } = props;

  const [search, setSearch] = useState("");
  const inputRef = useRef(null);

  const fnDebounce = useCallback(debounce(onChangeSensitivity, 300), [onChangeSensitivity]);

  const onSearchChange = (event: ChangeEvent<HTMLInputElement>) => {
    const v = event.target.value;
    setSearch(v);
    fnDebounce(Number(v));
  };

  const onClearSearch = () => {
    setSearch("");
    fnDebounce(0);

    if (inputRef.current) {
      (inputRef.current as HTMLInputElement).focus();
    }
  };

  return (
    <div className="flex flex-col gap-1 pt-[6px] border-t border-t-borderBorderAndDivider">
      <div className="relative bg-dark_black">
        <input
          ref={inputRef}
          type="number"
          value={search}
          onChange={onSearchChange}
          className="ltr w-full leading-6 h-6 pl-5 pr-5 text-right bg-transparent text-borderLightGray border border-[#444] rounded focus:border-semanticPrimary2 outline-0"
        />
        <div className="w-3 h-3 pt-1 absolute right-1 top-1/2 transform -translate-y-1/2 cursor-pointer flex justify-center items-center">
          <span>%</span>
        </div>

        <span
          role="presentation"
          onClick={onClearSearch}
          className="w-4 h-4 absolute left-1 top-1/2 transform -translate-y-1/2 cursor-pointer flex justify-center items-center"
        >
          {search !== "" && <CloseIcon width={9} height={9} />}
        </span>
      </div>
      <span className="flex justify-center items-center text-xs leading-6 rounded-b text-white bg-dark_black">
        نرخ بهره
      </span>
    </div>
  );
}

export default SensitivitySearch;
