/* eslint-disable no-plusplus */
/* eslint-disable no-param-reassign */

import { convertIntToCurrency, numbersUnitInToman, toDecimals } from "@/utils/helpers";

export const getZeroColumnChartOption = ({
  series,
  categories,
  min,
  max,
  height
}: {
  series: any;
  categories: string[];
  min?: number;
  max?: number;
  height?: number;
}) => ({
  chart: {
    ...(height ? { height } : {}),
    type: "column",
    animation: false,
    backgroundColor: "transparent",
    marginLeft: 60,
    marginBottom: 39,
    marginRight: 0,
    zooming: {
      type: "x",
      resetButton: {
        position: {
          x: 0,
          y: 3
        },
        theme: {
          fill: "#F4F4F4",
          stroke: "transparent",
          class: "zoom-button",
          border: 0,
          r: 4,
          style: {
            color: "#161616",
            fontSize: "14px",
            fontWeight: "400"
          }
        }
      }
    }
  },
  title: {
    text: ""
  },
  subtitle: {
    text: ""
  },
  xAxis: {
    categories,
    gridLineWidth: 1,
    gridLineColor: "#343438",
    gridLineDashStyle: "dash",
    lineColor: "red",
    lineWidth: 1,
    labels: {
      useHTML: true,
      rotation: 0,
      y: 20,
      /* @ts-ignore */
      style: {
        color: "#F4F4F4",
        fontFamily: "var(--font-yekan)",
        fontSize: "12px",
        fontWeight: "400",
        textOverflow: "none"
      },
      // @ts-ignore
      formatter() {
        // @ts-ignore
        return `${toDecimals(Number(this?.value))}٪`;
      }
    }
  },
  yAxis: {
    offset: 0,
    ...(min !== undefined && { min }),
    ...(max !== undefined && { max }),
    title: false,
    lineColor: "#343438",
    lineWidth: 1,
    gridLineColor: "#343438",
    gridLineDashStyle: "dash",
    gridLineWidth: 1,

    ...(min !== undefined && {
      plotBands: [
        {
          color: "#676767",
          width: 1,
          value: 0,
          zIndex: 4
        }
      ]
    }),
    labels: {
      useHTML: true,
      x: -8,
      /* @ts-ignore */
      // eslint-disable-next-line
      formatter: function () {
        /* @ts-ignore */
        const { value } = this;
        return convertIntToCurrency(value, 0).value + convertIntToCurrency(value).unit;
      },
      style: {
        color: "#F4F4F4",
        fontFamily: "var(--font-yekan)",
        fontSize: "12px",
        width: "auto"
      }
    }
  },
  plotOptions: {
    series: {
      animation: false,
      stickyTracking: false,
      states: {
        hover: {
          enabled: false
        }
      }
    },
    column: {
      borderRadius: 0,
      pointPadding: 0,
      borderWidth: 0,
      pointWidth: 61,
      minPointLength: 5,
      dataLabels: {
        useHTML: true,
        enabled: true,
        color: "#F4F4F4",
        align: "center",
        borderColor: "transparent",
        borderWidth: 0,
        borderRadius: 16,
        backgroundColor: "rgba(61, 61, 61, 0.3)",
        style: {
          fontSize: "12px",
          lineHeight: "16px",
          textShadow: false,
          textOutline: "none",
          fontFamily: "var(--font-yekan)"
        },
        /* @ts-ignore */
        // eslint-disable-next-line
        formatter: function (label) {
          /* @ts-ignore */
          if (this.y >= 0) {
            label.y = -8;
          } else {
            label.y = 8;
          }

          /* @ts-ignore */
          const v = convertIntToCurrency(this.y, 2).value + convertIntToCurrency(this.y).unit;

          return `<span style="padding:0 8px">${v}</span>`;
        }
      }
    }
  },
  series,
  tooltip: {
    enabled: false
  },
  credits: {
    enabled: false
  },
  legend: {
    enabled: false
  }
});

export const getSensitivCardOption = ({ series, categories }: { series: any; categories: string[] }) => {
  const minY = Math.min(...series[0].data);
  const maxY = Math.max(...series[0].data);

  const interval = (maxY - minY) / 5;
  const plotLines = [];
  const gridLineColor = "#404040";

  for (let i = 1; i < 5; i++) {
    plotLines.push({
      value: minY + interval * i,
      color: gridLineColor,
      width: 1,
      zIndex: 1,
      dashStyle: "dash"
    });
  }

  const options = getZeroColumnChartOption({ series, categories });

  return {
    ...options,
    chart: {
      ...options?.chart,
      marginLeft: 8,
      marginRight: 10,
      marginBottom: 5,
      marginTop: 0,
      zooming: {
        enabled: false
      }
    },
    xAxis: {
      ...options.xAxis,
      gridLineColor,
      lineWidth: 0,
      labels: {
        ...options.xAxis.labels,
        enabled: false
      }
    },
    yAxis: {
      ...options.yAxis,
      gridLineColor,
      lineWidth: 0,
      plotLines,
      labels: {
        enabled: false
      }
    },
    plotOptions: {
      ...options.plotOptions,
      column: {
        ...options.plotOptions.column,
        pointWidth: 22,
        minPointLength: 5,
        pointPadding: 2,
        dataLabels: {
          enabled: false
        }
      }
    },
    tooltip: {
      /* @ts-ignore */
      useHTML: true,
      outside: true,
      shadow: false,
      shared: true,
      backgroundColor: "#28282c",
      borderColor: "#545454",
      borderRadius: 8,
      borderWidth: 1,
      color: "#F4F4F4",
      padding: 12,
      animation: false,
      /* @ts-ignore */
      // eslint-disable-next-line
      formatter: function () {
        // @ts-ignore
        const item = this.point;
        return `
          <div class="text-[#F4F4F4]">
            <div class="flex items-center justify-between gap-2" dir="rtl">
              <span>${numbersUnitInToman(item.y)}</span>  
                <span class="whitespace-nowrap">تغییرات ارزش پرتفوی  اوراق<span>
              </div>

            <div class="flex items-center justify-between gap-2" dir="rtl">
              <span>${item.category}%</span>
                <span class="whitespace-nowrap">مفروضات تغییرات نرخ بهره<span>
              </div>
            </div>
        `;
      }
    }
  };
};

export const getZeroSingleColumnChartOption = ({
  series,
  labelValue,
  min,
  max
}: {
  series: any;
  labelValue: number;
  min: number;
  max: number;
}) => ({
  chart: {
    type: "column",
    animation: false,
    backgroundColor: "transparent",
    zooming: {
      type: "x",
      resetButton: {
        position: {
          x: 0,
          y: 3
        },
        theme: {
          fill: "#F4F4F4",
          stroke: "transparent",
          class: "zoom-button",
          border: 0,
          r: 4,
          style: {
            color: "#161616",
            fontSize: "14px",
            fontWeight: "400"
          }
        }
      }
    }
  },
  title: {
    text: ""
  },
  subtitle: {
    text: ""
  },
  xAxis: {
    lineColor: "#858585",
    labels: {
      enabled: false
    },
    plotBands: {
      color: "rgba(61, 61, 61, 0.3)",
      from: -1,
      to: 0.5
    }
  },
  yAxis: {
    min,
    max,
    title: false,
    gridLineColor: "#545454",
    gridLineDashStyle: "longdash",
    gridLineWidth: 1,
    plotLines: [
      {
        color: "#858585",
        width: 1,
        value: 0,
        zIndex: 5
      }
    ],
    labels: {
      enabled: false
    }
  },
  plotOptions: {
    column: {
      borderRadius: 0,
      pointPadding: 0,
      borderWidth: 0,
      pointWidth: 61,
      dataLabels: {
        enabled: true,
        useHTML: true,
        color: "#F4F4F4",
        align: "center",
        borderColor: "transparent",
        borderWidth: 0,
        backgroundColor: "transparent",
        style: {
          fontSize: "12px",
          lineHeight: "16px",
          textShadow: false,
          textOutline: "none",
          fontFamily: "var(--font-yekan)"
        },
        /* @ts-ignore */
        // eslint-disable-next-line
        formatter: function (label) {
          /* @ts-ignore */
          if (this.y >= 0) {
            label.y = -8;
          } else {
            label.y = 8;
          }

          if (!labelValue) {
            return "";
          }

          const v = convertIntToCurrency(labelValue, 2).value + convertIntToCurrency(labelValue).unit;
          return `<span class="px-2 rounded-2xl bg-opacity-30 bg-dark_black10">${v}</span>`;
        }
      }
    }
  },
  series,
  credits: {
    enabled: false
  },
  tooltip: {
    enabled: false
  },
  legend: {
    enabled: false
  }
});
