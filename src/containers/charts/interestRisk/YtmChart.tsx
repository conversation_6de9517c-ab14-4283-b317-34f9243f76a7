"use client";

/* eslint-disable react-hooks/exhaustive-deps */

import { useMemo } from "react";
import { parseAsBoolean, useQueryStates } from "nuqs";
import { getSeriesOfInterestRiskFormat, getSummaryInterestOptions } from "@/containers/cards/util";
import HighChart from "@/components/molecules/highChart/HighChart";
import { Spinner } from "@/components/atoms/spinner";
import { useGetFundYtmHistoryChart } from "@/queries/FundChartsAPI";
import ChartWrapper from "../chartLayouts/ChartWrapper";
import SummaryBox from "../chartLayouts/SummaryBox";

function YtmChart() {
  const { data: raw, isLoading } = useGetFundYtmHistoryChart({});
  const { data } = raw || {};

  const [queryStates] = useQueryStates({
    isShowMarketValue: parseAsBoolean.withDefault(true)
  });
  const { isShowMarketValue } = queryStates;

  const { graphPoints = [], yearAverage = 0, marketYearAverage = 0 } = data || {};
  const colors = ["#8871BA", "#F4F4F4"];
  const summary = getSummaryInterestOptions(yearAverage, isShowMarketValue ? marketYearAverage : undefined, colors);

  const options = useMemo(
    () =>
      getSeriesOfInterestRiskFormat({
        graphPoints,
        checked: !!isShowMarketValue,
        field1: {
          id: "ytm",
          title: "پرتفو"
        },
        field2: {
          id: "marketYtm",
          title: "بازار"
        },
        hasPercent: true,
        colors
      }),
    [data, isShowMarketValue]
  );

  return (
    <ChartWrapper summary={<SummaryBox data={summary} hasPercent />}>
      {raw && <HighChart options={options} showTunedPeriod />}
      {isLoading && (
        <div className="w-full h-full m-auto justify-center items-center flex relative top-[-46px]">
          <Spinner />
        </div>
      )}
    </ChartWrapper>
  );
}

export default YtmChart;
