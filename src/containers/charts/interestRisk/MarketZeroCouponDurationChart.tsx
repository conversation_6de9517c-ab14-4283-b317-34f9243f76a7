"use client";

/* eslint-disable react-hooks/exhaustive-deps */

import { useMemo } from "react";
import HighChart from "@/components/molecules/highChart/HighChart";
import { useGetMarketZeroCouponDurationHistoryChart } from "@/queries/FundChartsAPI";
import { getSeriesOfInterestRiskFormat, getSummaryInterestOptions } from "@/containers/cards/util";
import ChartWrapper from "../chartLayouts/ChartWrapper";
import SummaryBox from "../chartLayouts/SummaryBox";

function MarketZeroCouponDurationChart() {
  const { data: raw } = useGetMarketZeroCouponDurationHistoryChart();
  const { data } = raw || {};

  const { graphPoints = [], durationYearAverage = 0, modifiedDurationYearAverage = 0 } = data || {};

  const colors = ["#55C3FF", "#9A89C9"];
  const summary = getSummaryInterestOptions(durationYearAverage, modifiedDurationYearAverage, colors);

  const options = useMemo(
    () =>
      getSeriesOfInterestRiskFormat({
        graphPoints,
        field1: {
          id: "duration",
          title: "دیرش"
        },
        field2: {
          id: "modifiedDuration",
          title: "دیرش تعدیل شده"
        },
        colors
      }),
    [data]
  );

  return (
    <ChartWrapper summary={<SummaryBox data={summary} />}>
      {raw && <HighChart options={options} showTunedPeriod />}
    </ChartWrapper>
  );
}

export default MarketZeroCouponDurationChart;
