// eslint-disable-next-line import/prefer-default-export
export const fakeData = {
  data: {
    graphPoints: [
      {
        checkpointDate: "2025-03-05T00:00:00",
        totalDebitAmount: -230000,
        totalDepositAmount: 250000,
        depositTransactions: [
          {
            reasonCategory: 400,
            symbol: "اراد",
            reasonDescription: "خرید اوراق",
            contractNumber: 1200,
            bondOriginType: 100,
            amount: 70000
          },
          {
            reasonCategory: 100,
            symbol: "اراد",
            reasonDescription: "فروش دارایی اوراق",
            contractNumber: 1200,
            bondOriginType: 100,
            amount: 120000
          },
          {
            reasonCategory: 200,
            symbol: "اراد",
            reasonDescription: "درآمد بهره",
            contractNumber: 1200,
            bondOriginType: 100,
            amount: 60000
          }
        ],
        withdrawalTransactions: [
          {
            reasonCategory: 400,
            symbol: "اراد",
            reasonDescription: "خرید اوراق از تابلو",
            contractNumber: 1200,
            bondOriginType: 100,
            amount: 100000
          },
          {
            reasonCategory: 300,
            symbol: "اراد",
            reasonDescription: "کارمزد",
            contractNumber: 1200,
            bondOriginType: 100,
            amount: 30000
          },
          {
            reasonCategory: 400,
            symbol: "اراد",
            reasonDescription: "پرداخت بدهی",
            contractNumber: 1200,
            bondOriginType: 100,
            amount: 100000
          }
        ]
      },
      {
        checkpointDate: "2025-03-12T00:00:00",
        totalDebitAmount: -180000,
        totalDepositAmount: 210000,
        depositTransactions: [
          {
            reasonCategory: 100,
            symbol: "اراد",
            reasonDescription: "فروش اوراق از طریق قرارداد",
            contractNumber: 1200,
            bondOriginType: 700,
            amount: 100000
          },
          {
            reasonCategory: 200,
            symbol: "اراد",
            reasonDescription: "سود اوراق",
            contractNumber: 1200,
            bondOriginType: 700,
            amount: 40000
          },
          {
            reasonCategory: 100,
            symbol: "اراد",
            reasonDescription: "فروش دارایی",
            contractNumber: 1200,
            bondOriginType: 100,
            amount: 70000
          }
        ],
        withdrawalTransactions: [
          {
            reasonCategory: 400,
            symbol: "اراد",
            reasonDescription: "خرید مجدد",
            contractNumber: 1200,
            bondOriginType: 100,
            amount: 120000
          },
          {
            reasonCategory: 300,
            symbol: "اراد",
            reasonDescription: "هزینه کارمزد",
            contractNumber: 1200,
            bondOriginType: 100,
            amount: 60000
          }
        ]
      },
      {
        checkpointDate: "2025-03-19T00:00:00",
        totalDebitAmount: -200000,
        totalDepositAmount: 230000,
        depositTransactions: [
          {
            reasonCategory: 200,
            symbol: "اراد",
            reasonDescription: "بهره اوراق",
            contractNumber: 1200,
            bondOriginType: 100,
            amount: 30000
          },
          {
            reasonCategory: 100,
            symbol: "اراد",
            reasonDescription: "فروش دارایی اوراق",
            contractNumber: 1200,
            bondOriginType: 100,
            amount: 100000
          },
          {
            reasonCategory: 100,
            symbol: "اراد",
            reasonDescription: "فروش تابلو",
            contractNumber: 1200,
            bondOriginType: 100,
            amount: 100000
          }
        ],
        withdrawalTransactions: [
          {
            reasonCategory: 400,
            symbol: "اراد",
            reasonDescription: "سرمایه‌گذاری جدید",
            contractNumber: 1200,
            bondOriginType: 100,
            amount: 150000
          },
          {
            reasonCategory: 300,
            symbol: "اراد",
            reasonDescription: "کارمزد عملیات",
            contractNumber: 1200,
            bondOriginType: 100,
            amount: 50000
          }
        ]
      },
      {
        checkpointDate: "2025-03-26T00:00:00",
        totalDebitAmount: 240000,
        totalDepositAmount: 250000,
        depositTransactions: [
          {
            reasonCategory: 100,
            symbol: "اراد",
            reasonDescription: "فروش اوراق",
            contractNumber: 1200,
            bondOriginType: 100,
            amount: 120000
          },
          {
            reasonCategory: 200,
            symbol: "اراد",
            reasonDescription: "درآمد سود",
            contractNumber: 1200,
            bondOriginType: 100,
            amount: 50000
          },
          {
            reasonCategory: 100,
            symbol: "اراد",
            reasonDescription: "فروش تابلو",
            contractNumber: 1200,
            bondOriginType: 100,
            amount: 80000
          }
        ],
        withdrawalTransactions: [
          {
            reasonCategory: 400,
            symbol: "اراد",
            reasonDescription: "خرید اوراق",
            contractNumber: 1200,
            bondOriginType: 100,
            amount: 180000
          },
          {
            reasonCategory: 300,
            symbol: "اراد",
            reasonDescription: "کارمزد تراکنش",
            contractNumber: 1200,
            bondOriginType: 100,
            amount: 60000
          }
        ]
      },
      // Continue with existing and new dates
      {
        checkpointDate: "2025-04-02T00:00:00",
        totalDebitAmount: 270000,
        totalDepositAmount: 280000,
        depositTransactions: [
          {
            reasonCategory: 100,
            symbol: "اراد",
            reasonDescription: "فروش اوراق",
            contractNumber: 1200,
            bondOriginType: 100,
            amount: 180000
          },
          {
            reasonCategory: 200,
            symbol: "اراد",
            reasonDescription: "بهره دریافتی",
            contractNumber: 1200,
            bondOriginType: 100,
            amount: 100000
          }
        ],
        withdrawalTransactions: [
          {
            reasonCategory: 400,
            symbol: "اراد",
            reasonDescription: "سرمایه‌گذاری",
            contractNumber: 1200,
            bondOriginType: 100,
            amount: 200000
          },
          {
            reasonCategory: 300,
            symbol: "اراد",
            reasonDescription: "هزینه‌ها",
            contractNumber: 1200,
            bondOriginType: 100,
            amount: 70000
          }
        ]
      },
      {
        checkpointDate: "2025-04-09T00:00:00",
        totalDebitAmount: 260000,
        totalDepositAmount: 290000,
        depositTransactions: [
          {
            reasonCategory: 100,
            symbol: "اراد",
            reasonDescription: "فروش دارایی",
            contractNumber: 1200,
            bondOriginType: 100,
            amount: 150000
          },
          {
            reasonCategory: 200,
            symbol: "اراد",
            reasonDescription: "سود",
            contractNumber: 1200,
            bondOriginType: 100,
            amount: 140000
          }
        ],
        withdrawalTransactions: [
          {
            reasonCategory: 400,
            symbol: "اراد",
            reasonDescription: "خرید اوراق از تابلو",
            contractNumber: 1200,
            bondOriginType: 100,
            amount: 230000
          },
          {
            reasonCategory: 300,
            symbol: "اراد",
            reasonDescription: "کارمزد",
            contractNumber: 1200,
            bondOriginType: 100,
            amount: 30000
          }
        ]
      },
      // Keep all your original entries
      {
        checkpointDate: "2025-04-30T00:00:00",
        totalDebitAmount: 260000,
        totalDepositAmount: 260000,
        depositTransactions: [
          /* existing */
        ],
        withdrawalTransactions: [
          /* existing */
        ]
      },
      {
        checkpointDate: "2025-05-07T00:00:00",
        totalDebitAmount: 180000,
        totalDepositAmount: 200000,
        depositTransactions: [
          /* existing */
        ],
        withdrawalTransactions: [
          /* existing */
        ]
      },
      {
        checkpointDate: "2025-05-14T00:00:00",
        totalDebitAmount: 150000,
        totalDepositAmount: 170000,
        depositTransactions: [
          /* existing */
        ],
        withdrawalTransactions: [
          /* existing */
        ]
      },
      {
        checkpointDate: "2025-05-21T00:00:00",
        totalDebitAmount: 220000,
        totalDepositAmount: 250000,
        depositTransactions: [
          {
            reasonCategory: 100,
            symbol: "اراد",
            reasonDescription: "فروش اوراق",
            contractNumber: 1200,
            bondOriginType: 100,
            amount: 170000
          },
          {
            reasonCategory: 200,
            symbol: "اراد",
            reasonDescription: "سود اوراق",
            contractNumber: 1200,
            bondOriginType: 100,
            amount: 80000
          }
        ],
        withdrawalTransactions: [
          {
            reasonCategory: 400,
            symbol: "اراد",
            reasonDescription: "خرید اوراق",
            contractNumber: 1200,
            bondOriginType: 100,
            amount: 200000
          },
          {
            reasonCategory: 300,
            symbol: "اراد",
            reasonDescription: "هزینه انتقال",
            contractNumber: 1200,
            bondOriginType: 100,
            amount: 20000
          }
        ]
      },
      {
        checkpointDate: "2025-05-28T00:00:00",
        totalDebitAmount: 190000,
        totalDepositAmount: 230000,
        depositTransactions: [
          {
            reasonCategory: 100,
            symbol: "اراد",
            reasonDescription: "فروش دارایی",
            contractNumber: 1200,
            bondOriginType: 100,
            amount: 120000
          },
          {
            reasonCategory: 200,
            symbol: "اراد",
            reasonDescription: "درآمد سود",
            contractNumber: 1200,
            bondOriginType: 100,
            amount: 110000
          }
        ],
        withdrawalTransactions: [
          {
            reasonCategory: 400,
            symbol: "اراد",
            reasonDescription: "سرمایه‌گذاری مجدد",
            contractNumber: 1200,
            bondOriginType: 800,
            amount: 160000
          },
          {
            reasonCategory: 300,
            symbol: "اراد",
            reasonDescription: "کارمزد خدمات",
            contractNumber: 1200,
            bondOriginType: 800,
            amount: 30000
          }
        ]
      }
    ]
  },
  isSuccess: true,
  errorCode: null,
  errorMessage: null
};
