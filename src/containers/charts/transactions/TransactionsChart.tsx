"use client";

/* eslint-disable react-hooks/exhaustive-deps */

import { useMemo } from "react";
import { parseAsString, useQueryStates } from "nuqs";
import { getSeriesOfTransactionFormat } from "@/containers/cards/util";
import HighChart from "@/components/molecules/highChart/HighChart";
import { useGetTransactionsChart } from "@/queries/TransactionsAPI";
import { useVarDateFilterStore } from "@/store/varDateFilter";
import Table from "@/assets/icons/transaction-table.svg";
import Chart from "@/assets/icons/transaction-chart.svg";

export const switchItems = [
  {
    title: "جدول",
    id: 1,
    icon: <Table />
  },
  {
    title: "نمودار",
    id: 2,
    icon: <Chart />
  }
];

function TransactionsChart() {
  const { period } = useVarDateFilterStore();
  const [queryStates] = useQueryStates({
    purchaseType: parseAsString.withDefault("100"),
    transactionType: parseAsString.withDefault("1")
  });
  const { data: raw } = useGetTransactionsChart({
    fromDate: period?.fromDate,
    toDate: period?.toDate,
    filterByPurchaseType: queryStates?.purchaseType
  });
  const { data } = raw || {};

  const transformApiData = data?.graphPoints?.map(point => ({
    checkpointDate: point.checkpointDate,
    totalDebitAmount: point.totalDebitAmount,
    totalDepositAmount: point.totalDepositAmount,
    depositTransactions: point.depositTransactions,
    withdrawalTransactions: point.withdrawalTransactions
  }));

  const options = useMemo(
    () =>
      getSeriesOfTransactionFormat({
        data: transformApiData
      }),
    [data]
  );

  return (
    <div className="bg-[#343438] p-1 rounded-br-lg rounded-bl-lg grow h-full">
      <div className="bg-backgroundDarkRow h-full">
        <HighChart options={options} />
      </div>
    </div>
  );
}

export default TransactionsChart;
