/* eslint-disable no-nested-ternary */
import { ISelectItems } from "@/components/atoms/select";
import { StylesConfig } from "react-select";

const selectStyles: StylesConfig<ISelectItems<any>, boolean> = {
  control: styles => ({
    ...styles,
    width: "100%",
    minHeight: "20px !important",

    borderRadius: "4px",
    border: "1px solid #858585",
    boxShadow: "none",
    backgroundColor: "#28282C",
    color: "#F4F4F4",
    fontSize: "14px",
    cursor: "pointer"
  }),
  menu: styles => ({
    ...styles,
    zIndex: 20,
    fontSize: "10px",
    marginTop: "0",
    background: "#343438",
    borderRadius: "4px",
    border: "1px solid #545454",
    boxShadow: "0px 4px 12px 0px rgba(23, 28, 35, 0.20)"
  }),
  menuList: styles => ({
    ...styles,
    padding: "0 6px"
  }),
  container: styles => ({
    ...styles,
    "& *": {
      color: "#F4F4F4 !important"
    }
  }),
  input: styles => ({
    ...styles,
    color: "#F4F4F4",
    cursor: "pointer",
    padding: "0.5px 8px !important"
  }),
  option: (styles, state) => {
    const { data, options } = state;
    const currentIndex = options.findIndex((opt: any) => opt.value === data.value);
    const isLast = currentIndex === options.length - 1;

    return {
      ...styles,
      fontWeight: 400,
      fontSize: "14px",
      textAlign: "right",
      padding: "8px 6px",
      background: state.isSelected ? "transparent" : "unset",
      borderBottom: isLast ? "none" : "1px solid #676767",
      color: state.isSelected ? "#0C82F9 !important" : "#EFEFEF !important",
      "&:hover": {
        cursor: "pointer",
        background: "#1F1F22"
      },
      "&:active": {
        background: "transparent"
      },
      "&:focus": {
        background: "transparent"
      }
    };
  }
};

export default selectStyles;
