import dayjs from "dayjs";

// eslint-disable-next-line @typescript-eslint/naming-convention
export enum FILTER_TIME_FUTURE {
  Yearly = "1year",
  Month6 = "6month",
  Month3 = "3month",
  Month1 = "1month"
}

// eslint-disable-next-line @typescript-eslint/naming-convention
export enum FILTER_TIME_PAST {
  Month6 = "6month",
  Month3 = "3month",
  Month1 = "1month",
  Now = "now"
}

export const getPrevDate = (time: string | number) => {
  const f = "YYYY-MM-DD";

  switch (time) {
    case FILTER_TIME_FUTURE.Month1:
      return dayjs().subtract(1, "month").format(f);
    case FILTER_TIME_FUTURE.Month3:
      return dayjs().subtract(3, "month").format(f);
    case FILTER_TIME_FUTURE.Month6:
      return dayjs().subtract(6, "month").format(f);
    case FILTER_TIME_FUTURE.Yearly:
      return dayjs().subtract(1, "year").format(f);
    default:
      return dayjs().format(f);
  }
};

export const getPeriodByRange = (
  from: FILTER_TIME_PAST | "now",
  to: FILTER_TIME_FUTURE | "now"
): { fromDate: string; toDate: string } => {
  const f = "YYYY-MM-DD";

  const calculateDate = (value: string, direction: "add" | "subtract") => {
    const match = value.match(/^(\d+)(month|year)$/);
    if (!match) return dayjs();

    const amount = parseInt(match[1], 10);
    const unit = match[2] as "month" | "year";

    return direction === "subtract" ? dayjs().subtract(amount, unit) : dayjs().add(amount, unit);
  };

  const fromDate = from === "now" ? dayjs() : calculateDate(from, "subtract");

  const toDate = to === "now" ? dayjs() : calculateDate(to, "add");

  return {
    fromDate: fromDate.format(f),
    toDate: toDate.format(f)
  };
};
