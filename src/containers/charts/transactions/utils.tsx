/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable no-nested-ternary */
import useToggleCollapse from "@/app/(home)/CollapseStore";
import CellTooltip from "@/components/molecules/cellTooltip/CellTooltip";
import TableLoading from "@/components/organisms/Table/TableLoading";
import { FlatTransaction } from "@/queries/TransactionsAPI/types";
import { dateConverter } from "@/utils/DateHelper";
import { numbersUnit } from "@/utils/helpers";
import { ColDef, ColGroupDef } from "ag-grid-community";

export function columnDefs(): (ColDef | ColGroupDef)[] {
  return [
    {
      field: "transactionDate",
      headerName: "تاریخ",
      flex: 1,
      minWidth: 160,
      unSortIcon: true,
      sortable: true,
      // headerClass: "mt-[2px] !ps-0",
      // sort: "asc",
      cellRenderer: ({ data }: { data: FlatTransaction }) => (
        <div>{data?.transactionDate ? dateConverter(data?.transactionDate).format("YYYY/MM/DD") : "---"}</div>
      ),
      comparator: () => 0
    },

    {
      field: "reasonDescription",
      headerName: "علت",
      flex: 1,
      minWidth: 320,
      headerClass: "mt-[2px]",
      unSortIcon: true,
      sortable: true,
      cellRenderer: ({ data }: { data: FlatTransaction }) => (
        <CellTooltip title={data?.reasonDescription || ""} titleLength={35} className="pr-0 truncate max-w-[300px]" />
      ),

      // sort: "asc",
      comparator: () => 0
    },
    {
      field: "symbol",
      headerName: "نماد",
      flex: 1,
      minWidth: 142,
      headerClass: "mt-[2px]",
      unSortIcon: true,
      sortable: true,
      comparator: () => 0
    },
    {
      field: "amount",
      headerName: "مبلغ",
      flex: 1,
      minWidth: 142,
      headerClass: "mt-[2px]",
      unSortIcon: true,
      sortable: true,
      comparator: () => 0,
      cellRenderer: ({ data }: { data: FlatTransaction }) => (
        <div className="pr-1">{data?.amount ? numbersUnit(data?.amount) : "-"}</div>
      )
    },
    {
      field: "purchaseType",
      headerName: "نوع معامله",
      flex: 1,
      minWidth: 142,
      headerClass: "mt-[2px]",
      unSortIcon: true,
      sortable: true,
      cellRenderer: ({ data }: { data: FlatTransaction }) => (
        <div className="pr-1">
          {data?.purchaseType ? (data?.purchaseType === 100 ? "قراردادی" : "معاملات تابلو") : "-"}
        </div>
      ),
      comparator: () => 0
    },
    {
      field: "contractNumber",
      headerName: "شماره قرارداد",
      flex: 1,
      minWidth: 142,
      headerClass: "mt-[2px]",
      unSortIcon: true,
      sortable: true,
      comparator: () => 0,
      cellRenderer: ({ data }: { data: FlatTransaction }) => <div className="pr-1">{data?.contractNumber}</div>
    },

    {
      field: "bondOriginType",
      headerName: "نوع",
      flex: 1,
      minWidth: 142,
      headerClass: "mt-[2px]",
      unSortIcon: true,
      sortable: true,
      cellRenderer: ({ data }: { data: FlatTransaction }) => (
        <div className="pr-1">{data?.bondOriginType ? (data?.bondOriginType === 200 ? "تسهیلات" : "دارایی") : "-"}</div>
      ),
      comparator: () => 0
    },
    {
      field: "flowType",
      headerName: "نوع جریان نقدی",
      flex: 1,
      minWidth: 142,
      headerClass: "mt-[2px]",
      unSortIcon: false,
      sortable: false,
      cellRenderer: ({ data }: { data: FlatTransaction }) => (
        <div className="pr-1">
          {data?.flowType ? (
            data?.flowType === 100 ? (
              <span className="text-[#0FA968]">ورودی</span>
            ) : (
              <span className="text-[#F83B3B]">خروجی</span>
            )
          ) : (
            "-"
          )}
        </div>
      ),
      comparator: () => 0
    }
  ];
}

export function CustomLoadingOverlay() {
  const { isCollapsed } = useToggleCollapse();
  const colNumbers = isCollapsed ? 15 : 10;

  return <TableLoading colNumbers={colNumbers} rowNumbers={12} />;
}

export const sortKeyName: { [key: string]: string } = {
  transactionDate: "SortTransactionDate",
  reasonCategory: "SortReasonCategory",
  symbol: "SortSymbol",
  amount: "SortAmount",
  purchaseType: "SortPurchaseType",
  contractNumber: "SortContractNumber",
  bondOriginType: "SortBondOriginType"
};
