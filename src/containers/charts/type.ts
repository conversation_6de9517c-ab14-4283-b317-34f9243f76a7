export interface IDetailChartProps {
  id: string;
  groupName?: string;
  isShowRetrospect?: boolean;
}

export interface IEfficiencyChartProps {
  id: string;
  groupName?: string;
  isShowRetrospect?: boolean;
  isShowExpected?: boolean;
  isShowReal?: boolean;
}

export interface IRenderRecordParams {
  dateTime: string;
  value: number;
  index: number;
  count: number;
  color?: string | null;
}

export type LineChartRecord = { name: string; value: { x: number; y: number }; visible: boolean; type: "area" };

export interface ILineChartProps {
  data: LineChartRecord[];
}

export type ISummaryBoxProps = {
  data: { value: number; color: string }[];
  hasPercent?: boolean;
};
