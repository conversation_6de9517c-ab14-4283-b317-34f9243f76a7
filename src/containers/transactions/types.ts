/* eslint-disable @typescript-eslint/naming-convention */
export interface IInterestsRisks {
  type: string;
  isShowRetrospect: boolean;
  fundId: string;
  onCardClick: (id: string) => void;
}

export type TOrderedRanges = {
  level: number;
  type: number;
  min: number;
  max: number;
};

export type TFundInterestRisk = {
  indexRanges: TOrderedRanges[];
  portfolioValue: number;
  marketValue: number;
  showAlert: boolean;
  duration: number;
  modifiedDuration: number;
};

type FT = { [key: string]: string };

export enum FUND_INTEREST {
  YTM = "YTM",
  YTC = "YTC",
  CONVEXITY = "CONVEXITY",
  DURATION = "DURATION",
  DURATION_MODIFIED = "DURATION_MODIFIED",
  WEIGHTED_AVERAGE = "WEIGHTED_AVERAGE",
  DURATION_COUPON = "DURATION_COUPON",
  DURATION_ZERO_COUPON = "DURATION_ZERO_COUPON"
}

// Focus
export enum FUND_FOCUS {
  OWNERS = "OWNERS",
  HHI = "HHI",
  CR = "CR"
}

export enum FUND_TYPES {
  EFFICIENCY = "efficiency",
  BETA = "beta",
  DEVIATION = "deviation",
  TRAINER = "trainer",
  SHARP = "sharp",
  ALPHA = "alpha",
  ALPHA_ADJUSTED = "alpha_adjusted",
  RISK_VALUE = "riskValue",
  HISTOGRAM = "histogram"
}

export const FUND_LABELS: FT = {
  // Interests
  [FUND_INTEREST.YTM]: "YTM",
  [FUND_INTEREST.YTC]: "YTC پرتفو",
  [FUND_INTEREST.CONVEXITY]: "تحدب پرتفو",
  [FUND_INTEREST.DURATION]: "دیرش",
  [FUND_INTEREST.DURATION_MODIFIED]: "دیرش پرتفو",
  [FUND_INTEREST.WEIGHTED_AVERAGE]: "نرخ بهره بدون ریسک",
  [FUND_INTEREST.DURATION_COUPON]: "دیرش اوراق کوپن‌ دار",
  [FUND_INTEREST.DURATION_ZERO_COUPON]: "دیرش اوراق بدون کوپن",

  // Markets
  [FUND_TYPES.EFFICIENCY]: "بازدهی",
  [FUND_TYPES.BETA]: "ضریب بتا",
  [FUND_TYPES.DEVIATION]: "انحراف معیار",
  [FUND_TYPES.TRAINER]: "ترینر",
  [FUND_TYPES.SHARP]: "شارپ",
  [FUND_TYPES.ALPHA_ADJUSTED]: "آلفای تعدیل‌شده",
  [FUND_TYPES.RISK_VALUE]: "ارزش در معرض خطر (VAR)",

  // Focus
  [FUND_FOCUS.OWNERS]: "درصد مالکیت",
  [FUND_FOCUS.CR]: "CR",
  [FUND_FOCUS.HHI]: "HHI"
};

type IndexRange = {
  type: number;
  level: number;
  min: number;
  max: number;
};

export type YtmData = {
  portfolioValue: number;
  marketValue: number;
  indexRanges: IndexRange[];
};

export type ConvexityData = {
  portfolioValue: number;
  marketValue: number;
  indexRanges: IndexRange[];
};

export type DurationData = {
  duration: number;
  portfolioValue: number;
  marketValue: number;
  modifiedDuration: number;
  indexRanges: IndexRange[];
};

export type PortfolioData = {
  ytm: YtmData;
  convexity: ConvexityData;
  duration: DurationData;
  modifiedDuration: DurationData;
};

export interface PortfolioDataResponse {
  data: PortfolioData;
  isSuccess?: boolean;
  errorCode?: string | null;
  errorMessage?: string | null;
}
