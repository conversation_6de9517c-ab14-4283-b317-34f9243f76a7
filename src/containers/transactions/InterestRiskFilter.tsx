import Checkbox from "@/components/atoms/checkbox/Checkbox";
import { twMerge } from "tailwind-merge";

interface IInterestRiskFilter {
  isShowMarketValue: boolean;
  className?: string;
  onChange: () => void;
}

function InterestRiskFilter(props: IInterestRiskFilter) {
  const { isShowMarketValue, className, onChange } = props;

  return (
    <div className={twMerge("flex items-center gap-5 text-10  ", className)}>
      <div className="flex gap-1 items-center">
        <span className="w-3 h-3 rounded-sm bg-[#8871BA]" />
        <span className="text-[10px] text-[#EFEFEF]">پرتفو</span>
      </div>
      <Checkbox
        checked={isShowMarketValue}
        onChange={onChange}
        text="بازار"
        size="small"
        variant="filledWhite"
        textClassName="text-[10px]"
      />
    </div>
  );
}

export default InterestRiskFilter;
