import { twMerge } from "tailwind-merge";
import { IBox } from "./types";

function Box({ title, children, className, bodyClass, compact, titleClassName }: IBox) {
  return (
    <div className={twMerge("rounded", className)}>
      {title && (
        <div
          className={twMerge(
            "rounded-t border-b border-white/10 bg-card-bg bg-backgroundBodyBackground text-xs pr-2",
            compact ? "pb-1 pt-3px font-normal" : "pt-2 pb-[7px]",
            titleClassName
          )}
        >
          {title}
        </div>
      )}
      <div className={twMerge("w-full", bodyClass)}>{children}</div>
    </div>
  );
}

export default Box;
