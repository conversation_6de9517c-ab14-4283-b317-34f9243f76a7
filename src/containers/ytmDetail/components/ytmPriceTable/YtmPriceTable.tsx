import "ag-grid-community/styles/ag-grid.css";
import "ag-grid-community/styles/ag-theme-alpine.css";

import { AgGridReact } from "ag-grid-react";
import { twMerge } from "tailwind-merge";
import { memo, useRef } from "react";
import { getRowIndex } from "@/components/organisms/Table/utils";
import useValidateChartData from "@/hooks/useValidateChartData";
import { IYtmPriceTable } from "@/containers/ytmDetail/components/ytmPriceTable/types";
import styles from "./YtmPriceTable.module.scss";
import { columnDefs, gridOptions } from "./utils";
import { TOrderBookRow } from "../../types";

function YtmPriceTable({ data }: IYtmPriceTable) {
  const gridRef = useRef<AgGridReact<TOrderBookRow> | null>(null);
  useValidateChartData<TOrderBookRow>(data, gridRef);

  return (
    <div className={twMerge(styles.ytmTableContainer, "ag-theme-alpine")}>
      <AgGridReact<TOrderBookRow>
        animateRows
        enableRtl
        ref={gridRef}
        enableCellChangeFlash
        getRowId={getRowIndex}
        rowHeight={22}
        headerHeight={30}
        rowData={data}
        gridOptions={gridOptions}
        columnDefs={columnDefs}
        getRowClass={params => params?.data?.type}
      />
    </div>
  );
}

export default memo(YtmPriceTable);
