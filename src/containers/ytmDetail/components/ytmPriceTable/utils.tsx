import { ColDef, ColGroupDef, ICellRendererParams } from "ag-grid-community";
import { commaSeparator, numberWithCommas } from "@/utils/helpers";
import { TOrderBookRow } from "../../types";

const cellCommaSeparatorRender = ({ value }: { value: string }) => <span>{commaSeparator(value)}</span>;
const cellYtmColorRender = (params: any) => {
  const data: TOrderBookRow = params?.data;
  const { type = "sell" } = data;
  const value = params?.value;

  return <span className={type === "buy" ? "text-textAscending" : "text-textDescending"}>{value}</span>;
};

export const columnDefs: (ColDef | ColGroupDef)[] = [
  {
    field: "price",
    headerName: "قیمت",
    cellRenderer: cellCommaSeparatorRender,
    // headerClass: "ltr",
    flex: 1
  },
  {
    field: "volume",
    headerName: "حجم",
    flex: 1,
    cellRenderer: cellCommaSeparatorRender
    // cellRenderer: "VolumeCellRenderer"
  },
  {
    field: "ytm",
    headerName: "YTM",
    flex: 1,
    cellClass: "text-success",
    cellRenderer: cellYtmColorRender
    // cellRenderer: "YtmCellRenderer"
  }
];

export function ProgressCellRenderer(props: ICellRendererParams) {
  const { data, colDef } = props;
  const isBuy = colDef?.field === "buyPrice";
  const progress = data[isBuy ? "buyPercent" : "sellPercent"];
  const value = data[isBuy ? "buyPrice" : "sellPrice"];

  return (
    <div style={{ direction: isBuy ? "ltr" : "rtl" }}>
      <div
        className={`${isBuy ? "bg-success/20" : "bg-prompt/20"} ${!progress && "!bg-white"} absolute h-full`}
        style={{ width: `${progress}%` }}
      />
      <div style={{ position: "absolute" }}>{numberWithCommas(value) || ""}</div>
    </div>
  );
}

export function VolumeCellRenderer(props: ICellRendererParams) {
  const { value } = props;
  if (value === undefined) return "";
  return numberWithCommas(value);
}

export function YtmCellRenderer(props: ICellRendererParams) {
  const { data, colDef } = props;
  const isBuy = colDef?.field === "buyYtm";
  const value = isBuy ? data?.buyYtm : data?.sellYtm;
  return value ? `${(value * 100)?.toFixed(3)}٪` : <p className="text-black" />;
}
function changeRowColor(params: any) {
  const data: TOrderBookRow = params?.data;
  const { type, percent } = data;

  const color = type === "sell" ? "rgba(255, 94, 94, 0.5)" : "rgba(58, 74, 39, 0.75)";
  if (percent > 50) {
    return { background: `linear-gradient(270deg, ${color} ${percent}%, transparent ${100 - percent}%)` };
  }
  return { background: `linear-gradient(90deg, transparent ${100 - percent}%, ${color} ${percent}%)` };
}

export const gridOptions = {
  rowHeight: 32.079,
  suppressRowHoverHighlight: true,
  headerHeight: 40,
  overlayLoadingTemplate: "در حال بارگزاری ...",
  overlayNoRowsTemplate: "داده وجود ندارد!",
  getRowStyle: changeRowColor
};
