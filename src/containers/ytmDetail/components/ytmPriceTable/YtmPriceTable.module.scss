.ytmTableContainer {
  font-family: "yekan-bakh" !important;
  --ag-font-size: 12px;
  --ag-cell-horizontal-padding: 7px !important;
  --ag-border-color: rgba(var(--idle), 0.1);
  --ag-row-border-color: none;
  --ag-odd-row-background-color: none;
  --ag-header-background-color: none !important;
  --ag-font-family: "yekan-bakh" !important;
  --ag-row-border-width: 0 !important;
  --ag-background-color: #1f1f22 !important;
  --ag-data-color: #f4f4f4 !important;
  @apply p-3 pt-0 bg-backgroundDarkRow rounded-b;

  :global(.ag-root-wrapper-body.ag-layout-normal) {
    @apply h-auto;
  }

  :global(.ag-header-cell-label) {
    @apply self-end text-.8xs text-mediumGray;
  }

  :global(.ag-body) {
    @apply min-h-16;
  }

  :global(.ag-overlay-panel) {
    @apply pt-8;
  }

  :global(.ag-body.ag-layout-normal) {
    @apply pt-1;
  }

  :global(.ag-root-wrapper) {
    @apply border-0;
  }

  :global(.ag-row-last) {
    @apply rounded-b;
  }

  :global(.ag-row-first) {
    @apply rounded-t;
  }
}
