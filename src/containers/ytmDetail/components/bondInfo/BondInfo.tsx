import { twMerge } from "tailwind-merge";
import { ReactNode } from "react";
import { ListItem } from "./type";

interface PublicationInfoProps<TData> {
  data?: TData;
  list?: ListItem[];
  compact?: boolean;
}

function BondInfo<TData>({ data, list, compact }: PublicationInfoProps<TData>) {
  return (
    <div className="grid grid-cols-2 rounded-b overflow-hidden">
      {list?.map(item => (
        <div
          key={item?.name}
          className={twMerge(
            "flex justify-between",
            item?.zebra ? "bg-backgroundLightRow" : "bg-backgroundDarkRow",
            compact ? "px-2 pb-1 pt-3px" : "pt-[11px] pr-2 pb-3 pl-[9px]"
          )}
        >
          <div className="text-.8xs font-normal text-secondaryText">{item.name}</div>
          <div className={twMerge("text-xs", item?.rtl ? "" : "ltr")}>
            {item?.formatter
              ? (item?.formatter(data?.[item.key as keyof TData]) as ReactNode)
              : (data?.[item.key as keyof TData] as ReactNode)}
          </div>
        </div>
      ))}
    </div>
  );
}

export default BondInfo;
