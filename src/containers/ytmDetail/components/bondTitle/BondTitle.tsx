import { twMerge } from "tailwind-merge";
import { numberWithCommas } from "@/utils/helpers";
import { BondTitleProps } from "./type";

function BondTitle({ data, compact }: BondTitleProps) {
  const change = Number(
    data?.lastTradePrice !== undefined && data?.closePrice !== undefined
      ? Number(data?.lastTradePrice) - Number(data?.bondLastDayTrade?.closePrice)
      : undefined
  );
  const changePercent =
    change !== undefined &&
    data?.closePrice !== undefined &&
    ((change / Number(data?.bondLastDayTrade?.closePrice)) * 100)?.toFixed(3);
  const changePercentText = changePercent !== "NaN" ? `(${changePercent}%)` : "";

  return (
    <div
      className={twMerge(
        "flex justify-between py-2.25 border-b border-b-cardBackground rounded-t-lg",
        compact ? "pr-2 pl-1 bg-bodyBackground" : "pr-4 pl-4 items-center bg-backgroundCardBackground"
      )}
    >
      <div className={compact ? "block" : "flex items-center"}>
        {compact ? (
          <span data-test="88731e32-51d1-4419-a26c-1eb263ecfa4b" className="pl-1 text-mainText text-sm font-bold">
            {data?.symbol}
          </span>
        ) : (
          <span className="pl-1 text-mainText text-sm font-bold">{data?.symbol}</span>
        )}
        <span className={twMerge("text-xs font-normal text-secondaryText", compact && "block pt-1")}>
          (پایه فرابورس: {data?.instrumentName})<span className={twMerge("pr-1", !compact && "text-sm")}>IFB</span>
        </span>
        {!compact && (
          <div className="flex flex-row-reverse border-r border-bodyBackground mr-3 pr-3 items-end">
            {!data?.lastTradePrice && "---"}

            <span className="px-2 text-.8xs pr-8">
              <span className="px-1">YTM</span> {data?.lastTradePriceYtmInPercent?.toFixed(2)}٪
            </span>

            <div
              className={twMerge(
                "inline-flex text-sm items-end",
                // eslint-disable-next-line no-nested-ternary
                change === 0 || !data?.lastTradePrice
                  ? "text-gray-500"
                  : change > 0
                    ? "text-textAscending"
                    : "text-error"
              )}
            >
              {data?.lastTradePrice && (
                <>
                  <span className="pl-1 text-sm font-bold text-mainText">{numberWithCommas(data?.lastTradePrice)}</span>
                  {data?.lastTradePrice && changePercent ? (
                    <span className="ltr pr-1 text-.9xs">
                      {changePercentText} {numberWithCommas(change)}
                    </span>
                  ) : (
                    ""
                  )}
                </>
              )}
            </div>
          </div>
        )}
      </div>
      <div className="text-xs">
        {data?.bondType !== 200 ? (
          <span className="text-textAscending text-sm">
            {!compact && <span className="relative top-0.5"> &#10004;</span>}

            <span className={twMerge("pr-1", compact ? "text-[#26D5C0] pl-1.5 text-.8xs" : "text-mainText text-xs")}>
              {data?.bondTypeName}{" "}
            </span>
          </span>
        ) : (
          // <span className="pr-4 opacity-30">-</span>
          <span className={twMerge("pr-1", compact ? "pl-1.5 text-.8xs !text-[#db7916]" : "text-mainText text-xs")}>
            {data?.bondTypeName}{" "}
          </span>
        )}
      </div>
    </div>
  );
}

export default BondTitle;
