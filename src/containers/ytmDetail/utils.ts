// todo this line is just for demo.

import { CHART_TIME } from "@/containers/tradeHistory/util";
import { IOrderBook } from "@/queries/bondAPI/types";
import { dateConverter } from "@/utils/DateHelper";
import { commaSeparator, inMillion } from "@/utils/helpers";
import { AxisLabelsFormatterContextObject } from "highcharts";
import HighchartsReact from "highcharts-react-official";
import { MutableRefObject, useEffect } from "react";
import { TOrderBookRow, YtmDetail } from "./types";

export const publicationList = [
  { name: "مبلغ انتشار", key: "totalSheetsAmount", formatter: (e: string) => (e ? `${commaSeparator(e)} M` : "---") },
  { name: "مبلغ اسمی", key: "faceValue", formatter: (e: string) => commaSeparator(e) },
  { name: "نرخ کوپن", key: "nominalInterestRateInPercent", zebra: true, formatter: (e: string) => (e ? `${e}%` : "-") },
  {
    name: " مواعد پرداخت سود",
    key: "paymentCount",
    zebra: true,
    formatter: (e: string) => (e ? `  ${e} ماه ` : "-"),
    rtl: true
  },
  { name: "سررسید", key: "maturityDate", formatter: (e: string) => (e ? dateConverter(e).format("YYYY/M/DD") : "-") },
  {
    name: "اولین پرداخت سود",
    key: "firstPaymentDate",
    formatter: (e: string) => (e ? dateConverter(e).format("YYYY/M/DD") : "-")
  }
];
export const AdditionalInfo = [
  { name: "آخرین قیمت ", key: "lastTradePrice", formatter: (e: string) => commaSeparator(e) },
  { name: "قیمت پایانی", key: "closePrice", formatter: (e: string) => commaSeparator(e) },
  { name: "قیمت بازگشایی", key: "openingPrice", formatter: (e: string) => commaSeparator(e), zebra: true },
  { name: "حجم معاملات", key: "totalTradedVolume", formatter: (e: string) => commaSeparator(e), zebra: true },
  { name: "بالاترین قیمت", key: "dayHighPrice", formatter: (e: string) => commaSeparator(e) },
  { name: "ارزش معاملات", key: "totalTradeValue", formatter: (e: string) => inMillion(e) },
  { name: "پایین‌ترن قیمت", key: "dayLowPrice", formatter: (e: string) => commaSeparator(e), zebra: true },
  { name: "تعداد معاملات", key: "totalNumberOfTrades", formatter: (e: string) => commaSeparator(e), zebra: true }
];
export const renderTooltip = (point: any, data: YtmDetail[]) => `
  <div class="linechart-tooltip">
    ${`
        <div style="width: 169px;text-align: center">
          <div class="pb-1">${dateConverter(new Date(Number(point.x))).format("YYYY/M/DD")}</div>
          <div class="flex justify-between flex-row-reverse"><div>:YTM آخرین</div> <div>${data?.[point?.point?.index]
            ?.lastTradePriceYtmToPercent} ٪</div></div>
          ${
            point.point.low
              ? `<div class="flex justify-between flex-row-reverse"><div>:YTM بیشترین</div> <div>${data?.[
                  point?.point?.index
                ]?.lowPriceYtmToPercent}٪</div></div>`
              : ""
          }
          ${
            point.point.high
              ? `<div class="flex justify-between flex-row-reverse"><div>:YTM کمترین</div><div> ${data?.[
                  point?.point?.index
                ]?.highPriceYtmToPercent}٪</div></div>`
              : ""
          }
        </div>
    `}
  </div>
`;

const maxValue = (arr: number[]) => arr.reduce((max, val) => (max > val ? max : val), 0);
const calcHighlightPercent = (value: number, max: number) => {
  const v = (value * 100) / max;
  return v;
};

export const renderOrderData = (orderBookData: IOrderBook[]): TOrderBookRow[] => {
  const sellList = orderBookData.filter(i => i.sellAmount !== 0);
  const buyList = orderBookData.filter(i => i.buyAmount !== 0);

  const sellVolumeValues = sellList.map(i => i.sellVolume);
  const buyVolumeValues = sellList.map(i => i.buyVolume);
  const maxVolumeValue = maxValue(sellVolumeValues.concat(buyVolumeValues));

  const sellSortedList: TOrderBookRow[] = sellList
    .sort((a, b) => a.sellPrice - b.sellPrice)
    .map(i => ({
      type: "sell",
      volume: i.sellVolume,
      price: i.sellPrice,
      ytm: i.sellPriceYtmToPercent,
      index: 0,
      percent: calcHighlightPercent(i.sellVolume, maxVolumeValue)
    }));

  const buySortedList: TOrderBookRow[] = buyList
    .sort((a, b) => b.buyPrice - a.buyPrice)
    .map(i => ({
      type: "buy",
      volume: i.buyVolume,
      price: i.buyPrice,
      ytm: i.buyPriceYtmToPercent,
      index: 0,
      percent: calcHighlightPercent(i.buyVolume, maxVolumeValue)
    }));

  const emptyRow: TOrderBookRow = {
    type: "empty",
    index: 0,
    percent: 0
  };

  return sellSortedList
    .concat(emptyRow)
    .concat(buySortedList)
    .map((i, index) => ({ ...i, index }));
};

export const useReflowChart = (chartRef: MutableRefObject<HighchartsReact.RefObject | null>) => {
  useEffect(() => {
    const timer = setTimeout(() => {
      const chart = chartRef?.current?.chart;
      chart?.reflow();
    }, 5);
    return () => {
      clearTimeout(timer);
    };
  }, []);
};
const getTickInterval = (time: string | number) => {
  switch (time) {
    case CHART_TIME.Weekly:
    case CHART_TIME.Month1:
      // daily
      // 7 days
      return 24 * 3600 * 1000;
    case CHART_TIME.Month3:
    case CHART_TIME.Month6:
    case CHART_TIME.Yearly:
      return 24 * 3600 * 1000 * 30;
    default:
      // monthly
      return 24 * 3600 * 1000;
  }
};

const getChartDateTime = (time: string | number) => {
  switch (time) {
    case CHART_TIME.Month1:
      // 7 days
      return "M/D";
    case CHART_TIME.Weekly:
      return "YYYY/M/D";
    case CHART_TIME.Month3:
    case CHART_TIME.Month6:
    case CHART_TIME.Yearly:
      return "MMMM";
    default:
      // monthly
      return "YYYY/M/D";
  }
};
export const renderInitialOptions = ({
  data,
  ranges,
  time
}: {
  data: YtmDetail[];
  ranges: (number | undefined)[][] | undefined;
  time: string | number;
  // sortedData?: TGetTradeHistoryResponse[];
}) => ({
  chart: {
    backgroundColor: "#1F1F22",
    zooming: {
      type: "x",
      resetButton: {
        theme: {
          fill: "#F4F4F4",
          stroke: "transparent",
          paddingLeft: 12,
          paddingRight: 12,
          border: 0,
          r: 4,
          style: {
            color: "#0C82F9",
            fontFamily: "yekan-bakh",
            fontSize: "14px"
          },
          states: {
            hover: {
              fill: "#0C82F9",
              style: {
                color: "#F4F4F4"
              }
            }
          }
        }
      }
    },
    paddingTop: 10,
    marginLeft: 95,
    marginBottom: 75
  },
  boost: {
    useGPUTranslations: true,
    // Chart-level boost when there are more than 41 series in the chart
    seriesThreshold: 41
  },
  credits: {
    enabled: false
  },
  exporting: {
    enabled: false, // for hiding default export button (hamburger icon)
    chartOptions: {
      legend: {
        enabled: true
      },
      navigator: {
        enabled: false
      },
      scrollbar: {
        enabled: false
      },
      chart: {
        backgroundColor: "#ffffff"
      }
    }
  },
  title: {
    text: ""
  },
  legend: {
    enabled: false
  },
  plotOptions: {
    series: {
      marker: {
        enabled: false
      },
      states: {
        hover: {
          enabled: false,
          lineWidth: 0,
          size: 0,
          lineWidthPlus: 0,
          halo: {
            lineWidth: 0,
            size: 0,
            lineWidthPlus: 0
          },
          inactive: {
            enabled: false
          }
        }
      }
    }
  },
  tooltip: {
    crosshairs: true,
    shared: true,
    backgroundColor: null,
    borderWidth: 0,
    shadow: false,
    useHTML: true,
    formatter() {
      // return renderTooltip(this, sortedData);
      return renderTooltip(this, data);
    }
  },
  yAxis: {
    gridLineColor: "#545454",
    gridLineWidth: 1,
    labels: {
      useHTML: true,
      formatter(): string {
        const item = this as unknown as AxisLabelsFormatterContextObject;

        if (typeof item.value !== "number") {
          const label = item.axis.defaultLabelFormatter.call(item);

          return `<text>${label}</text>`;
        }
        return `${item.value?.toFixed(3)}٪`;
      },
      style: {
        color: "#F4F4F4"
      }
    },
    plotLines: [
      {
        color: "#26D5C0",
        width: 1,
        value: data?.[Number(data?.length) - 1]?.y,
        zIndex: 5,
        dashStyle: "Dash"
      }
    ],
    title: {
      text: ""
    }
  },
  xAxis: {
    type: "datetime",
    tickInterval: getTickInterval(time),
    labels: {
      /* @ts-ignore */
      formatter() {
        /* @ts-ignore */
        const d = new Date(this.value);
        const format = getChartDateTime(time);
        return dateConverter(d).locale("fa-IR").format(format);
      },
      style: {
        color: "#F4F4F4"
      }
    },
    tickWidth: 0,
    lineWidth: 1,
    lineColor: "#545454"
  },
  series: [
    {
      name: "Range",
      data: ranges,
      type: "arearange",
      zIndex: 0,
      marker: {
        enabled: false
      },
      linkedTo: ":previous",
      lineWidth: 0,
      color: {
        linearGradient: {
          x1: 0,
          x2: 0,
          y1: 0,
          y2: 1
        },
        stops: [
          [0, "rgba(20, 111, 100, 0.4) -23.62%"],
          [0.5, "rgba(38, 213, 192, 0.4) 62.17%"],
          [1, "rgba(20, 111, 100, 0.4) 153.69%"]
        ]
      }
    },
    {
      color: "#26D5C0",
      data,
      name: "كل بازار بورس",
      type: "line",
      zIndex: 1,
      lineWidth: 4,
      dataLabels: {
        enabled: true,
        borderRadius: 6,
        borderWidth: 1,
        borderColor: "white",
        backgroundColor: "#26D5C0",
        y: -16,
        style: {
          textOutline: false
        },
        distance: -50,
        className: "font-yekan",
        filter: {
          property: "index",
          operator: "==",
          value: data.length - 1
        },
        // eslint-disable-next-line
        formatter: function () {
          return `${Number(this.y?.toFixed(3))} ٪`;
        }
      },
      marker: {
        enabled: false
      }
    }
  ]
});
