import ChipsSelect from "@/components/molecules/chipsSelect/ChipsSelect";
import { IChipsSelectItem } from "@/components/molecules/chipsSelect/type";
import { DatePickerWrapper } from "@/components/organisms/datePicker";
import { IDatePickerWrapperRefProps } from "@/components/organisms/datePicker/types";
import socketUrls from "@/constants/socketUrls";
import {
  CHART_TIME,
  getNow,
  getPeriodDate,
  getPrevDate,
  getTooltipCalendar,
  tradeChipsList
} from "@/containers/tradeHistory/util";
import { IBondTableSocket } from "@/containers/watchList/types";
import { TGetTradeHistoryResponse, YtmDetailContainerParams } from "@/containers/ytmDetail/types";
import useSignalR from "@/hooks/useSignalR/useSignalR";
import { ORDER_TABLE, TBondData, useGetBondChartQuery, useGetOrderBookQuery } from "@/queries/bondAPI";
import { I<PERSON>rderBook, TBondResponse, TOrderBokResponse } from "@/queries/bondAPI/types";
import { dateConverter } from "@/utils/DateHelper";
import useDelayedStack from "@reactutils/use-delayed-stack";
import { useQueryClient } from "@tanstack/react-query";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import highchartsMore from "highcharts/highcharts-more";
import { useEffect, useMemo, useRef, useState } from "react";
import { twMerge } from "tailwind-merge";
import styles from "./BondDetailContainer.module.scss";
import { BondInfo, BondTitle, Box, YtmPriceTable } from "./components";
import { AdditionalInfo, publicationList, renderInitialOptions, renderOrderData, useReflowChart } from "./utils";

require("highcharts/modules/mouse-wheel-zoom")(Highcharts);

highchartsMore(Highcharts);

if (Highcharts && Highcharts.setOptions) {
  Highcharts.setOptions({
    lang: {
      resetZoom: "لغو بزرگ نمایی"
    }
  });
}

function BondDetailContainer({ id }: YtmDetailContainerParams) {
  const queryClient = useQueryClient();
  const initialStartDate = getPrevDate(CHART_TIME.Yearly);

  const [period, setPeriod] = useState<string | number>(CHART_TIME.Yearly);
  const [fromDate, setFromDate] = useState<string>(initialStartDate);
  const [toDate, setToDate] = useState<string>(getPrevDate(""));
  const [bond, setBond] = useState<TBondData | undefined>(undefined);

  const [activeChip, setActiveChip] = useState(tradeChipsList[2]);
  const myTooltip = getTooltipCalendar(fromDate, toDate, activeChip);

  const [initialDate, setInitialDate] = useState<Date[] | undefined>();
  const datePickerRef = useRef<IDatePickerWrapperRefProps>(null);

  useSignalR(
    socketUrls.orderBookUrl,
    socketUrls.BondDetailLastState,
    {
      next: (message: TBondResponse) => {
        if (message?.data?.isin) setBond(message?.data);
      },
      error: () => {}
    },
    id,
    true,
    true
  );

  useSignalR(socketUrls.priceChangeYtmUrl, socketUrls.priceChangeYtmStreamName, {
    next: ({ message: { instrument, limitOrders, ...restMessages } }: IBondTableSocket) => {
      if (instrument.isin === id && Object.keys(restMessages).length) {
        setBond(prev => ({ ...prev, ...restMessages }) as TBondData);
      }
    },
    error: () => {}
  });

  const { data: chart } = useGetBondChartQuery({
    id,
    fromDate,
    toDate
  });

  const { data: orderBook } = useGetOrderBookQuery(id);
  const { data: orderBookData = [] } = orderBook || {};
  const orderList = useMemo(() => renderOrderData(orderBookData), [orderBookData]);

  const [pushToStack] = useDelayedStack((items: IOrderBook[][]) => {
    const cacheData = queryClient.getQueryData<TOrderBokResponse>([ORDER_TABLE, id]);
    const updatedData = { ...cacheData, data: items[items.length - 1] };
    queryClient.setQueryData([ORDER_TABLE, id], updatedData);
  }, 300);

  useSignalR(
    socketUrls.orderBookUrl,
    socketUrls.orderBookStreamName,
    {
      next: (items: IOrderBook[]) => pushToStack(items),
      error: () => {}
    },
    id
  );

  const onSwitch = (v: IChipsSelectItem, firstRender?: boolean) => {
    // using if to stop repetitive user click
    if (v.id !== activeChip.id || firstRender) {
      // convert enum to date
      const date = getPrevDate(v.id);
      const now = getNow();
      setActiveChip(v);

      if (v.id !== CHART_TIME.DateRange) {
        setPeriod(v.id);
        setFromDate(date);
        setToDate(now);
        setInitialDate(undefined);
      }
    }
  };

  useEffect(() => {
    onSwitch(tradeChipsList[2], true);
  }, []);

  const onCancelCalendar = () => {
    setInitialDate(undefined);
    onSwitch(tradeChipsList[2]);
  };

  const sortedData = chart?.data
    // @ts-ignore
    ?.map((item: TGetTradeHistoryResponse) => ({ ...item, x: new Date(item?.tradeDate).getTime() }))
    ?.sort((a: { x: number }, b: { x: number }) => Number(a?.x) - Number(b?.x));
  const modifiedData = sortedData?.map((item: TGetTradeHistoryResponse) => ({
    ...item,
    x: item?.x,
    y: item?.lastTradePriceYtmToPercent,
    volume: item?.volume
  }));

  const ranges = sortedData?.map((item: TGetTradeHistoryResponse) => [
    item?.x,
    item?.highPriceYtmToPercent,
    item?.lowPriceYtmToPercent
  ]);
  const chartRef = useRef<HighchartsReact.RefObject | null>(null);
  useReflowChart(chartRef);

  return (
    <div className="px-4 bg-backgroundBodyBackground flex gap-1 h-full w-full">
      <div className="bg-backgroundCardBackground px-2 py-2 rounded w-[326px]">
        <Box title="اطلاعات انتشار">
          <BondInfo<TBondData> list={publicationList} data={bond} />
        </Box>
        <Box className="pt-1" title="اطلاعات تکمیلی اوراق">
          <BondInfo<TBondData> list={AdditionalInfo} data={bond} />
        </Box>
        <Box className="pt-1" titleClassName="pr-3" title="مظنه">
          <YtmPriceTable data={orderList} />
        </Box>
      </div>
      <div className="rounded grow flex flex-col">
        <BondTitle data={bond} />
        <div className="bg-backgroundCardBackground mt-1 rounded-b-lg grow flex flex-col">
          <div className="flex justify-between items-center p-2">
            <div className="text-xs text-mainText">
              <div>
                <span className="pl-2">آخرین معامله:</span>
                {bond?.lastTradeTime} - {dateConverter(bond?.lastTradeDate || "").format("YYYY/MM/DD")}
              </div>
            </div>
            <div className="relative flex items-center py-1/2 rounded text-base shadow-tradeChipsSelect bg-bodyBackground">
              <div data-test="c2232289-98fc-420f-902b-6d84697b01b2">
                <DatePickerWrapper
                  isRange
                  hasFooter={false}
                  initialValue={initialDate}
                  ref={datePickerRef}
                  config={{
                    maxDate: new Date()
                  }}
                  className="-top-[6px]"
                  onCancel={onCancelCalendar}
                  onChange={(date?: Date[]) => {
                    if (date && date[0] && date[1] && date !== initialDate) {
                      setFromDate(dateConverter(date[0]).calendar("gregory").format("YYYY-MM-DD"));
                      setToDate(dateConverter(date[1]).calendar("gregory").format("YYYY-MM-DD"));

                      const range = getPeriodDate(date);
                      setPeriod(range);

                      setInitialDate(date);
                      setFromDate(dateConverter(date[0]).calendar("gregory").format("YYYY-MM-DD"));
                      setToDate(dateConverter(date[1]).calendar("gregory").format("YYYY-MM-DD"));
                      datePickerRef.current?.close();

                      const f = tradeChipsList.find(i => i.id === CHART_TIME.DateRange);

                      if (f) {
                        onSwitch(f);
                      }
                    }
                  }}
                >
                  {myTooltip}
                </DatePickerWrapper>
              </div>
              <ChipsSelect
                activeChip={activeChip}
                items={tradeChipsList}
                initialSelect={tradeChipsList[tradeChipsList.length - 1]}
                onSwitch={onSwitch}
              />
            </div>
          </div>
          <div className={twMerge("mx-auto w-full px-2 pb-2 grow", styles.lineChart)}>
            {chart?.data && modifiedData && (
              <HighchartsReact
                containerProps={{ className: "h-full min-h-[500px]" }}
                highcharts={Highcharts}
                options={renderInitialOptions({ data: modifiedData, ranges, time: period })}
                ref={chartRef}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default BondDetailContainer;
