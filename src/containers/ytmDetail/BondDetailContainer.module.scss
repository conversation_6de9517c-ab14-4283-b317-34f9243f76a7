.lineChart {
  :global(.linechart-tooltip) {
    @apply w-fit rounded-lg border p-2 opacity-90 bg-cardBackground text-white text-sm font-bold font-yekan;
    font-family: "yekan-bakh";
  }

  :global(.highcharts-yaxis-labels) {
    span {
      @apply font-yekan font-normal #{!important};
    }
  }

  :global(.highcharts-xaxis-labels) {
    text {
      @apply font-yekan font-bold #{!important};
    }
  }

  :global(.highcharts-container) {
    @apply w-full max-w-full rounded bg-backgroundDarkRow pt-2 #{!important};
  }

  :global(.highcharts-root) {
    @apply w-full #{!important};
  }
}
