export type TGetTradeHistoryResponse = {
  isin: string;
  tradesCount: number;
  TotalNumberOfTrades: number;
  value: number;
  volume: number;
  highPrice: number;
  highPriceYtm: number;
  lowPrice: number;
  lowPriceYtm: number;
  closePrice: number;
  closePriceYtm: number;
  openingPrice: number;
  openingPriceYtm: number;
  lastTradePrice: number;
  lastTradePriceYtm: number;
  tradeDate: string;
  x?: number;
  highPriceYtmToPercent?: number;
  lowPriceYtmToPercent?: number;
  closePriceYtmToPercent?: number;
  lastTradePriceYtmToPercent?: number;
};
export interface YtmDetailContainerParams {
  id: string;
}

export type TOrderBookRow = {
  type: "buy" | "sell" | "empty";
  volume?: number;
  price?: number;
  ytm?: number;
  index: number;
  percent: number;
};

export interface YtmDetail extends TGetTradeHistoryResponse {
  y?: number;
}
