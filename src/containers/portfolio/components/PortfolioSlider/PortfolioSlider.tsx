/* eslint-disable jsx-a11y/control-has-associated-label */

import React, { useRef, useState } from "react";
import ArrowRight from "@/assets/icons/box-arrow-right.svg";
import { twMerge } from "tailwind-merge";
import ArrowLeft from "@/assets/icons/box-arrow-left.svg";
import { Swiper, SwiperSlide } from "swiper/react";
import { Keyboard, Mousewheel, Navigation } from "swiper/modules";
import { Swiper as SwiperClass } from "swiper/types";
import { convertIntToCurrency } from "@/utils/helpers";
import { IPiePortfo } from "@/queries/FundChartsAPI/types";
import randomHexColorCode from "./utils";
import "swiper/swiper-bundle.css";

function PortfolioSlider({ data }: { data: IPiePortfo[] }) {
  const [isBeginning, setIsBeginning] = useState(true);
  const [isEnd, setIsEnd] = useState(false);
  const swiperRef = useRef<SwiperClass | null>(null);
  const updateNavigationState = (swiper: SwiperClass) => {
    setIsBeginning(swiper.isBeginning);
    setIsEnd(swiper.isEnd);
  };

  const handlePrevSlide = () => {
    if (swiperRef.current) {
      swiperRef.current.slidePrev();
      // Update state immediately after slide action
      setTimeout(() => updateNavigationState(swiperRef.current!), 50);
    }
  };

  const handleNextSlide = () => {
    if (swiperRef.current) {
      swiperRef.current.slideNext();
      // Update state immediately after slide action
      setTimeout(() => updateNavigationState(swiperRef.current!), 50);
    }
  };

  const handleSlideChange = (swiper: SwiperClass) => {
    updateNavigationState(swiper);
  };

  const handleSlideChangeTransitionEnd = (swiper: SwiperClass) => {
    // Ensure state is updated after transition completes
    updateNavigationState(swiper);
  };
  return (
    <>
      <div className="flex justify-between items-center pt-2 px-2">
        <div className="text-sm font-bold">
          {data?.length ?? 0}
          <span className="text-xs font-normal">نماد</span>
        </div>

        <div className="flex gap-2 items-center ltr">
          <button
            type="button"
            onClick={handlePrevSlide}
            disabled={isBeginning}
            className={`bg-transparent hover:bg-transparent focus:bg-transparent rounded-full flex items-center justify-center transition-colors `}
          >
            <ArrowLeft
              isActive={!isBeginning}
              className={twMerge(!isBeginning ? "text-[#1ACD81]" : "text-[#858585]")}
            />
          </button>

          <button
            type="button"
            onClick={handleNextSlide}
            disabled={isEnd}
            className={`bg-transparent hover:bg-transparent focus:bg-transparent rounded-full flex items-center justify-center transition-colors `}
          >
            <ArrowRight isActive={!isEnd} className={twMerge(!isEnd ? "text-[#1ACD81]" : "text-[#858585]")} />
          </button>
        </div>
      </div>

      <div className="relative w-full">
        <div className="rounded-lg p-2 pr-4">
          <Swiper
            dir="rtl"
            slidesPerView="auto"
            keyboard
            grabCursor
            spaceBetween={8}
            modules={[Navigation, Keyboard, Mousewheel]}
            onSwiper={swiper => {
              swiperRef.current = swiper;
              updateNavigationState(swiper);
            }}
            onSlideChange={handleSlideChange}
            onSlideChangeTransitionEnd={handleSlideChangeTransitionEnd}
          >
            {data?.map((i, index) => (
              <SwiperSlide key={i?.isin} className="min-w-[190x] max-w-[200px]">
                <div className="bg-[#343438] pt-[9px] rounded-xl text-[#EFEFEF]">
                  <div className="flex justify-between px-3 pb-3">
                    <div className="flex">
                      <div
                        style={{ background: randomHexColorCode(index), borderColor: "rgba(0, 0, 0, 0.5)" }}
                        className="rounded-full w-[14px] h-[14px] border-[3px] relative top-1"
                      />
                      <div className="text-sm pr-1.5">{i?.symbol}</div>
                    </div>
                    <div className="font-bold">{i?.portfolioWeightInPercents?.toFixed(2)}%</div>
                  </div>
                  <div className="flex px-3 pt-1.5 pb-1 justify-between border-t-1 border-dark_black7 items-center">
                    <div className="flex">
                      <div className="text-.8xs">ارزش نماد</div>
                    </div>
                    <div>
                      {convertIntToCurrency(i?.portfolioSheetsValue)?.value}
                      <span className="px-0.5">{convertIntToCurrency(i?.portfolioSheetsValue)?.unit}</span>
                    </div>
                  </div>
                </div>
              </SwiperSlide>
            ))}
          </Swiper>
        </div>
      </div>
    </>
  );
}

export default PortfolioSlider;
