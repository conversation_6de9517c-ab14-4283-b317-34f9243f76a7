import React, { useMemo, useState } from "react";
import Input from "@/components/atoms/input";
import Search from "@/assets/icons/detail-search.svg";
import CollapsibleTable from "@/components/organisms/Table/CollapsibleTable/CollapsibleTable";
import { useGetPortfolioWithPurchaseDetail } from "@/queries/FundChartsAPI";
import { ColDef } from "@ag-grid-community/core";
import Pagination from "@/components/molecules/pagination/Pagination";
import { LoadingRenderer, columnDefs, getRowId, renderChildTableConfig } from "./utils";

function PortfolioTable() {
  const [filterText, setFilterText] = useState("");
  const [pageNumber, setPageNumber] = useState(0);
  const [pageSize, setPageSize] = useState(15);

  const onSearch = (value: string) => {
    setFilterText(value);
  };
  const { data: portfolioWithPurchaseData, isLoading } = useGetPortfolioWithPurchaseDetail({
    pageSize,
    pageNumber: pageNumber + 1,
    symbol: filterText
  });
  const columnDefsData = useMemo(() => columnDefs(), []);
  const defaultColDef = useMemo<ColDef>(
    () => ({
      flex: 1
    }),
    []
  );
  const totalCount = portfolioWithPurchaseData?.data?.items?.length;
  const detailCellRendererParams = useMemo(() => renderChildTableConfig(), []);

  const tableData = useMemo(
    () =>
      portfolioWithPurchaseData?.data?.items?.map(({ purchaseDetails, ...item }) => ({
        ...item,
        key: item?.isin,
        callRecords: purchaseDetails?.map((i, index) => ({ ...i, key: index }))
      })),
    [portfolioWithPurchaseData]
  );

  return (
    <div className="grow flex flex-col">
      <div className="flex justify-between px-2 pt-6 bg-dark_black7">
        <Input
          value={filterText}
          onChange={onSearch}
          inputSize="small"
          inputWrapperProps={{ preserveErrorMessage: false }}
          placeHolder="جستجو (نام نماد)"
          startAdornment={<Search />}
          className="w-[290px]"
        />
        <div className="text-sm font-bold pl-2 pt-1">
          {portfolioWithPurchaseData?.data?.items?.length ?? 0}
          <span className="text-xs font-normal">نماد</span>
        </div>
      </div>
      <div className="grow flex-col flex">
        <CollapsibleTable
          getRowId={getRowId as any}
          hiddenGroupArrow={false}
          masterDetail
          sumValues={false}
          columnDefs={columnDefsData}
          detailCellRenderer={isLoading ? LoadingRenderer : null}
          detailCellRendererParams={detailCellRendererParams}
          defaultColDef={defaultColDef}
          data={tableData?.length ? tableData : null}
        />

        <div className="h-[40px] bg-[#343438] border-t border-[#545454] justify-between rounded-b-[4px] flex items-center px-2">
          {!!totalCount && totalCount > 0 && (
            <div className="w-fit">
              <Pagination
                showItemsCounter
                setPageNumber={setPageNumber}
                pageNumber={pageNumber}
                totalCount={totalCount ?? 0}
                rowPerPage={pageSize}
                onChangeRow={e => {
                  setPageSize(e);
                  setPageNumber(0);
                }}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default PortfolioTable;
