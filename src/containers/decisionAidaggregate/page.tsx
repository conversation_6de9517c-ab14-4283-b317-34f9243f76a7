"use client";

import PassedTimer from "@/app/(home)/(decisionAid)/components/PassedTimer/PassedTimer";
import SideBar from "@/app/(home)/(decisionAid)/components/Sidebar/SideBar";
import Reload from "@/assets/icons/Reload.svg";
import Checkbox from "@/components/atoms/checkbox/Checkbox";
import { Spinner } from "@/components/atoms/spinner";
import Switch from "@/components/atoms/switch";
import { Tooltip } from "@/components/atoms/tooltip";
import routes from "@/constants/routes";
import { IGetDecisionAidDataItems, useGetAggregatedInvestmentSuggestionsQuery } from "@/queries/decisionAidAPI";
import { insertParamsToUrl } from "@/utils/helpers";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useCallback, useEffect, useMemo, useState } from "react";
import { twMerge } from "tailwind-merge";
import Aggregated from "./components/Aggregated/Aggregated";

function DecisionAidAggregate() {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const MinimumYtm = Number(searchParams.get("MinimumYtm")) || undefined;
  const MinimumMaturityRemainedDays = Number(searchParams.get("MinimumMaturityRemainedDays")) || undefined;
  const MaximumMaturityRemainedDays = Number(searchParams.get("MaximumMaturityRemainedDays")) || undefined;
  const BondType = Number(searchParams.get("BondType")) || undefined;
  const MinimumInvestmentPerBond = Number(searchParams.get("MinimumInvestmentPerBond")) || undefined;
  const MaximumInvestmentBudget = Number(searchParams.get("MaximumInvestmentBudget")) || undefined;

  const [suggestPurchasable, setSuggestPurchasable] = useState(true);
  const [suggestNotPurchasable, setSuggestNotPurchasable] = useState(true);
  const [notSuggestPurchasable, setNotSuggestPurchasable] = useState(true);
  const [notSuggestNotPurchasable, setNotSuggestNotPurchasable] = useState(true);

  useEffect(() => {
    setSuggestPurchasable(true);
    setSuggestNotPurchasable(true);
    setNotSuggestPurchasable(true);
    setNotSuggestNotPurchasable(true);
  }, [searchParams]);

  const {
    data: bondTable,
    isError,
    isPending,
    mutateAsync,
    submittedAt
  } = useGetAggregatedInvestmentSuggestionsQuery({
    MinimumYtm,
    MinimumMaturityRemainedDays,
    MaximumMaturityRemainedDays,
    BondType,
    MinimumInvestmentPerBond,
    MaximumInvestmentBudget
  });

  const getData = useCallback(() => {
    mutateAsync({
      MinimumYtm,
      MinimumMaturityRemainedDays,
      MaximumMaturityRemainedDays,
      BondType,
      MinimumInvestmentPerBond,
      MaximumInvestmentBudget
    });
  }, []);

  const triggerSubmit = () => {
    getData();
  };
  useEffect(() => {
    getData();
  }, [
    MinimumYtm,
    MinimumMaturityRemainedDays,
    MaximumMaturityRemainedDays,
    BondType,
    MinimumInvestmentPerBond,
    MaximumInvestmentBudget,
    getData
  ]);

  const collapsibleData: IGetDecisionAidDataItems[] = useMemo(
    () =>
      bondTable?.data?.data?.length
        ? bondTable?.data?.data?.map(item => ({ ...item, callRecords: [...item.orders] }))
        : [],
    [bondTable]
  );

  const filteredData = collapsibleData.filter(
    data =>
      (suggestPurchasable && data?.orders[0]?.isSuggested && data?.orders[0]?.isPurchasable) ||
      (suggestNotPurchasable && data?.orders[0]?.isSuggested && !data?.orders[0]?.isPurchasable) ||
      (notSuggestPurchasable && !data?.orders[0]?.isSuggested && data?.orders[0]?.isPurchasable) ||
      (notSuggestNotPurchasable && !data?.orders[0]?.isSuggested && !data?.orders[0]?.isPurchasable)
  );

  const suggestPurchasableShow = collapsibleData?.some(
    i => i?.orders?.[0]?.isSuggested && i?.orders?.[0]?.isPurchasable
  );
  const suggestNotPurchasableShow = collapsibleData?.some(
    i => i?.orders?.[0]?.isSuggested && !i?.orders?.[0]?.isPurchasable
  );
  const notSuggestPurchasableShow = collapsibleData?.some(
    i => !i?.orders?.[0]?.isSuggested && i?.orders?.[0]?.isPurchasable
  );
  const notSuggestNotPurchasableShow = collapsibleData?.some(
    i => !i?.orders?.[0]?.isSuggested && !i?.orders?.[0]?.isPurchasable
  );

  // if while get watch list data error occurs, run this code
  if (isError) {
    <div className="flex justify-center items-center grow">
      <span>خطایی رخ داده است!</span>
    </div>;
  }

  const urlParams = {
    MinimumYtm,
    MinimumMaturityRemainedDays,
    MaximumMaturityRemainedDays,
    BondType,
    MinimumInvestmentPerBond,
    MaximumInvestmentBudget
  };

  return (
    <div className="grow  flex h-full py-2 pl-2 pr-4">
      <div className="w-[240px] bg-backgroundCardBackground rounded-r-xl">
        <SideBar />
      </div>
      <div className="grow h-full max-w-full flex-col">
        <div className="mx-2 pl-2 pr-4 py-4 h-14 flex items-center justify-between rounded-tl-xl border-b border-borderBorderAndDivider bg-cardBackground">
          {MinimumYtm ? (
            <div className="flex gap-2 text-sm text-mainText">
              <div>بهترین سفارش‌ها:</div>
              <Switch
                dataTest="78ff3d45-df1a-4e5b-b887-acd0404ccd5b"
                defaultChecked={pathname === routes.decisionAidAggregate}
                onChange={e => {
                  if (e?.target?.checked) {
                    router.push(insertParamsToUrl(routes.decisionAidAggregate, urlParams));
                  } else {
                    router.push(insertParamsToUrl(routes.decisionAidSingle, urlParams));
                  }
                }}
                title="نمایش تجمعی"
              />
            </div>
          ) : (
            <div className="text-white text-sm flex-1">همه مظنه‌ها</div>
          )}
          {MinimumYtm && (
            <div>
              <div className="flex gap-6 items-center">
                {suggestPurchasableShow && (
                  <Checkbox
                    id="suggestPurchasable"
                    text={MaximumInvestmentBudget ? "پیشنهادی قابل خرید" : "پیشنهادی"}
                    variant="filledLightGreen"
                    size="medium"
                    checked={suggestPurchasable}
                    onChange={() => {
                      setSuggestPurchasable(!suggestPurchasable);
                    }}
                  />
                )}
                {suggestNotPurchasableShow && (
                  <Checkbox
                    id="suggestNotPurchasable"
                    text="پیشنهادی غیرقابل خرید"
                    variant="filledDarkGreen"
                    size="medium"
                    checked={suggestNotPurchasable}
                    onChange={() => {
                      setSuggestNotPurchasable(!suggestNotPurchasable);
                    }}
                  />
                )}
                {notSuggestPurchasableShow && (
                  <Checkbox
                    id="notSuggestPurchasable"
                    text={MaximumInvestmentBudget ? "توصیه نشده‌ قابل خرید" : "غیرپیشنهادی"}
                    variant={MaximumInvestmentBudget ? "filledMediumYellow" : "filledRed"}
                    size="medium"
                    checked={notSuggestPurchasable}
                    onChange={() => {
                      setNotSuggestPurchasable(!notSuggestPurchasable);
                    }}
                  />
                )}
                {notSuggestNotPurchasableShow && (
                  <Checkbox
                    id="notSuggestNotPurchasable"
                    text="توصیه نشده‌ها غیرقابل خرید"
                    variant="filledRed"
                    size="medium"
                    checked={notSuggestNotPurchasable}
                    onChange={() => {
                      setNotSuggestNotPurchasable(!notSuggestNotPurchasable);
                    }}
                  />
                )}
              </div>
            </div>
          )}
          <div>
            <div className="flex gap-4 ml-4">
              <div className="flex gap-2 items-center text-secondaryText text-[10px]">
                <div className="flex gap-0.5">
                  <div>آخرین به روزرسانی</div>
                  <PassedTimer submittedAt={submittedAt} />
                  <div>دقیقه قبل</div>
                </div>

                <Tooltip className="w-fit" content="بررسی مجدد پیشنهادها" placement="top">
                  <div data-test="97379c6d-4442-40e6-90a2-840ac8bfc431" onClick={triggerSubmit}>
                    <Reload className="w-[14px] h-4 cursor-pointer" />
                  </div>
                </Tooltip>
              </div>
            </div>
          </div>
        </div>

        <div className={twMerge(" flex flex-col ", MinimumYtm ? " h-[calc(100%-48px)]" : " h-full")}>
          {isPending ? (
            <div className="h-full flex justify-center items-center">
              <Spinner />
            </div>
          ) : (
            <Aggregated filteredData={filteredData} isPending={isPending} hasData={!!collapsibleData?.length} />
          )}
        </div>
      </div>
    </div>
  );
}

export default DecisionAidAggregate;
