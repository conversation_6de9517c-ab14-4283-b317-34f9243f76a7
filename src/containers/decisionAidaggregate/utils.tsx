/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import { ColDef, ColGroupDef } from "ag-grid-community";

import useToggleCollapse from "@/app/(home)/CollapseStore";
import { Tooltip } from "@/components/atoms/tooltip";
import { colors } from "@/components/organisms/Table/CollapsibleTable/CollapsibleTableTypes";
import { IAccount, ICallRecord } from "@/components/organisms/Table/CollapsibleTable/storybook/CollapsibleStoryRender";
import { CustomGroupCellRenderer } from "@/components/organisms/Table/CollapsibleTable/utils";
import TableLoading from "@/components/organisms/Table/TableLoading";
import { IGetDecisionAidDataItems } from "@/queries/decisionAidAPI";
import { dateConverter } from "@/utils/DateHelper";
import { commaSeparator } from "@/utils/helpers";
import { IDetailCellRendererParams } from "@ag-grid-community/core";

export const detailCellRendererParamsFn = () =>
  ({
    detailGridOptions: {
      columnDefs: [
        {
          field: "totalPrice",
          headerName: "قیمت",
          cellRenderer: ({ data }: { data: any }) =>
            data && <div className=" text-xs mt-1 ">{data?.totalPrice ? commaSeparator(data?.totalPrice) : ""}</div>
        },
        {
          field: "chosenVolume",
          headerName: "حجم سفارش قابل معامله",
          cellRenderer: ({ data }: { data: any }) => (
            <div>
              <div className="relative w-full h-full">
                {data?.chosenVolume !== data?.originalOrderVolume && (
                  <div className="absolute cursor-pointer top-0 right-0">
                    <Tooltip
                      content="tooltipContent"
                      placement="top-end"
                      // eslint-disable-next-line react/no-children-prop
                      children={
                        <div
                          data-test="5a17115e-0fa2-4064-a2c4-b61fb78ac5d3"
                          className="border-l-[10px] border-l-transparent border-r-[10px] border-solid border-b-transparent border-b-[10px] border-[#545454]"
                        />
                      }
                    />
                  </div>
                )}

                <div className=" text-xs mt-1 ">
                  {data ? data?.chosenVolume : "---"} <br />
                  <p className=" text-xs">
                    {data?.chosenVolume === data?.originalOrderVolume && (
                      <div className="w-full flex h-1/2 justify-center items-center">
                        از
                        {data?.originalOrderVolume}
                      </div>
                    )}
                  </p>
                </div>
              </div>
            </div>
          )
        },
        { field: "ytmInPercent", headerName: "YTM سفارش", minWidth: 150 },
        {
          field: "compoundInterestPrice",
          headerName: "سود سهامدار قبلی",
          valueFormatter: "x.toLocaleString() + 's'"
        },
        {
          field: "totalTradeCost",
          headerName: "بهای تمام شده معامله",
          minWidth: 150
        },
        {
          field: "daysToMaturity",
          headerName: " روز تا سررسید",
          comparator: () => 0,
          width: 152,
          minWidth: 152,

          cellRenderer: ({ data }: { data: IGetDecisionAidDataItems }) =>
            data ? (
              <div className=" text-xs mt-1 ">
                {data?.maturityDate ? dateConverter(data?.maturityDate).format("YYYY/MM/DD") : ""} <br />
                <p className=" text-xs">
                  {data?.daysToMaturity > 0 ? (
                    <div className="flex items-center gap-0.5">
                      <span>{data?.daysToMaturity}</span> <span className="[word-spacing:1px]">روز مانده</span>
                    </div>
                  ) : (
                    ""
                  )}
                </p>
              </div>
            ) : (
              "---"
            )
        }
      ].reverse(),
      defaultColDef: {
        flex: 1
      },
      rowHeight: 40
    },
    getDetailRowData: params => {
      params.successCallback(params.data.callRecords);
    }
  }) as IDetailCellRendererParams<IAccount, ICallRecord>;

export function columnDefs(collapsible: boolean): (ColDef | ColGroupDef)[] | null | undefined {
  return [
    {
      field: "symbol",
      headerName: "نام ورقه",
      tooltipField: "isin",
      cellRenderer: (props: any) => {
        let variant: keyof typeof colors = "lightGreen";
        if (props?.data?.orders?.[0]?.isSuggested && props?.data?.orders?.[0]?.isPurchasable) variant = "lightGreen";
        if (props?.data?.orders?.[0]?.isSuggested && !props?.data?.orders?.[0]?.isPurchasable) variant = "primaryGreen";
        if (!props?.data?.orders?.[0]?.isSuggested && props?.data?.orders?.[0]?.isPurchasable)
          variant = "primaryYellow";
        if (!props?.data?.orders?.[0]?.isSuggested && !props?.data?.orders?.[0]?.isPurchasable) variant = "lightRed";
        if (collapsible) return <CustomGroupCellRenderer variant={variant} {...props} />;
        return <div>{props ? props?.data?.symbol : "---"}</div>;
      }
    },
    {
      field: "totalVolume",
      headerName: "حجم سفارش قابل معامله",
      tooltipField: "isin",
      width: 110,
      minWidth: 110,
      comparator: () => 0,
      cellRenderer: ({ data }: { data: IGetDecisionAidDataItems }) => <div>{data ? data.totalVolume : "---"}</div>
    },
    {
      field: "ytmInPercent",
      headerName: "میانگین وزنی YTM",
      tooltipField: "isin",
      width: 111,
      minWidth: 111,
      cellRenderer: ({ data }: { data: IGetDecisionAidDataItems }) => <div>{data ? data.ytmInPercent : "---"}</div>,
      comparator: () => 0
    },

    {
      field: "totalCompoundInterest",
      headerName: "مجموع سود سهامدار قبلی",
      tooltipField: "isin",
      width: 140,
      minWidth: 140,
      cellRenderer: ({ data }: { data: IGetDecisionAidDataItems }) => (
        <div>{data ? data.totalCompoundInterest : "---"}</div>
      ),
      comparator: () => 0
    },

    {
      field: "totalTradeCost",
      headerName: "بهای تمام شده معامله",
      tooltipField: "isin",
      width: 150,
      minWidth: 150,
      cellRenderer: ({ data }: { data: IGetDecisionAidDataItems }) => <div>{data ? data.totalTradeCost : "---"}</div>,
      comparator: () => 0
    },
    {
      field: "bondType",
      headerName: "کوپن",
      tooltipField: "isin",
      width: 120,
      minWidth: 120,
      cellClass: "flex justify-center",
      comparator: () => 0,
      cellRenderer: "renderCoupon"
    },
    {
      field: "daysToMaturity",
      headerName: " روز تا سررسید",
      tooltipField: "isin",
      comparator: () => 0,
      width: 152,
      minWidth: 152,

      cellRenderer: ({ data }: { data: IGetDecisionAidDataItems }) =>
        data ? (
          <div className=" text-base mt-1 ">
            {data?.maturityDate ? dateConverter(data?.maturityDate).format("YYYY/MM/DD") : ""} <br />
            <p className=" text-xs">
              {data?.daysToMaturity > 0 ? (
                <div className="flex items-center gap-0.5">
                  <span>{data?.daysToMaturity}</span> <span className="[word-spacing:1px]">روز مانده</span>
                </div>
              ) : (
                ""
              )}
            </p>
          </div>
        ) : (
          "---"
        )
    }
  ];
}

export function CustomLoadingOverlay() {
  const { isCollapsed } = useToggleCollapse();
  const colNumbers = isCollapsed ? 15 : 10;

  return <TableLoading colNumbers={colNumbers} rowNumbers={12} />;
}

export const switchItems = [
  {
    title: "بهترین سفارش‌ها",
    id: 1
  },
  {
    title: "نمایش تجمعی",
    id: 2
  }
];
