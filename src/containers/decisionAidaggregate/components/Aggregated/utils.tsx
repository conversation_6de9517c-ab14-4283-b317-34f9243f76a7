/* eslint-disable no-restricted-syntax */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import { ColDef, ColGroupDef } from "ag-grid-community";

import useToggleCollapse from "@/app/(home)/CollapseStore";
import { Tooltip } from "@/components/atoms/tooltip";
import { colors } from "@/components/organisms/Table/CollapsibleTable/CollapsibleTableTypes";
import { IAccount, ICallRecord } from "@/components/organisms/Table/CollapsibleTable/storybook/CollapsibleStoryRender";
import { CustomGroupCellRenderer } from "@/components/organisms/Table/CollapsibleTable/utils";
import TableLoading from "@/components/organisms/Table/TableLoading";
import { IGetDecisionAidDataItems, IGetDecisionAidDataItemsOrders } from "@/queries/decisionAidAPI";
import { dateConverter } from "@/utils/DateHelper";
import { commaSeparator, toFixed } from "@/utils/helpers";
import { IDetailCellRendererParams } from "@ag-grid-community/core";

export const detailCellRendererParamsFn = () =>
  ({
    detailGridOptions: {
      columnDefs: [
        {
          field: "price",
          headerName: "قیمت",
          cellRenderer: ({ data }: { data: IGetDecisionAidDataItemsOrders }) =>
            data && <div className=" text-xs mt-1 ">{data?.price ? commaSeparator(data?.price) : ""}</div>
        },
        {
          field: "chosenVolume",
          headerName: "حجم سفارش قابل معامله",
          cellRenderer: ({ data }: { data: IGetDecisionAidDataItemsOrders }) => (
            <div className="w-1/2 h-full">
              <div className="relative w-full h-full">
                {data?.chosenVolume !== data?.originalOrderVolume && (
                  <div className="absolute cursor-pointer top-0 right-0">
                    <Tooltip
                      content={`امکان خرید ${commaSeparator(data?.chosenVolume)} از ${commaSeparator(
                        data?.originalOrderVolume
                      )} را دارید`}
                      placement="top-end"
                      // eslint-disable-next-line react/no-children-prop
                      children={
                        <div
                          data-test="5a17115e-0fa2-4064-a2c4-b61fb78ac5d3"
                          className="border-l-[10px] border-l-transparent border-r-[10px] border-solid border-b-transparent border-b-[10px] border-[#545454]"
                        />
                      }
                    />
                  </div>
                )}

                <div className=" text-xs mt-1 text-center pt-2">
                  {data ? commaSeparator(data?.chosenVolume) : "---"} <br />
                  <p className=" text-xs">
                    {data?.chosenVolume !== data?.originalOrderVolume && (
                      <div className="w-full flex h-1/2 justify-center items-center">
                        از
                        {commaSeparator(data?.originalOrderVolume)}
                      </div>
                    )}
                  </p>
                </div>
              </div>
            </div>
          )
        },
        { field: "ytmInPercent", headerName: "YTM سفارش", minWidth: 150 },
        {
          field: "compoundInterestPrice",
          headerName: "سود سهامدار قبلی",
          valueFormatter: "x.toLocaleString() + 's'",
          cellRenderer: ({ data }: { data: IGetDecisionAidDataItemsOrders }) => (
            <div className=" text-xs mt-1 ">
              {data?.compoundInterestPrice ? commaSeparator(Math.ceil(data.compoundInterestPrice)) : ""}
            </div>
          )
        },
        {
          headerClass: "!text-right !justify-start [&_.ag-header-cell-comp-wrapper]:!justify-start",
          field: "totalTradeCost",
          headerComponent: () => (
            <Tooltip
              arrow
              className="!p-0 !text-right"
              placement="top-start"
              content={<span className="text-xs cursor-pointer">بهای تمام شده بدون احتساب کارمزد میباشد</span>}
            >
              <span className="text-right cursor-pointer">بهای تمام شده معامله</span>
            </Tooltip>
          ),
          minWidth: 150,
          cellRenderer: ({ data }: { data: IGetDecisionAidDataItemsOrders }) => (
            <div className=" text-xs mt-1 ">
              {data?.totalTradeCost ? commaSeparator(Math.ceil(data.totalTradeCost)) : ""}
            </div>
          )
        },
        {
          field: "daysToMaturity",
          headerName: " روز تا سررسید",
          comparator: () => 0,
          width: 152,
          minWidth: 152,

          cellRenderer: ({ data }: { data: IGetDecisionAidDataItems }) =>
            data ? (
              <div className=" text-xs mt-1 ">
                {data?.maturityDate ? dateConverter(data?.maturityDate).format("YYYY/MM/DD") : ""} <br />
                <p className=" text-xs">
                  {data?.daysToMaturity > 0 ? (
                    <div className="flex items-center gap-0.5">
                      <span>{commaSeparator(data?.daysToMaturity)}</span>{" "}
                      <span className="[word-spacing:1px]">روز مانده</span>
                    </div>
                  ) : (
                    ""
                  )}
                </p>
              </div>
            ) : (
              "---"
            )
        }
      ].reverse(),
      defaultColDef: {
        flex: 1
      },
      rowHeight: 40
    },
    getDetailRowData: params => {
      params.successCallback(params.data.callRecords);
    }
  }) as IDetailCellRendererParams<IAccount, ICallRecord>;

export function columnDefs({
  MaximumInvestmentBudget,
  collapsible,
  isinOrderBookYtm,
  shouldShowTooltip,
  nextYtm,
  lastDuplicateIndex
}: {
  collapsible: boolean | undefined | null;
  MaximumInvestmentBudget: number | undefined | null;
  shouldShowTooltip: boolean;
  nextYtm?: string;
  lastDuplicateIndex: number;
  isinOrderBookYtm: number;
}): (ColDef | ColGroupDef)[] | null | undefined {
  return [
    {
      field: "symbol",
      headerName: "نام نماد",
      tooltipField: "isin",
      headerComponentParams: {
        template:
          '<span data-test="229167de-741b-463d-8f33-82f8263505f1" ref="eText" class="custom-header-cell-right"></span>'
      },
      sortable: false,
      cellRenderer: ({ data, ...props }: { data: IGetDecisionAidDataItems }) => {
        let variant: keyof typeof colors = "lightGreen";
        if (data?.orders?.[0]?.isSuggested && data?.orders?.[0]?.isPurchasable) variant = "lightGreen";
        if (data?.orders?.[0]?.isSuggested && !data?.orders?.[0]?.isPurchasable) variant = "primaryGreen";
        if (!data?.orders?.[0]?.isSuggested && data?.orders?.[0]?.isPurchasable && MaximumInvestmentBudget)
          variant = "primaryYellow";
        if (!data?.orders?.[0]?.isSuggested && data?.orders?.[0]?.isPurchasable && !MaximumInvestmentBudget)
          variant = "lightRed";
        if (!data?.orders?.[0]?.isSuggested && !data?.orders?.[0]?.isPurchasable) variant = "lightRed";
        if (collapsible) return <CustomGroupCellRenderer variant={variant} data={data} {...props} />;
        return <div>{data ? data?.symbol : "---"}</div>;
      }
    },
    {
      field: "totalVolume",
      headerName: "حجم سفارش قابل معامله",
      tooltipField: "isin",
      cellClass: "flex justify-center",
      sortable: false,
      headerComponentParams: {
        template:
          '<span data-test="00d9b17b-f4d3-4fbd-be4d-3536f38ffa01" ref="eText" class="custom-header-cell-right"></span>'
      },
      width: 110,
      minWidth: 110,
      comparator: () => 0,
      cellRenderer: ({ data }: { data: IGetDecisionAidDataItems }) => (
        <div>{data ? commaSeparator(data.totalVolume) : "---"}</div>
      )
    },
    {
      field: "ytmInPercent",
      headerName: "میانگین وزنی YTM",
      tooltipField: "isin",
      cellClass: "flex justify-center",
      sortable: false,
      headerComponentParams: {
        template:
          '<span data-test="7491574b-47b3-4f52-ab8c-5cd322bbc730" ref="eText" class="custom-header-cell-right"></span>'
      },
      width: 111,
      minWidth: 111,
      comparator: () => 0,
      cellRenderer: ({ data, rowIndex }: { data: IGetDecisionAidDataItemsOrders; rowIndex: number }) => (
        <div className="w-1/2 h-full">
          <div className="relative w-full h-full">
            {!!shouldShowTooltip && rowIndex === lastDuplicateIndex && (
              <div className="absolute cursor-pointer -top-1 right-0">
                <Tooltip
                  content={
                    nextYtm
                      ? `میانگین ytm مظنه فوق برابر با ${isinOrderBookYtm} و بیشتر از میانگین ytm  ${nextYtm} است`
                      : `میانگین ytm مظنه فوق برابر با ${isinOrderBookYtm ? toFixed(isinOrderBookYtm) : ""} است`
                  }
                  placement="top-end"
                  // eslint-disable-next-line react/no-children-prop
                  children={
                    <div
                      data-test="5a17115e-0fa2-4064-a2c4-b61fb78ac5d3"
                      className="border-l-[10px] border-l-transparent border-r-[10px] border-solid border-b-transparent border-b-[10px] border-[#C2840C]  "
                    />
                  }
                />
              </div>
            )}

            <div className=" text-xs mt-1 text-center pt-2">
              <p className=" text-xs">{data ? data.ytmInPercent : "---"}</p>
            </div>
          </div>
        </div>
      )
    },

    {
      field: "totalCompoundInterest",
      headerName: "مجموع سود سهامدار قبلی",
      cellClass: "flex justify-center",
      tooltipField: "isin",
      sortable: false,
      headerComponentParams: {
        template:
          '<span data-test="a98ed46e-e28a-4aca-80aa-57b5c09b2801" ref="eText" class="custom-header-cell-right"></span>'
      },
      width: 140,
      minWidth: 140,
      cellRenderer: ({ data }: { data: IGetDecisionAidDataItems }) => (
        <div>{data ? commaSeparator(Math.ceil(data.totalCompoundInterest)) : "---"}</div>
      ),
      comparator: () => 0
    },

    {
      field: "totalTradeCost",
      headerName: "بهای تمام شده معامله",
      cellClass: "flex justify-center",
      tooltipField: "isin",
      sortable: false,
      headerComponent: () => (
        <Tooltip
          arrow
          className="!p-0"
          placement="top-start"
          content={<span className="text-xs cursor-pointer">بهای تمام شده بدون احتساب کارمزد میباشد</span>}
        >
          <span className="cursor-pointer">بهای تمام شده معامله</span>
        </Tooltip>
      ),
      headerComponentParams: {
        template:
          '<span data-test="05b3ccec-bd9f-4967-8238-5c601d6c6ae4" ref="eText" class="custom-header-cell-right"></span>'
      },
      width: 150,
      minWidth: 150,
      cellRenderer: ({ data }: { data: IGetDecisionAidDataItems }) => (
        <div>{data ? commaSeparator(Math.ceil(data.totalTradeCost)) : "---"}</div>
      ),
      comparator: () => 0
    },
    {
      field: "bondType",
      headerName: "کوپن",
      cellClass: "flex justify-center",
      tooltipField: "isin",
      sortable: false,
      width: 120,
      minWidth: 120,
      headerComponentParams: {
        template:
          '<span data-test="f524f2e7-3065-48c8-be1c-2a456ad337aa" ref="eText" class="custom-header-cell-text"></span>'
      },
      comparator: () => 0,
      cellRenderer: "renderCoupon"
    },
    {
      field: "daysToMaturity",
      headerName: " روز تا سررسید",
      cellClass: "flex justify-center",
      tooltipField: "isin",
      headerComponentParams: {
        template:
          '<span data-test="f8bd2544-378f-4872-9774-0ad79d323a91" ref="eText" class="custom-header-cell-right"></span>'
      },
      comparator: () => 0,
      sortable: false,
      width: 152,
      minWidth: 152,

      cellRenderer: ({ data }: { data: IGetDecisionAidDataItems }) =>
        data ? (
          <div className=" text-base mt-1 ">
            {data?.maturityDate ? dateConverter(data?.maturityDate).format("YYYY/MM/DD") : ""} <br />
            <p className=" text-xs">
              {data?.daysToMaturity > 0 ? (
                <div className="flex items-center gap-0.5">
                  <span>{commaSeparator(data?.daysToMaturity)}</span>{" "}
                  <span className="[word-spacing:1px]">روز مانده</span>
                </div>
              ) : (
                ""
              )}
            </p>
          </div>
        ) : (
          "---"
        )
    }
  ];
}

export function CustomLoadingOverlay() {
  const { isCollapsed } = useToggleCollapse();
  const colNumbers = isCollapsed ? 15 : 10;

  return <TableLoading colNumbers={colNumbers} rowNumbers={12} />;
}

export function DetailCellRenderer() {
  return <h1 style={{ padding: "20px", color: "white" }}>Loading...</h1>;
}

export function findDuplicatedIsinItems(data: any[]): {
  duplicates: IGetDecisionAidDataItemsOrders[];
  hasPowerSell: boolean;
  isinOrderBookYtm: number;
  lastDuplicateIndex: number;
  nextAfterDuplicates?: IGetDecisionAidDataItemsOrders;
} {
  const flat: IGetDecisionAidDataItemsOrders[] = data.flat(Infinity);

  const isinCount: Record<string, number> = {};
  const duplicates: IGetDecisionAidDataItemsOrders[] = [];

  flat.forEach(item => {
    const isin = item?.isin;
    if (!isin) return;

    isinCount[isin] = (isinCount[isin] || 0) + 1;
    if (isinCount[isin] === 2) {
      // Collect all previous duplicates as well
      duplicates.push(...flat.filter(i => i.isin === isin));
    } else if (isinCount[isin] > 2) {
      duplicates.push(item);
    }
  });

  // Find the index of the last duplicate item
  const lastDuplicateIndex = duplicates.findIndex(d => flat.findIndex(i => i === d));

  const nextAfterDuplicates = flat[lastDuplicateIndex + 1];

  const hasPowerSell = duplicates.length > 1;
  const isinOrderBookYtm = flat[lastDuplicateIndex]?.isinOrderBookYtmInPercent;

  return { duplicates, hasPowerSell, isinOrderBookYtm, nextAfterDuplicates, lastDuplicateIndex };
}
