"use client";

import { Spinner } from "@/components/atoms/spinner";
import { useMemo, useRef } from "react";
import { IGetDecisionAidDataItems, IGetDecisionAidDataItemsOrders } from "@/queries/decisionAidAPI";
import { ColDef } from "@ag-grid-community/core";
import { RowClassRules } from "@ag-grid-community/core/dist/types/src/entities/gridOptions";
import CollapsibleTable, {
  ICollapsibleTableRefProps
} from "@/components/organisms/Table/CollapsibleTable/CollapsibleTable";
import NoData from "@/app/(home)/(decisionAid)/components/NoData/NoData";
import { useSearchParams } from "next/navigation";
import CustomTooltip from "../tableTooltip/TableTooltip";
import { DetailCellRenderer, columnDefs, detailCellRendererParamsFn, findDuplicatedIsinItems } from "./utils";
import { IAggregatedProps } from "./type";

function Aggregated(props: IAggregatedProps) {
  const searchParams = useSearchParams();
  const { filteredData, isPending, hasData } = props;

  const defaultColDef = useMemo<ColDef>(
    () => ({
      flex: 1,
      tooltipComponent: CustomTooltip
    }),
    []
  );

  const MaximumInvestmentBudget = Number(searchParams.get("MaximumInvestmentBudget")) || undefined;
  const MinimumYtm = Number(searchParams.get("MinimumYtm")) || undefined;

  const getRowId = (paramsRowId: { data: { key: string } }) => paramsRowId.data.key;

  const ref = useRef<ICollapsibleTableRefProps>(null);

  const newData: IGetDecisionAidDataItems[] = useMemo(() => {
    ref.current?.closeAll();

    return filteredData?.map(item => ({
      ...item,
      key: Math.random() * 10000,
      orders: item?.orders?.map(i => ({ ...i, key: Math.random() * 10000 })),
      callRecords: item?.callRecords?.map(i => ({ ...i, key: Math.random() * 10000 }))
    }));
  }, [filteredData]);

  let flattenArray: IGetDecisionAidDataItemsOrders[] = [];
  flattenArray = flattenArray.concat(...newData.map((subArray: IGetDecisionAidDataItems) => [...subArray.callRecords]));
  const lastPur: IGetDecisionAidDataItemsOrders = flattenArray
    ?.filter((itm: any) => itm?.isPurchasable)
    ?.reverse()?.[0];

  const rowClassRules = {
    "row-line": (params: any) =>
      params?.data?.callRecords &&
      params?.data?.callRecords?.[params.data.callRecords.length - 1]?.key === lastPur?.key &&
      MaximumInvestmentBudget
  } as RowClassRules;

  const { isinOrderBookYtm, nextAfterDuplicates, lastDuplicateIndex, duplicates, hasPowerSell } =
    findDuplicatedIsinItems(filteredData);

  const minimumYtm = searchParams.get("MinimumYtm");
  const minimumYtmInPercent = MinimumYtm ? Number(minimumYtm) / 100 : undefined;

  const shouldShowTooltip =
    // eslint-disable-next-line no-unsafe-optional-chaining
    hasPowerSell && !!minimumYtmInPercent && duplicates?.[duplicates?.length - 1]?.ytmInPercent < minimumYtmInPercent;
  // const shouldShowTooltip = true;

  const detailCellRendererParams = useMemo(() => detailCellRendererParamsFn(), []);

  const columnDefsData = useMemo(
    () =>
      columnDefs({
        collapsible: !!MinimumYtm,
        MaximumInvestmentBudget,
        shouldShowTooltip,
        isinOrderBookYtm,
        lastDuplicateIndex,
        nextYtm: nextAfterDuplicates?.symbol
      }),
    [
      MinimumYtm,
      MaximumInvestmentBudget,
      shouldShowTooltip,
      isinOrderBookYtm,
      lastDuplicateIndex,
      nextAfterDuplicates?.symbol
    ]
  );

  if (isPending) {
    return (
      <div className="h-full flex justify-center items-center">
        <Spinner />
      </div>
    );
  }

  return (
    <CollapsibleTable
      rowClassRules={rowClassRules}
      hiddenGroupArrow={false}
      getRowId={getRowId as any}
      masterDetail
      ref={ref}
      noRowsOverlayComponent={NoData}
      noRowsOverlayComponentParams={{ hasData }}
      columnDefs={columnDefsData as any}
      detailCellRenderer={isPending ? DetailCellRenderer : null}
      detailCellRendererParams={detailCellRendererParams}
      defaultColDef={defaultColDef}
      data={newData?.length ? newData : []}
    />
  );
}

export default Aggregated;
