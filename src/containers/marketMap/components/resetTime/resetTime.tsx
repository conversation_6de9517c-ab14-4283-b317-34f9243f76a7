import { dateConverter } from "@/utils/DateHelper";

function ResetTime({ time }: { time: number }) {
  const minute = dateConverter(time as number)?.format("mm");

  return (
    <div className="w-full grow flex flex-col h-full pl-[54px]">
      <div className="text-center pt-[50px] text-xl font-normal">بازار بسته است</div>

      <div className="w-full grow bg-no-repeat bg-[length:51.5%] bg-[center_top_46%] bg-noData">
        <div className="mt-8 flex justify-center">
          <div className="h-20 w-16 rounded border-[#B9B4AB] bg-[#181D36] text-center text-[38px] font-bold leading-[6rem] pl-.5">
            {Number(minute) < 10 ? minute?.[0] : minute?.[1]}
          </div>
          <div className="mr-2 h-20 w-16 rounded border-[#B9B4AB] bg-[#181D36] text-center text-[38px] font-bold leading-[6rem] pl-.5">
            {Number(minute) < 10 ? 0 : minute?.[0]}
          </div>
          <div className="mx-2 flex h-20 w-4 flex-col items-center justify-center text-center align-middle text-[38px] font-bold leading-[6rem] pl-.5">
            <div className="mb-1 h-2 w-2 rounded-full border border-[#B9B4AB] bg-[#181D36]" />
            <div className="mt-1 h-2 w-2 rounded-full border border-[#B9B4AB] bg-[#181D36]" />
          </div>
          <div className="h-20 w-16 rounded border-[#B9B4AB] bg-[#181D36] text-center text-[38px] font-bold leading-[6rem] pl-.5">
            8
          </div>
          <div className="mr-2 h-20 w-16 rounded border-[#B9B4AB] bg-[#181D36] text-center text-[38px] font-bold leading-[6rem] pl-.5">
            0
          </div>
        </div>
      </div>
    </div>
  );
}

export default ResetTime;
