import Checkbox from "@/components/atoms/checkbox/Checkbox";
import Switch from "@/components/atoms/switch";
import SwitchButton from "@/components/molecules/switchButton";
import { IFilterBar } from "./types";
import { bindShowBasedValueToSwitchItemId, bindSwitchItemIdToShowBasedValue, switchItems } from "./utils";

function FilterBar({
  showZeroCoupon,
  showHasCoupon,
  setShowHasCoupon,
  setShowZeroCoupon,
  showBasedValue,
  setShowBasedValue,
  setIsSameWeight
}: IFilterBar) {
  const toggleHasCoupons = () => {
    if (showZeroCoupon || !showHasCoupon) setShowHasCoupon(prev => !prev);
  };

  const toggleNoCoupons = () => {
    if (!showZeroCoupon || showHasCoupon) setShowZeroCoupon(prev => !prev);
  };

  return (
    <div
      data-test="a8e5a2b8-ca92-49e8-9f0c-431f8148b65f"
      className="flex justify-between bg-cardBackground p-1 mb-1 rounded-t-lg items-center"
    >
      <div data-test="ab6a9d1b-5654-45e4-9221-458cce401f63" className="flex justify-between gap-[26px]">
        <SwitchButton
          dataTest="7195235c-9c7c-4eee-9c28-38a37cb21e50"
          switchItems={switchItems}
          selectedItemId={bindShowBasedValueToSwitchItemId(showBasedValue)}
          onSelectedItemChange={itemID => {
            setShowBasedValue(bindSwitchItemIdToShowBasedValue(itemID));
          }}
        />
        <Switch
          onChange={e => setIsSameWeight(e?.target?.checked)}
          titleClassName="pr-2"
          title="نمایش هم وزن"
          dataTest="3c112eee-a99a-4bcd-b65f-7f3333e78753"
        />
      </div>
      <div data-test="8cd520f4-236a-499a-97a0-82ca5fbe953a" className="flex">
        <div
          data-test="7bc7ee61-fb7e-4358-8de7-dc097bac1610"
          className="flex gap-4 rounded-lg h-full pl-[9px] p-1 shadow-tradeChipsSelect bg-bodyBackground"
        >
          <Checkbox
            checked={showHasCoupon}
            onChange={toggleHasCoupons}
            variant="filledGreen"
            id="fcfdfc1d-b727-40f7-81fd-7675cdc091c2"
            text="کوپن دار"
          />
          <Checkbox
            checked={showZeroCoupon}
            onChange={toggleNoCoupons}
            variant="filledYellow"
            id="308def95-7090-468e-8169-a973f6364bfa"
            text="بدون کوپن"
          />
        </div>
      </div>
    </div>
  );
}

export default FilterBar;
