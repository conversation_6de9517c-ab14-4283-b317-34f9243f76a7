import { TBondData, TBondResponse, useGetOrderBookQuery } from "@/queries/bondAPI";
import React, { useMemo } from "react";
import { BondInfo, BondTitle, Box, YtmPriceTable } from "@/containers/ytmDetail/components";
import { AdditionalInfo, publicationList, renderOrderData } from "@/containers/ytmDetail/utils";
import { twMerge } from "tailwind-merge";
import { numberWithCommas } from "@/utils/helpers";
import Close from "@/assets/icons/toastClose.svg";
import Spinner from "@/assets/spinner.svg";
import Link from "next/link";
import { useSocketInitialData } from "@/hooks/useSocketInitialData";
import socketUrls from "@/constants/socketUrls";

function SideBar({
  isin,
  setSideBarId,
  className
}: {
  isin?: string;
  setSideBarId: (id: string) => void;
  className?: string;
}) {
  const { data: bond, isLoading } = useSocketInitialData<TBondResponse>({
    url: socketUrls.orderBookUrl,
    streamName: socketUrls.BondDetailLastState,
    id: isin,
    isEnabled: !!isin,
    isUpdatable: !!isin
  });

  const { data: orderBook } = useGetOrderBookQuery(isin);
  const { data: orderBookData = [] } = orderBook || {};
  const orderList = useMemo(() => renderOrderData(orderBookData), [orderBookData]);

  const change = Number(
    bond?.data?.lastTradePrice !== undefined && bond?.data?.closePrice !== undefined
      ? Number(bond?.data?.lastTradePrice) - Number(bond?.data?.bondLastDayTrade?.closePrice)
      : undefined
  );

  const changePercent =
    change !== undefined &&
    bond?.data?.closePrice !== undefined &&
    ((change / Number(bond?.data?.bondLastDayTrade?.closePrice)) * 100)?.toFixed(3);

  return (
    <div
      className={twMerge(
        "bg-backgroundCardBackground pr-2 pl-[7px] py-2 rounded w-[325px] ml-3px",
        className,
        isin ? "" : "hidden"
      )}
    >
      <div className="flex justify-between">
        <div className="text-sm font-bold pb-[7px] pt-px text-mainText">اطلاعات تکمیلی</div>
        <Close
          onClick={() => setSideBarId("")}
          className="scale-[1.3] cursor-pointer relative top-1 left-1"
          data-test="7100594d-62a3-4a2c-8533-415c817b1970"
        />
      </div>
      {isLoading ? (
        <div className="text-center pt-10">
          <Spinner className="animate-spin p-0.5 h-12 w-12 m-auto mb-1" />
          در حال دریافت ...
        </div>
      ) : (
        <>
          <div className="bg-cardBackground">
            <Link data-test="88731e32-51d1-4419-a26c-1eb263ecfa4b" href={`detail/${isin}`}>
              <BondTitle data={bond?.data} compact />
            </Link>
            <div className="bg-backgroundDarkRow rounded-b-lg flex justify-between p-2 pt-3px">
              <div className="text-xs text-mainText w-full">
                <div className="pb-1 flex justify-between text-.9xs text-secondaryText pt-0.5">
                  <div className="pl-1">آخرین معامله</div>
                  <div> {bond?.data?.lastTradeTime}</div>
                </div>
                <div className="flex justify-between w-full pt-0.5">
                  <div className="ltr">
                    {Number.isNaN(change) ? (
                      ""
                    ) : (
                      <span
                        className={twMerge(
                          "inline-flex text-xs",
                          // eslint-disable-next-line no-nested-ternary
                          change === 0 ? "text-gray-500" : change > 0 ? "text-textAscending" : "text-error"
                        )}
                      >
                        {change > 0 && <span className="pl-px text-success">&#x25B2;</span>}
                        {change < 0 && <span className="pl-px text-error">&#9660;</span>}
                        <span className="ltr">{numberWithCommas(change)}</span>
                        {changePercent !== undefined ? (
                          <span className="ltr px-1 text-xs">({changePercent} ٪ )</span>
                        ) : (
                          ""
                        )}
                      </span>
                    )}
                    <span className="pl-1 text-sm font-bold">{numberWithCommas(bond?.data?.lastTradePrice)}</span>
                  </div>
                  <div className="text-sm text-secondaryText">
                    <span className="pr-1 text-xs">YTM</span>{" "}
                    {(Number(bond?.data?.lastTradePriceYtm) * 100)?.toFixed(2)}٪
                  </div>
                </div>
              </div>
            </div>
          </div>
          <Box title="اطلاعات انتشار" className="mt-2" compact>
            <BondInfo<TBondData> list={publicationList} data={bond?.data} compact />
          </Box>
          <Box className="pt-2" title="اطلاعات تکمیلی اوراق" compact>
            <BondInfo<TBondData> list={AdditionalInfo} data={bond?.data} compact />
          </Box>
          {orderList?.length > 1 && (
            <Box className="pt-2" title="مظنه" titleClassName="pr-3" compact>
              <YtmPriceTable data={orderList} compact />
            </Box>
          )}
        </>
      )}
    </div>
  );
}

export default SideBar;
