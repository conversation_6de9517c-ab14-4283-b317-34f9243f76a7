function renderColorGuides(initial: number) {
  return [
    {
      label: `>${(initial + 2)?.toFixed(1)}`,
      color: "bg-darkGreen",
      comparator: (value: number) => initial + 2.5 < value,
      testId: "6f9fb518-bf25-41b2-9cb0-9c31c15dd113"
    },
    {
      label: `+${(initial + 2)?.toFixed(1)}`,
      color: "bg-marketGreen",
      comparator: (value: number) => initial + 1.5 < value && initial + 2.5 > value,
      testId: "46635a79-99bf-476f-98dd-2974ab739614"
    },
    {
      label: `+${(initial + 1)?.toFixed(1)}`,
      color: "bg-lightGreen",
      comparator: (value: number) => initial + 0.5 < value && initial + 1.5 > value,
      testId: "ee6e82e8-d280-46a2-a318-0e42fc0c04cc"
    },
    {
      label: ` ${initial?.toFixed(1)} `,
      color: "bg-[#8b8b8b]",
      comparator: (value: number) => initial - 0.5 < value && initial + 0.5 > value,
      testId: "43b665fc-9b2c-4227-95ed-6a1342e62bb3"
    },
    {
      label: `${(initial - 1)?.toFixed(1)}`,
      color: "bg-lightRed",
      comparator: (value: number) => initial - 0.5 > value && initial - 1.5 < value,
      testId: "0ea0bbe0-96fd-494a-9c4c-b39c2177ac20"
    },
    {
      label: `${(initial - 2)?.toFixed(1)}`,
      color: "bg-marketRed",
      comparator: (value: number) => initial - 1.5 > value && initial - 2.5 < value,
      testId: "7c4badf5-3b67-49f6-aac4-2a61ad42b38e"
    },
    {
      label: `<${(initial - 2)?.toFixed(1)}`,
      color: "bg-marketDarkRed",
      comparator: (value: number) => initial - 2.5 > value,
      testId: "133602a2-4202-4f2a-9857-30756430376a"
    }
  ];
}

export default renderColorGuides;
