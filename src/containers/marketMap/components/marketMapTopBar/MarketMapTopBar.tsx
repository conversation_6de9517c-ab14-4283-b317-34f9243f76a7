import { twMerge } from "tailwind-merge";
import React from "react";
import Close from "@/assets/icons/toastClose.svg";
import { IMarketMapTopBarProps } from "./types";
import renderColorGuides from "./utils";

function MarketMapTopBar({ average, title, setShowMap, showClose, onClick, bondType }: IMarketMapTopBarProps) {
  return (
    <div className="flex justify-between items-center pt-1 pl-1.5 pr-0.5 ml-px">
      <div>
        <div className="bg-disable pl-1 pr-2 py-[1px] rounded-t font-normal text-sm mr-0.5 flex justify-between">
          <div className="pr-px">{title}</div>
          {showClose && (
            <div className="cursor-pointer relative w-5 h-5 mr-1 flex justify-center items-center">
              <Close
                onClick={() => setShowMap(false)}
                className=""
                data-test={
                  bondType === 100 ? "f9a20fe4-b352-47b6-b484-c5f166e76e51" : "cfaa2010-19b2-4de0-b059-b310a37f6adc"
                }
              />
            </div>
          )}
        </div>
      </div>
      {average && (
        <div className="flex items-center justify-center mr-auto first relative top-px">
          {renderColorGuides(Number(average)).map(item => (
            <div
              role="presentation"
              key={item.label}
              data-test={item?.testId}
              className={twMerge([
                "ltr h-[22px] py-3px w-10 text-center text-xs text-white first:rounded-tr last:rounded-tl cursor-pointer",
                item.color
              ])}
              onClick={() => onClick(item?.comparator)}
            >
              {item.label}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

export default MarketMapTopBar;
