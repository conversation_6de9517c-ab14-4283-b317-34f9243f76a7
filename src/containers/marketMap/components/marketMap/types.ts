import { TMarketMapData } from "@/queries/marketMapAPI";

export interface IMarketMap {
  title?: string;
  bondType: 100 | 200;
  showBasedValue?: boolean;
  updatedValues: TMarketMapData[];
  setCouponSideBarId: (id: string) => void;
  couponSideBarId: string;
  reRender?: string;
  showCloseButton?: boolean;
  onCloseButtonClick: (e: boolean) => void;
  isSameWeight?: boolean;
  isNotFullWidth?: boolean;
}

export type TComparator = (e: number) => boolean;
