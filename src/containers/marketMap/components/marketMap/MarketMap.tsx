/* eslint-disable no-param-reassign */
import React, { useEffect, useLayoutEffect, useMemo, useRef } from "react";
import * as echarts from "echarts/core";
import { TreemapChart } from "echarts/charts";
import { CanvasRenderer } from "echarts/renderers";
import { useGetMarketMap, useGetTodayAveragePriceYtm, TMarketMapData } from "@/queries/marketMapAPI";
import MarketMapTopBar from "@/containers/marketMap/components/marketMapTopBar/MarketMapTopBar";
import ReactECharts from "echarts-for-react";
import Spinner from "@/assets/spinner.svg";
import { TMarketData } from "@/containers/marketMap/type";
import { EChartsOption } from "echarts-for-react/lib/types";
import ResetTime from "@/containers/marketMap/components/resetTime/resetTime";
import { useServerTime } from "@/components/organisms/Header/components/utils";
import NoData from "../noData/NoData";
import { IMarketMap, TComparator } from "./types";
import { checkResetTime, fixNegative, generateMarketMapData, getOptions, reRenderMap, updateChart } from "./utils";

echarts.use([TreemapChart, CanvasRenderer]);

function MarketMap({
  title,
  bondType,
  showBasedValue,
  updatedValues,
  couponSideBarId,
  setCouponSideBarId,
  reRender,
  showCloseButton,
  onCloseButtonClick,
  isNotFullWidth,
  isSameWeight
}: IMarketMap) {
  const chartRef = useRef<ReactECharts | null>(null);
  const { data: map, isLoading } = useGetMarketMap();
  const { data: averageYtm } = useGetTodayAveragePriceYtm();

  const average =
    averageYtm?.data?.[
      bondType === 100 ? "couponBondLastDayAverageYtmInPercent" : "zeroCouponBondLastDayAverageYtmInPercent"
    ]?.toFixed(2);

  const marketMapData = useMemo(
    () => generateMarketMapData(map?.data, average, showBasedValue, bondType, isSameWeight),
    [bondType, map?.data, showBasedValue, isSameWeight]
  );
  const dataIsValid = Number(marketMapData?.length) > 0;
  const { time } = useServerTime();

  useEffect(() => {
    const eChartInstance = chartRef?.current?.getEchartsInstance();
    if (updatedValues?.length > 0) {
      updateChart(
        eChartInstance,
        generateMarketMapData(updatedValues, average, showBasedValue, bondType, isSameWeight)
      );
    }
  }, [JSON.stringify(updatedValues)]);

  useLayoutEffect(() => {
    reRenderMap(chartRef);
  }, [reRender]);

  useEffect(() => {
    const eChartInstance = chartRef?.current?.getEchartsInstance();
    if (couponSideBarId) {
      eChartInstance?.dispatchAction({
        type: "select",
        name: couponSideBarId
      });
    }
  }, [couponSideBarId]);

  const handleRangeClick = (comparator: TComparator) => {
    const eChartInstance = chartRef.current!.getEchartsInstance();
    const oldOptions: EChartsOption = eChartInstance.getOption();
    const allBondIsin = map?.data?.map(item => item?.isin);

    setTimeout(() => {
      eChartInstance?.dispatchAction({
        type: "unselect",
        name: allBondIsin
      });
    }, 1);

    eChartInstance?.dispatchAction({
      type: "unselect",
      name: allBondIsin
    });
    const selectedBondList = map?.data
      ?.filter(item => comparator(item?.lastTradePriceYtmInPercent))
      ?.map(item => item?.isin);
    eChartInstance?.setOption({
      series: [
        {
          ...oldOptions.series[0],
          label: {
            ...oldOptions.series[0].label,
            formatter: (info: { data: TMarketData }) => {
              const isSelected = comparator(info?.data?.lastTradePriceYtmInPercent);
              return [
                !isSelected ? `{name|${info?.data?.symbol}}` : `{selectedName|${info?.data?.symbol}}`,
                !isSelected
                  ? `{value|${fixNegative(Number(info.data.lastTradePriceYtmInPercent))}}`
                  : `{selectedValue|${fixNegative(Number(info.data.lastTradePriceYtmInPercent))}}`
              ].join("\n");
            }
          }
        }
      ]
    });
    setTimeout(() => {
      if (selectedBondList?.length) {
        eChartInstance?.dispatchAction({
          type: "select",
          name: selectedBondList
        });
      }
    }, 100);
  };

  const memorizedChart = useMemo(
    () =>
      dataIsValid ? (
        <ReactECharts
          echarts={echarts}
          // showLoading
          ref={e => {
            chartRef.current = e;
          }}
          onEvents={{
            click: (e: { data: TMarketMapData }) => {
              if (e?.data?.isin) setCouponSideBarId(e?.data?.isin);
            }
          }}
          className="grow !h-[calc(100%_-_4.4rem)]"
          option={getOptions(marketMapData)}
        />
      ) : (
        <NoData isNotFullWidth={isNotFullWidth} />
      ),
    [marketMapData, couponSideBarId, isNotFullWidth]
  );

  if (isLoading) {
    return (
      <div className="flex align-middle items-center justify-center flex-col h-full">
        <Spinner className="animate-spin p-0.5 h-12 w-12" />
      </div>
    );
  }

  if (!dataIsValid && checkResetTime(Number(time))) {
    return !isNotFullWidth ? (
      <ResetTime time={Number(time)} />
    ) : (
      <div className="flex w-full grow flex-col h-full">
        <NoData isNotFullWidth isResetTime />
      </div>
    );
  }

  return (
    <div className="flex w-full grow flex-col h-full">
      {dataIsValid && (
        <MarketMapTopBar
          bondType={bondType}
          setShowMap={onCloseButtonClick}
          showClose={showCloseButton}
          title={title}
          average={average}
          onClick={handleRangeClick}
        />
      )}
      {memorizedChart}
    </div>
  );
}

export default MarketMap;
