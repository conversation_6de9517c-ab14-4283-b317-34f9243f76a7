import * as echarts from "echarts";
import { EChartsOption } from "echarts-for-react/lib/types";
import { TMarketData } from "@/containers/marketMap/type";
import { TMarketMapData } from "@/queries/marketMapAPI";
import { commaSeparator, inBillion } from "@/utils/helpers";
import { EChartsType } from "echarts";
import EChartsReact from "echarts-for-react";
import { MutableRefObject } from "react";

export const fixNegative = (value: number) =>
  value < 0 ? `${value?.toFixed(3).toString().replace("-", "")}% -` : `${value?.toFixed(3)}%`;

/**
 * in this function we convert the value to linearMap to be able to use the colorMappingBy option
 * first value is the value of the linearMap second value is the domain of value third is range of color
 * based on the value of the These three items we can get the color of the node
 */
export function addColourToData(
  originList?: { value: (number | null)[]; children?: TMarketData[] }[],
  max = 5
):
  | {
      value: (number | null)[];
      children?: TMarketData[];
    }[]
  | undefined {
  if (originList?.length) {
    for (let i = 0; i < originList?.length; i += 1) {
      const node = originList[i];
      if (node) {
        const { value } = node;
        if (value[2] != null && value[2] > 0) {
          value[3] = echarts.number.linearMap(value[2], [-1 * max, max], [0, max], true);
        } else if (value[2] != null && value[2] < 0) {
          value[3] = echarts.number.linearMap(value[2], [-1 * max, max], [-1 * max, 0], true);
        }
      }
    }
  }

  return originList;
}
/**
 * این آبجکت اطلاعات مورد نیاز برای رنگ بندی خانه های نقشه بازار از سبز روشن تا قرمز را مشخص میکند
 * در ایچارت برای این منظرو باید از لول های مختلف که یک آرایه با چند مقدار میباشد استفاده شده است
 */
const levelOption = [
  {
    // color: ["#FF4255", "#992833", "#f5cac9", "#8f94a2", "#acfcde", "#007046", "#00D583"],
    color: ["#6C0000", "#B52929", "#DB7171", "rgba(245, 40, 145, 0)", "#71DB88", "#29B547", "#006C17"],
    colorMappingBy: "value",
    itemStyle: {
      borderColor: "#343438",
      borderWidth: 5.5,
      gapWidth: 4
    },
    emphasis: {
      itemStyle: {
        borderColor: "#28282C"
      }
    }
  }
];

const labelConfig = {
  show: true,
  position: [0, "50%"],
  overflow: "truncate",
  ellipsis: "",
  offset: [0, -15],
  formatter(info: { data: TMarketData }) {
    return [
      `{name|${info?.data?.symbol}}`,
      `{value|${fixNegative(Number(info.data.lastTradePriceYtmInPercent))}}`
    ].join("\n");
  },
  rich: {
    name: {
      fontSize: "14px",
      fontWeight: "bold",
      fontFamily: "yekan-bakh",
      align: "center",
      padding: 0
    },
    selectedName: {
      fontSize: "16px",
      fontWeight: "bold",
      fontFamily: "yekan-bakh",
      align: "center",
      padding: 0,
      color: "blue"
    },
    value: {
      fontSize: "12px",
      fontWeight: "normal",
      fontFamily: "yekan-bakh",
      align: "center",
      padding: [7, 0, 0, 0]
    },
    selectedValue: {
      fontSize: "14px",
      fontWeight: "normal",
      fontFamily: "yekan-bakh",
      align: "center",
      padding: [7, 0, 0, 0],
      color: "blue"
    }
  }
};

const BondTooltip = (
  info: TMarketData
) => `<div class="market-tooltip bg-cardBackground rounded-xl border border-white w-[170px]">
           <div class="tooltip-container rounded-xl">
             <div class="company-detail font-yekan rounded-xl px-1">
               <div class="name bg-cardBackground text-center p-1 pt-[5px] font-bold text-white100 rounded-xl">${info?.symbol}</div>
               <div class="flex justify-between pt-1 pb-[2px]">
 <div class="title text-mainText text-xs font-normal p-0">قیمت آخر</div>
 <div class="text-sm text-white p-0">${commaSeparator(info.lastTradePrice)}</div>
</div><div class="flex justify-between pt-1 pb-[2px]">
 <div class="title text-mainText text-xs font-normal p-0">قیمت پایانی</div>
 <div class="text-sm text-white p-0">${commaSeparator(info.closePrice)}</div>
</div><div class="flex justify-between pt-1 pb-[2px]">
 <div class="title text-mainText text-xs font-normal p-0">بالاترین قیمت</div>
 <div class="text-sm text-white p-0">${commaSeparator(info.dayHighPrice)}</div>
</div><div class="flex justify-between pt-1 pb-[2px]">
 <div class="title text-mainText text-xs font-normal p-0">پایین‌ترین قیمت</div>
 <div class="text-sm text-white p-0">${commaSeparator(info.dayLowPrice)}</div>
</div><div class="flex justify-between pt-1 pb-[2px]">
 <div class="title text-mainText text-xs font-normal p-0">ارزش معاملات</div>
 <div class="text-sm text-white p-0 ltr">${inBillion(info.totalTradeValue)}</div>
</div><div class="flex justify-between pt-1 pb-[2px]">
 <div class="title text-mainText text-xs font-normal p-0">حجم معاملات</div>
 <div class="text-sm text-white p-0 ltr">${commaSeparator(info.totalTradedVolume)}</div>
</div><div class="flex justify-between pt-1 pb-[2px]">
 <div class="title text-mainText text-xs font-normal p-0">تعداد معاملات</div>
 <div class="text-sm text-white p-0">${commaSeparator(info.totalNumberOfTrades)}</div>
</div>
             </div>
            
             </div>
           </div>
          </div>`;

export const tooltipConfig = () => ({
  borderWidth: 0,
  padding: 0,
  extraCssText: "box-shadow: none;background: transparent;border",
  formatter(info: { data: TMarketData }) {
    return [info.data?.symbol ? `${BondTooltip(info.data)}` : ""].join("");
  }
});

export function generateSeries(data: any) {
  return [
    {
      select: {
        itemStyle: {
          shadowColor: "#F1C21B",
          // color: "rgb(171,172,171)",
          shadowBlur: 7
        }
      },
      selectedMode: "multiple",
      // silent: true,
      name: "بورس",
      type: "treemap",
      top: "top",
      roam: false,
      nodeClick: false,
      visibleMin: 0,
      height: "100%",
      width: "100%",
      label: labelConfig,
      data,
      breadcrumb: {
        show: false
      },
      itemStyle: {
        color: "#8F94A2"
      },
      visualMin: -4,
      visualMax: 4,
      visualDimension: 3,
      levels: levelOption
    }
  ];
}

export const updateChart = (
  instance: EChartsType | undefined,
  newData: { value: (number | null)[]; children?: TMarketData[] }[] | undefined
) => {
  instance?.setOption({
    series: [
      {
        data: newData && newData
      }
    ]
  });
};

const generateValue = (
  item: TMarketMapData,
  showBasedValue?: boolean,
  isSameWeight?: boolean,
  lastDayAverage?: string
) => {
  let value;
  // Todo this line should refactored with lastTradePriceYtmDifferentInPercent after backend fixed the value.
  const diff = Number(item?.lastTradePriceYtmInPercent) - Number(lastDayAverage);
  const fixedChange = diff < 0.5 && diff > -0.5;

  if (isSameWeight) {
    value = 1;
  } else {
    value = showBasedValue ? item?.totalTradeValue : item?.totalTradedVolume;
  }
  return [value, null, fixedChange ? 0 : diff];
};

export function generateMarketMapData(
  data?: TMarketMapData[],
  lastDayAverage?: string,
  showBasedValue?: undefined | boolean,
  bondType?: 100 | 200,
  isSameWeight?: boolean
) {
  return addColourToData(
    data
      ?.filter(item => item?.totalTradeValue && item?.totalTradedVolume && item?.bondType === bondType)
      ?.map(item => ({
        ...item,
        name: item?.isin,
        value: generateValue(item, showBasedValue, isSameWeight, lastDayAverage)
      }))
  );
}

/** به وسیله این متد تمامی کانفیگ های موردنیاز برای چارت ساخته میشود مثلا فونت و رنگ و تولتیپ یا کانفیگ هایی مثل کمترین مقداری که باید در چارت نشان داده شود */
export function getOptions(
  data?: { value?: (number | null)[]; children?: TMarketData[] }[] | undefined
): EChartsOption {
  return {
    tooltip: tooltipConfig(),
    series: generateSeries(data)
  };
}

export const clone = (item: any) => JSON.parse(JSON.stringify(item));

export const reRenderMap = (chartRef?: MutableRefObject<EChartsReact | null>) => {
  const eChartInstance = chartRef?.current?.getEchartsInstance();
  eChartInstance?.resize();
};

export const checkResetTime = (time: number) => {
  // return true if market is closed
  const startTime = "08:00:00";
  const endTime = "08:30:00";
  const currentDate = new Date(time);

  const startDate = new Date(time);
  startDate.setHours(Number(startTime.split(":")[0]));
  startDate.setMinutes(Number(startTime.split(":")[1]));
  startDate.setSeconds(Number(startTime.split(":")[2]));

  const endDate = new Date(time);
  endDate.setHours(Number(endTime.split(":")[0]));
  endDate.setMinutes(Number(endTime.split(":")[1]));
  endDate.setSeconds(Number(endTime.split(":")[2]));

  return startDate < currentDate && endDate > currentDate;
};
