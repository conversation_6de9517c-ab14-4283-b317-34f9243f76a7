import { twMerge } from "tailwind-merge";

function NoData({ isNotFullWidth, isResetTime }: { isNotFullWidth?: boolean; isResetTime?: boolean }) {
  return (
    <div className="w-full grow flex flex-col">
      <div
        className={
          isNotFullWidth
            ? "w-full h-full bg-no-repeat flex flex-col-reverse bg-[length:80%] bg-[center_top_38%] bg-noData"
            : "w-full h-full bg-no-repeat flex flex-col-reverse bg-[length:49%] bg-[center_top_36%] bg-noData"
        }
      >
        <div
          className={twMerge("text-center text-mainText text-xl font-normal", isNotFullWidth ? "h-[40%]" : "pb-[12%]")}
        >
          {isResetTime ? "هنوز معامله ای انجام نشده است!" : "اطلاعات نقشه بازار در دسترس نیست !"}
        </div>
      </div>
    </div>
  );
}

export default NoData;
