import socketUrls from "@/constants/socketUrls";
import SideBar from "@/containers/marketMap/components/SideBar/SideBar";
import FilterBar from "@/containers/marketMap/components/filterBar";
import MarketMap from "@/containers/marketMap/components/marketMap";
import useSignalR from "@/hooks/useSignalR/useSignalR";
import useKeepUpdateMarketMap from "@/hooks/useUpdateMarketMap";
import { TMarketMapData } from "@/queries/marketMapAPI";
import { TreemapChart } from "echarts/charts";
import * as echarts from "echarts/core";
import { CanvasRenderer } from "echarts/renderers";
import { memo, useState } from "react";
import { twMerge } from "tailwind-merge";

echarts.use([TreemapChart, CanvasRenderer]);

function MarketMapContainer() {
  const [showHasCoupon, setShowHasCoupon] = useState<boolean>(false);
  const [showZeroCoupon, setShowZeroCoupon] = useState<boolean>(true);
  const [showBasedValue, setShowBasedValue] = useState<boolean>(true);
  const [isSameWeight, setIsSameWeight] = useState<boolean>(false);
  const [updatedValues, setUpdatedValues] = useState<TMarketMapData[]>([]);
  const [couponSideBarId, setCouponSideBarId] = useState<string>("");
  const pushToStack = useKeepUpdateMarketMap(setUpdatedValues);

  const filterProps = {
    showHasCoupon,
    showZeroCoupon,
    setShowZeroCoupon,
    setShowHasCoupon,
    setShowBasedValue,
    showBasedValue,
    setIsSameWeight
  };

  useSignalR(socketUrls.marketMapUrl, socketUrls.marketMapStreamName, {
    next: (item: TMarketMapData) => pushToStack(item),
    error: () => {}
  });

  const marketMapProps = {
    couponSideBarId,
    setCouponSideBarId,
    updatedValues,
    showBasedValue,
    isNotFullWidth: showHasCoupon && showZeroCoupon,
    reRender: `${showZeroCoupon}${showHasCoupon}`,
    isSameWeight
  };

  return (
    <div className="echart-container !h-full w-full bg-bodyBackground px-2.5 pb-4 flex-col flex">
      <div className="flex h-full">
        <SideBar className="mr-1.5" setSideBarId={setCouponSideBarId} isin={couponSideBarId} />
        <div className="grow h-full max-w-full flex flex-col px-1.5">
          <FilterBar {...filterProps} />
          <div className="grow flex max-w-full bg-backgroundCardBackground">
            {showHasCoupon && (
              <div className={twMerge("grow flex flex-col", showZeroCoupon ? "w-1/2" : "w-full")}>
                <MarketMap
                  {...marketMapProps}
                  showCloseButton={showZeroCoupon}
                  onCloseButtonClick={setShowHasCoupon}
                  bondType={100}
                  title="کوپن دار"
                />
              </div>
            )}
            {showHasCoupon && showZeroCoupon && (
              <div className="h-full border-x border-borderBorderAndDivider border-px w-[5px] mr-3px ml-px" />
            )}
            {showZeroCoupon && (
              <div className={showHasCoupon ? "w-1/2" : "w-full"}>
                <MarketMap
                  {...marketMapProps}
                  showCloseButton={showHasCoupon}
                  onCloseButtonClick={setShowZeroCoupon}
                  bondType={200}
                  title="بدون کوپن"
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default memo(MarketMapContainer);
