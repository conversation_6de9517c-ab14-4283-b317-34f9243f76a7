export interface ITradeHistoryParams {
  fromDate: string;
  toDate: string;
  isEnabled: boolean;
}
export interface IDurationHistoryParams {
  fromDate: string;
  toDate: string;
  isEnabled: boolean;
}

export interface IGetBondChartParams {
  id: string;
  fromDate: string;
  toDate: string;
}

export enum TradeType {
  TRADE_VALUE = "value",
  TRADE_YTM = "ytm"
}

export interface IRenderRecordParams {
  tradeDate: string;
  value: number;
  index: number;
  count: number;
  isMarketValueType: boolean;
  noSign?: boolean;
  hasCoupon: boolean;
}
export type TMarketValueSocket = {
  couponTotalValue: number;
  zeroCouponTotalValue: number;
};

export type TMarketYtmSocket = {
  couponBondTodayAverageYtmInPercent: number;
  zeroCouponBondTodayAverageYtmInPercent: number;
};
