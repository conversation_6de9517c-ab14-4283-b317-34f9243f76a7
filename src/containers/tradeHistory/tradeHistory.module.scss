.tradeChartWrapper {
  :global {
    * {
      font-family: "yekan-bakh";
    }
    .tooltip {
      display: flex;
      flex-direction: column;
      justify-content: center;
      gap: 3px;
      min-width: 153px;
      padding: 4px 8px;
      font-size: 10px;
      border-radius: 6px;
      color: #efefef;
      border: 0.5px solid #efefef;
      background: #343438;
      box-shadow: 0px 0px 14px 4px rgba(133, 133, 133, 0.25);

      & > span.date {
        text-align: center;
      }

      & > div {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }

    .tooltip b {
      line-height: 22px;
      font-size: 14px;
    }
    .chart-billion-label {
      display: flex;
      gap: 10px;
      font-size: 16px;
      align-items: center;
      padding-right: 8px;
    }
  }
}
