"use client";

/* eslint-disable react-hooks/exhaustive-deps */
import Checkbox from "@/components/atoms/checkbox/Checkbox";
import { Spinner } from "@/components/atoms/spinner";
import ChipsSelect from "@/components/molecules/chipsSelect/ChipsSelect";
import { IChipsSelectItem } from "@/components/molecules/chipsSelect/type";
import { DatePickerWrapper } from "@/components/organisms/datePicker";
import { IDatePickerWrapperRefProps } from "@/components/organisms/datePicker/types";
import socketUrls from "@/constants/socketUrls";
import useSignalR from "@/hooks/useSignalR/useSignalR";
import { useGetTradeYtmHistoryQuery } from "@/queries/tradeHistoryApi";
import { IDataMarketValueItem } from "@/queries/tradeHistoryApi/types";
import { dateConverter } from "@/utils/DateHelper";
import { useEffect, useMemo, useRef, useState } from "react";
import dayjs from "dayjs";
import { twMerge } from "tailwind-merge";
import YtmIcon from "@/assets/icons/circle-market-ytm.svg";
import HighChart from "../../components/molecules/highChart/HighChart";
import Styles from "./tradeHistory.module.scss";
import { TMarketYtmSocket } from "./types";
import getTradeHistoryChartOptions, {
  CHART_TIME,
  getNow,
  getPeriodDate,
  getPrevDate,
  getTooltipCalendar,
  renderRecord,
  tradeChipsList
} from "./util";

function YtmHistory() {
  const isMarketValueType = false;

  const timeRef: { current: NodeJS.Timeout | null } = useRef(null);
  const timeRefLoading: { current: NodeJS.Timeout | null } = useRef(null);

  const refChart = useRef(null);
  const initialStartDate = getPrevDate(CHART_TIME.Month3);

  const [data, setData] = useState<IDataMarketValueItem[] | undefined>([]);
  const [activeChip, setActiveChip] = useState(tradeChipsList[2]);

  const [period, setPeriod] = useState<string | number>(CHART_TIME.Month3);
  const [initialDate, setInitialDate] = useState<Date[] | undefined>();

  const [fromDate, setFromDate] = useState<string>(initialStartDate);
  const [toDate, setToDate] = useState<string>(dayjs().format("YYYY-MM-DD"));
  const myTooltip = getTooltipCalendar(fromDate, toDate, activeChip);

  const { data: tradeMarketYtm, isLoading: isLoadingYtm } = useGetTradeYtmHistoryQuery({
    fromDate,
    toDate,
    isEnabled: true
  });

  const [isEnabledCoupons, setIsEnabledCoupons] = useState(true);
  const [isEnabledNoCoupons, setIsEnabledNoCoupons] = useState(true);
  const [isLoading, setIsLoading] = useState(true);

  const datePickerRef = useRef<IDatePickerWrapperRefProps>(null);
  const dataRef = useRef<(() => IDataMarketValueItem[]) | null>(null);

  const getData = () => data || [];
  dataRef.current = getData;

  const toggleSeries = (s: boolean, index: number) => {
    /* @ts-ignore */
    const chart = refChart?.current?.chart;

    if (chart) {
      const series = chart.series[index];
      if (!s) {
        series.hide();
      } else {
        series.show();
      }
    }
  };

  const toggleHasCoupons = () => {
    const s = !isEnabledCoupons;
    toggleSeries(s, 0);
    setIsEnabledCoupons(s);
  };

  const toggleNoCoupons = () => {
    const s = !isEnabledNoCoupons;
    toggleSeries(s, 1);
    setIsEnabledNoCoupons(s);
  };

  useEffect(() => {
    let list: IDataMarketValueItem[] | undefined = [];

    list = tradeMarketYtm?.data.map(i => ({
      tradeDate: i.date,
      totalCouponTradeValue: i.couponAverageYtmInPercent,
      totalZeroCouponTradeValue: i.zeroCouponAverageYtmInPercent
    }));

    setIsLoading(true);
    setData(list);
  }, [tradeMarketYtm, isMarketValueType]);

  useEffect(() => {
    dataRef.current = () => data || [];
  }, []);

  const options = useMemo(() => {
    const count = data?.length || 0;

    const couponsData = data?.map((i, index) =>
      renderRecord({
        tradeDate: i.tradeDate,
        value: i.totalCouponTradeValue,
        index,
        count,
        isMarketValueType,
        hasCoupon: true
      })
    );

    const noCouponsData = data?.map((i, index) =>
      renderRecord({
        tradeDate: i.tradeDate,
        value: i.totalZeroCouponTradeValue,
        index,
        count,
        isMarketValueType,
        hasCoupon: false
      })
    );

    if (timeRefLoading.current) {
      clearTimeout(timeRefLoading.current);
    }

    timeRefLoading.current = setTimeout(() => {
      setIsLoading(false);
    }, 200);

    return getTradeHistoryChartOptions({
      couponsData,
      noCouponsData,
      time: period,
      isMarketValueType,
      isEnabledCoupons,
      isEnabledNoCoupons
    });
  }, [period, isEnabledCoupons, isEnabledNoCoupons, data, data?.length]);

  const onSwitch = (v: IChipsSelectItem) => {
    // using if to stop repetitive user click
    if (v.id !== period) {
      // convert enum to date
      setActiveChip(v);
      const date = getPrevDate(v.id);
      const now = getNow();

      if (timeRefLoading.current) {
        clearTimeout(timeRefLoading.current);
      }

      if (v.id !== CHART_TIME.DateRange) {
        setIsLoading(true);
        setPeriod(v.id);
        setFromDate(date);
        setToDate(now);
        setInitialDate(undefined);
      }

      if (!isEnabledCoupons && !isEnabledNoCoupons) {
        setIsEnabledCoupons(true);
        setIsEnabledNoCoupons(true);
      }
    }
  };

  const onCancelCalendar = () => {
    setInitialDate(undefined);
    onSwitch(tradeChipsList[2]);
  };

  const updateLastRecord = (couponValue: number, noCouponValue: number) => {
    const oldData = dataRef.current?.();

    if (oldData && oldData.length) {
      if (timeRef.current) {
        clearTimeout(timeRef.current);
      }

      timeRef.current = setTimeout(() => {
        const newData = [...oldData];
        const last = newData[newData.length - 1];
        last.totalCouponTradeValue = couponValue;
        last.totalZeroCouponTradeValue = noCouponValue;
        newData[newData.length - 1] = last;
        setData(newData);
      }, 3000);
    }
  };

  // YTM Signal
  useSignalR(
    socketUrls.totalMarketYtmUrl,
    socketUrls.totalMarketYtmStreamName,
    {
      next: (item: TMarketYtmSocket) => {
        if (
          !isLoading &&
          !isLoadingYtm &&
          item.couponBondTodayAverageYtmInPercent &&
          item.zeroCouponBondTodayAverageYtmInPercent
        ) {
          updateLastRecord(item.couponBondTodayAverageYtmInPercent, item.zeroCouponBondTodayAverageYtmInPercent);
        }
      },
      error: () => {}
    },
    undefined,
    true
  );

  return (
    <div className="p-2 rounded-lg bg-cardBackground flex flex-col h-full">
      <div className="flex gap-2 mb-2 items-center">
        <div className="flex gap-3 grow text-lg leading-normal font-bold text-white100">
          <YtmIcon className="rounded-full" />
          میانگین ساده YTM
        </div>

        <div className="flex gap-4 rounded h-full pl-2 pr-1 py-1 shadow-tradeChipsSelect bg-bodyBackground">
          <Checkbox
            id="check-coupons"
            checked={isEnabledCoupons}
            onChange={toggleHasCoupons}
            variant="filledGreen"
            text="کوپن دار"
          />
          <Checkbox
            id="check-no-coupons"
            checked={isEnabledNoCoupons}
            onChange={toggleNoCoupons}
            variant="filledYellow"
            text="بدون کوپن"
          />
        </div>
        <div
          className={twMerge(
            "relative flex items-center rounded text-base shadow-tradeChipsSelect bg-bodyBackground",
            (isLoading || isLoadingYtm) && "pointer-events-none"
          )}
        >
          <div data-test="c2232289-98fc-420f-902b-6d84697b01b2">
            <DatePickerWrapper
              isRange
              hasFooter={false}
              initialValue={initialDate}
              ref={datePickerRef}
              config={{
                locale: "fa-IR",
                weekends: ["friday"],
                maxDate: new Date()
              }}
              className="-top-[6px]"
              onChange={(date?: Date[]) => {
                if (date && date[0] && date[1] && date !== initialDate) {
                  const range = getPeriodDate(date);
                  setPeriod(range);
                  setIsEnabledCoupons(true);
                  setIsEnabledNoCoupons(true);

                  setInitialDate(date);
                  setFromDate(dateConverter(date[0]).calendar("gregory").format("YYYY-MM-DD"));
                  setToDate(dateConverter(date[1]).calendar("gregory").format("YYYY-MM-DD"));
                  datePickerRef.current?.close();

                  const f = tradeChipsList.find(i => i.id === CHART_TIME.DateRange);

                  if (f) {
                    onSwitch(f);
                  }
                }
              }}
              onCancel={onCancelCalendar}
            >
              {myTooltip}
            </DatePickerWrapper>
          </div>
          <ChipsSelect items={tradeChipsList} activeChip={activeChip} onSwitch={onSwitch} />
        </div>
      </div>
      {options && (
        <div className={twMerge("grow bg-black1 p-4 rounded", Styles.tradeChartWrapper)}>
          {isLoadingYtm || isLoading ? (
            <div className="flex grow h-full items-center justify-center">
              <Spinner />
            </div>
          ) : (
            <HighChart options={options} refChart={refChart} />
          )}
        </div>
      )}
    </div>
  );
}

export default YtmHistory;
