/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect, useRef } from "react";
import { IChipsSelectItem } from "@/components/molecules/chipsSelect/type";

function TransitionEffect({ active, padding }: { active: IChipsSelectItem; padding: number }) {
  const glider = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!glider.current) {
      return;
    }

    const bg: HTMLDivElement = glider.current;
    const id = `chips-item-${active.id.toString()}`;
    const el = document.getElementById(id) as HTMLElement;
    const pos = `${el.offsetLeft - padding}px`;

    bg.style.width = `${el.clientWidth}px`;
    bg.style.transform = `translateX(${pos})`;
  }, [active]);

  return (
    <span
      ref={glider}
      className="glider transition ease-out duration-200 w-[64px] h-7 absolute left-2 z-0 rounded bg-mainBlue"
    />
  );
}

export default TransitionEffect;
