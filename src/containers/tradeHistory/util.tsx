/* eslint-disable no-nested-ternary */
import CalendarIcon from "@/assets/icons/calendar.svg";
import { Tooltip } from "@/components/atoms/tooltip";
import { IChipsSelectItem } from "@/components/molecules/chipsSelect/type";
import { dateConverter } from "@/utils/DateHelper";
import { commaSeparator } from "@/utils/helpers";
import dayjs from "dayjs";
import Highcharts from "highcharts";
import { IRenderRecordParams } from "./types";

const colors = ["#26D5C0", "#BCD526"];
const convertToBillon = (n: number) =>
  !Number.isNaN(n) ? commaSeparator(n / 1000000000, 2, false)?.replace(".00", "") : "-";
export const convertToPercent = (n: number) => +(n * 100).toFixed(3);
export const toFixed = (n: number) => (!Number.isNaN(n) ? +n.toFixed(3) : "-");

// eslint-disable-next-line @typescript-eslint/naming-convention
enum CHART_TIME {
  Yearly = "1year",
  Month6 = "6month",
  Month3 = "3month",
  Month1 = "1month",
  Weekly = "1week",
  Daily = "1day",
  DateRange = "dateRange"
}

const tradeChipsList: IChipsSelectItem[] = [
  { id: CHART_TIME.Weekly, title: "1 هفته" },
  { id: CHART_TIME.Month1, title: "1 ماه" },
  { id: CHART_TIME.Month3, title: "3 ماه" },
  { id: CHART_TIME.Month6, title: "6 ماه" },
  { id: CHART_TIME.Yearly, title: "1 سال" },
  { id: CHART_TIME.DateRange, title: "", gap: 18 }
];

export const getBigPointProps = (isMarketValueType: boolean, hasCoupon: boolean, noSign?: boolean) => {
  const sign = noSign ? "" : isMarketValueType ? "B" : "%";
  const color = hasCoupon ? colors[0] : colors[1];

  return {
    marker: {
      symbol: "circle",
      radius: 10
    },
    dataLabels: {
      enabled: true,
      borderRadius: 6,
      backgroundColor: color,
      y: -16,
      style: {
        textOutline: false
      },
      // eslint-disable-next-line
      formatter: function () {
        const v = this.y;
        const value = noSign ? toFixed(v) : isMarketValueType ? convertToBillon(v) : toFixed(v);
        // eslint-disable-next-line
        return value + ` ${sign}`;
      }
    }
  };
};

const convertDate = (date: string) => {
  const d = new Date(date);
  const utc = Date.UTC(d.getFullYear(), d.getMonth(), d.getDate());
  return utc;
};

export const renderRecord = (params: IRenderRecordParams) => {
  const { tradeDate, value, count, index, isMarketValueType, hasCoupon, noSign } = params;

  if (index < count - 1) {
    return [convertDate(tradeDate), value];
  }

  return {
    ...getBigPointProps(isMarketValueType, hasCoupon, noSign),
    x: convertDate(tradeDate),
    y: value
  };
};

export const getNow = () => dayjs().format("YYYY-MM-DD");

const getTickInterval = (time: string | number) => {
  switch (time) {
    case CHART_TIME.Weekly:
    case CHART_TIME.Month1:
      // daily
      // 7 days
      return 24 * 3600 * 1000;
    case CHART_TIME.Month3:
    case CHART_TIME.Month6:
    case CHART_TIME.Yearly:
      return 24 * 3600 * 1000 * 30;
    default:
      // monthly
      return 24 * 3600 * 1000;
  }
};
export const getPrevDate = (time: string | number) => {
  const f = "YYYY-MM-DD";

  switch (time) {
    case CHART_TIME.Daily:
      return dayjs().format(f);
    case CHART_TIME.Weekly:
      return dayjs().subtract(7, "day").format(f);
    case CHART_TIME.Month1:
      return dayjs().subtract(1, "month").format(f);
    case CHART_TIME.Month3:
      return dayjs().subtract(3, "month").format(f);
    case CHART_TIME.Month6:
      return dayjs().subtract(6, "month").format(f);
    case CHART_TIME.Yearly:
      return dayjs().subtract(1, "year").format(f);
    default:
      return dayjs().format(f);
  }
};

const getChartDateTime = (time: string | number) => {
  switch (time) {
    case CHART_TIME.Month1:
      // 7 days
      return "M/D";
    case CHART_TIME.Weekly:
      return "YYYY/M/D";
    case CHART_TIME.Month3:
    case CHART_TIME.Month6:
    case CHART_TIME.Yearly:
      return "MMMM";
    default:
      // monthly
      return "YYYY/M/D";
  }
};
const getPriceChartOptions = ({
  couponsData,
  noCouponsData,
  time,
  isMarketValueType,
  noSign,
  isEnabledCoupons,
  isEnabledNoCoupons
}: {
  couponsData: any;
  noCouponsData: any;
  time: string | number;
  isMarketValueType: boolean;
  noSign?: boolean;
  isEnabledCoupons: boolean;
  isEnabledNoCoupons: boolean;
}) => {
  let plotLines = null;

  if (couponsData && noCouponsData) {
    const lastCoupon = isEnabledCoupons ? couponsData[couponsData.length - 1] : 0;
    const lastNoCoupon = isEnabledNoCoupons ? noCouponsData[noCouponsData.length - 1] : 0;

    plotLines = [
      {
        value: lastCoupon?.y,
        color: colors[0],
        dashStyle: "shortDash",
        width: 2
      },
      {
        value: lastNoCoupon?.y,
        color: colors[1],
        dashStyle: "shortDash",
        width: 2
      }
    ];
  }

  const options = {
    chart: {
      backgroundColor: "transparent",
      zooming: {
        type: "x",
        resetButton: {
          position: {
            x: 10,
            y: -10
          },
          theme: {
            fill: "#F4F4F4",
            stroke: "transparent",
            paddingLeft: 12,
            paddingRight: 12,
            border: 0,
            r: 4,
            style: {
              color: "#0C82F9",
              fontSize: "14px"
            },
            states: {
              hover: {
                fill: "#0C82F9",
                style: {
                  color: "#F4F4F4"
                }
              }
            }
          }
        }
      }
    },
    title: {
      text: ""
    },
    subtitle: {
      text: ""
    },
    xAxis: {
      type: "datetime",
      tickLength: 0,
      tickInterval: getTickInterval(time),
      labels: {
        /* @ts-ignore */
        formatter() {
          /* @ts-ignore */
          const d = new Date(this.value) as any;
          const format = getChartDateTime(time);
          return dateConverter(d).locale("fa-IR").format(format);
        },
        style: {
          color: "#F4F4F4",
          fontSize: "16px",
          fontWeight: "700"
        }
      }
    },
    yAxis: {
      title: false,
      gridLineColor: "#545454",
      gridLineWidth: 1,
      plotLines,
      labels: {
        useHTML: true,
        /* @ts-ignore */
        // eslint-disable-next-line
        formatter: function () {
          /* @ts-ignore */
          const { value } = this;

          if (isMarketValueType) {
            const digits = convertToBillon(value);
            return `<div class='chart-billion-label'><span>${digits}</span><img src="/billion.png" /></div>`;
          }

          return `${toFixed(value)}%`;
        },
        style: {
          color: "#F4F4F4",
          fontFamily: "yekan-bakh"
        }
      }
    },
    tooltip: {
      /* @ts-ignore */
      useHTML: true,
      backgroundColor: null,
      borderWidth: 0,
      shadow: false,
      /* @ts-ignore */
      // eslint-disable-next-line
      formatter: function () {
        let date1 = "";

        /* @ts-ignore */
        this.points?.forEach(i => {
          const d = dateConverter(new Date(i.x).toString());
          date1 = d.format("YYYY/M/D");
        });

        let s = `
              <div class="tooltip">
                  <span class="date">${date1}</span>
              `;

        /* @ts-ignore */
        this.points?.forEach(i => {
          const { y: value, series } = i;
          s += `
                  <div>
                    <b>
                      ${
                        noSign
                          ? toFixed(value)
                          : isMarketValueType
                            ? `${convertToBillon(value)}B`
                            : `${toFixed(value)}%`
                      }
                    </b>
                    <span>${series.name}</span>
                  </div>
              `;
        });

        s += "</div>";

        return s;
      },
      shared: true
    },
    colors,
    series: [
      {
        type: "area",
        name: "کوپن دار",
        data: couponsData || [],
        fillColor: {
          linearGradient: [0, 0, 0, 300],
          stops: [
            [0, colors[0]],
            [1, Highcharts?.color?.(colors[0]).setOpacity(0).get("rgba")]
          ]
        },
        marker: {
          enabled: false
        },
        visible: isEnabledCoupons
      },
      {
        type: "area",
        name: "بدون کوپن",
        data: noCouponsData || [],
        fillColor: !isEnabledCoupons
          ? {
              linearGradient: [0, 0, 0, 300],
              stops: [
                [0, colors[1]],
                [1, Highcharts?.color?.(colors[1]).setOpacity(0).get("rgba")]
              ]
            }
          : "transparent",
        marker: {
          enabled: false
        },
        visible: isEnabledNoCoupons
      }
    ],
    plotOptions: {
      series: {
        lineWidth: 4
      }
    },

    credits: {
      enabled: false
    },
    legend: {
      enabled: false
    }
  };

  return options;
};

export const getTooltipCalendar = (fromDate: string, toDate: string, active: IChipsSelectItem) => {
  const f = dateConverter(fromDate).format("YYYY/MM/DD");
  const t = dateConverter(toDate).format("YYYY/MM/DD");

  const content = (
    <div className="flex gap-3 font-bold">
      <div>
        <span className="ml-1">از</span>
        {f}
      </div>
      <div>
        <span className="ml-1">تا</span>
        {t}
      </div>
    </div>
  );

  const calendarIcon = (
    <div
      id={`chips-item-${CHART_TIME.DateRange}`}
      className="flex items-center justify-center w-7 h-7 m-[2px] rounded z-10 relative"
    >
      <CalendarIcon className="cursor-pointer" />
    </div>
  );

  const isActive = active.id === CHART_TIME.DateRange;

  const myTooltip =
    toDate && isActive ? (
      <Tooltip
        content={content}
        className="w-fit p-2"
        placement="bottom-end"
        // eslint-disable-next-line react/no-children-prop
        children={calendarIcon}
      />
    ) : (
      calendarIcon
    );

  return myTooltip;
};

export const getPeriodDate = (date: Date[]) => {
  const date1 = new Date(date[0]);
  const date2 = new Date(date[1]);

  const differenceTime = date2.getTime() - date1.getTime();
  const day = 1000 * 3600 * 24;
  const differenceDays = Math.round(differenceTime / day);

  let range = CHART_TIME.Yearly;

  if (differenceDays <= 7) {
    range = CHART_TIME.Weekly;
  } else if (differenceDays <= 30) {
    range = CHART_TIME.Month1;
  } else if (differenceDays <= 90) {
    range = CHART_TIME.Month3;
  } else if (differenceDays <= 180) {
    range = CHART_TIME.Month6;
  }

  return range;
};

export { CHART_TIME, tradeChipsList };
export default getPriceChartOptions;
