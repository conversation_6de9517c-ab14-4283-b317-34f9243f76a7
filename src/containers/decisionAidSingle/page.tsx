"use client";

import Reload from "@/assets/icons/Reload.svg";
import { useCallback, useEffect, useMemo, useState } from "react";
import Checkbox from "@/components/atoms/checkbox/Checkbox";
import { twMerge } from "tailwind-merge";
import { IGetSingleDecisionAidDataItems, useGetSingleInvestmentSuggestionsQuery } from "@/queries/decisionAidAPI";
import { Spinner } from "@/components/atoms/spinner";
import { useRouter, useSearchParams } from "next/navigation";
import routes from "@/constants/routes";
import SideBar from "@/app/(home)/(decisionAid)/components/Sidebar/SideBar";
import PassedTimer from "@/app/(home)/(decisionAid)/components/PassedTimer/PassedTimer";
import Switch from "@/components/atoms/switch";
import { Tooltip } from "@/components/atoms/tooltip";
import { insertParamsToUrl } from "@/utils/helpers";
import Single from "./components/Single/Single";

function DecisionAidSingle() {
  const router = useRouter();
  const searchParams = useSearchParams();

  const MinimumYtm = Number(searchParams.get("MinimumYtm")) || undefined;
  const MinimumMaturityRemainedDays = Number(searchParams.get("MinimumMaturityRemainedDays")) || undefined;
  const MaximumMaturityRemainedDays = Number(searchParams.get("MaximumMaturityRemainedDays")) || undefined;
  const BondType = Number(searchParams.get("BondType")) || undefined;
  const MinimumInvestmentPerBond = Number(searchParams.get("MinimumInvestmentPerBond")) || undefined;
  const MaximumInvestmentBudget = Number(searchParams.get("MaximumInvestmentBudget")) || undefined;

  const [suggestPurchasable, setSuggestPurchasable] = useState(true);
  const [suggestNotPurchasable, setSuggestNotPurchasable] = useState(true);
  const [notSuggestPurchasable, setNotSuggestPurchasable] = useState(true);
  const [notSuggestNotPurchasable, setNotSuggestNotPurchasable] = useState(true);

  useEffect(() => {
    setSuggestPurchasable(true);
    setSuggestNotPurchasable(true);
    setNotSuggestPurchasable(true);
    setNotSuggestNotPurchasable(true);
  }, [searchParams]);

  const {
    data: bondTable,
    isError,
    isPending,
    mutateAsync,
    submittedAt
  } = useGetSingleInvestmentSuggestionsQuery({
    MinimumYtm,
    MinimumMaturityRemainedDays,
    MaximumMaturityRemainedDays,
    BondType,
    MinimumInvestmentPerBond,
    MaximumInvestmentBudget
  });

  const getData = useCallback(() => {
    mutateAsync({
      MinimumYtm,
      MinimumMaturityRemainedDays,
      MaximumMaturityRemainedDays,
      BondType,
      MinimumInvestmentPerBond,
      MaximumInvestmentBudget
    });
  }, []);

  const triggerSubmit = () => {
    getData();
  };
  useEffect(() => {
    getData();
  }, [
    MinimumYtm,
    MinimumMaturityRemainedDays,
    MaximumMaturityRemainedDays,
    BondType,
    MinimumInvestmentPerBond,
    MaximumInvestmentBudget,
    getData
  ]);

  const collapsibleData: IGetSingleDecisionAidDataItems[] = useMemo(
    () => (bondTable?.data?.data?.length ? bondTable?.data?.data?.map(item => ({ ...item })) : []),
    [bondTable]
  );

  const filteredData = collapsibleData.filter(
    data =>
      (suggestPurchasable && data?.isSuggested && data?.isPurchasable) ||
      (suggestNotPurchasable && data?.isSuggested && !data?.isPurchasable) ||
      (notSuggestPurchasable && !data?.isSuggested && data?.isPurchasable) ||
      (notSuggestNotPurchasable && !data?.isSuggested && !data?.isPurchasable)
  );

  const suggestPurchasableShow = collapsibleData?.some(i => i?.isSuggested && i?.isPurchasable);
  const suggestNotPurchasableShow = collapsibleData?.some(i => i?.isSuggested && !i?.isPurchasable);
  const notSuggestPurchasableShow = collapsibleData?.some(i => !i?.isSuggested && i?.isPurchasable);
  const notSuggestNotPurchasableShow = collapsibleData?.some(i => !i?.isSuggested && !i?.isPurchasable);

  // if while get watch list data error occurs, run this code
  if (isError) {
    <div className="flex justify-center items-center grow">
      <span>خطایی رخ داده است!</span>
    </div>;
  }

  const urlParams = {
    MinimumYtm,
    MinimumMaturityRemainedDays,
    MaximumMaturityRemainedDays,
    BondType,
    MinimumInvestmentPerBond,
    MaximumInvestmentBudget
  };

  return (
    <div className="grow  flex h-full pb-4 pl-2 pr-4">
      <div className="w-[240px] bg-backgroundCardBackground rounded-r-xl">
        <SideBar />
      </div>
      <div className="grow h-full max-w-full flex-col">
        <div className="mx-2 pl-2 pr-4 py-4 h-14 flex items-center justify-between rounded-tl-xl border-b border-borderBorderAndDivider bg-cardBackground">
          {MinimumYtm ? (
            <div className="flex gap-2 text-sm text-mainText">
              <div>بهترین سفارش‌ها:</div>
              <Switch
                onChange={e => {
                  if (e?.target?.checked) {
                    router.push(insertParamsToUrl(routes.decisionAidAggregate, urlParams));
                  } else {
                    router.push(insertParamsToUrl(routes.decisionAidSingle, urlParams));
                  }
                }}
                title="نمایش تجمعی"
              />
            </div>
          ) : (
            <div className="text-white text-sm flex-1">همه مظنه‌ها</div>
          )}
          {MinimumYtm && (
            <div>
              <div className="flex gap-6 items-center">
                {suggestPurchasableShow && (
                  <Checkbox
                    id="suggestPurchasable"
                    text={MaximumInvestmentBudget ? "پیشنهادی قابل خرید" : "پیشنهادی"}
                    variant="filledLightGreen"
                    size="medium"
                    checked={suggestPurchasable}
                    onChange={() => {
                      setSuggestPurchasable(!suggestPurchasable);
                    }}
                  />
                )}
                {suggestNotPurchasableShow && (
                  <Checkbox
                    id="suggestNotPurchasable"
                    text="پیشنهادی غیرقابل خرید"
                    variant="filledDarkGreen"
                    size="medium"
                    checked={suggestNotPurchasable}
                    onChange={() => {
                      setSuggestNotPurchasable(!suggestNotPurchasable);
                    }}
                  />
                )}
                {notSuggestPurchasableShow && (
                  <Checkbox
                    id="notSuggestPurchasable"
                    text={MaximumInvestmentBudget ? "توصیه نشده‌ قابل خرید" : "غیرپیشنهادی"}
                    variant={MaximumInvestmentBudget ? "filledMediumYellow" : "filledRed"}
                    size="medium"
                    checked={notSuggestPurchasable}
                    onChange={() => {
                      setNotSuggestPurchasable(!notSuggestPurchasable);
                    }}
                  />
                )}
                {notSuggestNotPurchasableShow && (
                  <Checkbox
                    id="notSuggestNotPurchasable"
                    text="توصیه نشده‌ها غیرقابل خرید"
                    variant="filledRed"
                    size="medium"
                    checked={notSuggestNotPurchasable}
                    onChange={() => {
                      setNotSuggestNotPurchasable(!notSuggestNotPurchasable);
                    }}
                  />
                )}
              </div>
            </div>
          )}
          <div>
            <div className="flex gap-4 ml-4">
              <div className="flex gap-2 items-center text-secondaryText text-[10px]">
                <div className="flex gap-0.5">
                  <div>آخرین به روزرسانی</div>
                  <PassedTimer submittedAt={submittedAt} />
                  <div>دقیقه قبل</div>
                </div>
                <Tooltip className="w-fit" content="بررسی مجدد پیشنهادها" placement="top">
                  <div data-test="728085fc-af4f-40ef-a9cc-47599cec3ffe" onClick={triggerSubmit}>
                    <Reload className="w-[14px] h-4 cursor-pointer" />
                  </div>
                </Tooltip>
              </div>
            </div>
          </div>
        </div>
        <div className={twMerge(" flex flex-col h-[calc(100%-48px)]")}>
          {isPending ? (
            <div className="h-full flex justify-center items-center">
              <Spinner />
            </div>
          ) : (
            <Single filteredData={filteredData} isPending={isPending} hasData={!!collapsibleData?.length} />
          )}
        </div>
      </div>
    </div>
  );
}

export default DecisionAidSingle;
