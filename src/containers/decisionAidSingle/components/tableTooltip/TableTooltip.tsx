import { CustomTooltipProps } from "@ag-grid-community/react";
import { commaSeparator } from "@/utils/helpers";

function TableTooltip(props: CustomTooltipProps) {
  return (
    <div className="bg-cardBackground text-white border rounded-md border-mainText w-[250px] p-2 text-.8xs font-normal font-yekan">
      <div className="flex justify-between">
        <div>مجموع حجم</div>
        <div className="text-sm font-bold">{commaSeparator(props?.data?.accumulatedVolume)}</div>
      </div>
      <div className="flex justify-between pt-2">
        <div>مجموع بهای تمام شده معامله</div>
        <div className="text-sm font-bold">{commaSeparator(props?.data?.accumulatedTotalTradePrice)}</div>
      </div>
      <div className="flex justify-between pt-2">
        <div>مجموع سود سهامدار قبلی</div>
        <div className="text-sm font-bold">{commaSeparator(props?.data?.accumulatedCompoundInterest)}</div>
      </div>
    </div>
  );
}

export default TableTooltip;
