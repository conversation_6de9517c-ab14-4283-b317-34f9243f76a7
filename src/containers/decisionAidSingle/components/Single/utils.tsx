/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import { ColDef, ColGroupDef } from "ag-grid-community";

import useToggleCollapse from "@/app/(home)/CollapseStore";
import { Tooltip } from "@/components/atoms/tooltip";
import { colors } from "@/components/organisms/Table/CollapsibleTable/CollapsibleTableTypes";
import { IAccount, ICallRecord } from "@/components/organisms/Table/CollapsibleTable/storybook/CollapsibleStoryRender";
import { CustomGroupCellRenderer } from "@/components/organisms/Table/CollapsibleTable/utils";
import TableLoading from "@/components/organisms/Table/TableLoading";
import { IGetDecisionAidDataItems, IGetSingleDecisionAidDataItems } from "@/queries/decisionAidAPI";
import { dateConverter } from "@/utils/DateHelper";
import { commaSeparator } from "@/utils/helpers";
import { IDetailCellRendererParams } from "@ag-grid-community/core";

export const detailCellRendererParamsFn = () =>
  ({
    detailGridOptions: {
      columnDefs: [
        {
          field: "totalPrice",
          headerName: "قیمت",
          cellRenderer: ({ data }: { data: any }) =>
            data && <div className=" text-xs mt-1">{data?.totalPrice ? commaSeparator(data?.totalPrice) : ""}</div>
        },
        {
          field: "chosenVolume",
          headerName: "حجم سفارش قابل معامله",
          cellRenderer: ({ data }: { data: any }) => (
            <div>
              <div className="relative w-full h-full">
                {data?.chosenVolume !== data?.originalOrderVolume && (
                  <div className="absolute cursor-pointer top-0 right-0">
                    <Tooltip
                      content="tooltipContent"
                      placement="top-end"
                      // eslint-disable-next-line react/no-children-prop
                      children={
                        <div
                          data-test="5a17115e-0fa2-4064-a2c4-b61fb78ac5d3"
                          className="border-l-[10px] border-l-transparent border-r-[10px] border-solid border-b-transparent border-b-[10px] border-[#545454]"
                        />
                      }
                    />
                  </div>
                )}

                <div className=" text-xs mt-1 ">
                  {data ? data?.chosenVolume : "---"} <br />
                  <p className=" text-xs">
                    {data?.chosenVolume === data?.originalOrderVolume && (
                      <div className="w-full flex h-1/2 justify-center items-center">
                        از
                        {data?.originalOrderVolume}
                      </div>
                    )}
                  </p>
                </div>
              </div>
            </div>
          )
        },
        { field: "ytmInPercent", headerName: "YTM سفارش", minWidth: 150 },
        {
          field: "compoundInterestPrice",
          headerName: "سود سهامدار قبلی",
          valueFormatter: "x.toLocaleString() + 's'"
        },
        {
          field: "totalTradeCost",
          headerName: "بهای تمام شده معامله",
          minWidth: 150
        },
        {
          field: "daysToMaturity",
          headerName: " روز تا سررسید",
          comparator: () => 0,
          width: 152,
          minWidth: 152,

          cellRenderer: ({ data }: { data: IGetDecisionAidDataItems }) =>
            data ? (
              <div className=" text-xs mt-1 ">
                {data?.maturityDate ? dateConverter(data?.maturityDate).format("YYYY/MM/DD") : ""} <br />
                <p className=" text-xs">
                  {data?.daysToMaturity > 0 ? (
                    <div className="flex items-center gap-0.5">
                      <span>{data?.daysToMaturity}</span> <span className="[word-spacing:1px]">روز مانده</span>
                    </div>
                  ) : (
                    ""
                  )}
                </p>
              </div>
            ) : (
              "---"
            )
        }
      ].reverse(),
      defaultColDef: {
        flex: 1
      },
      rowHeight: 40
    },
    getDetailRowData: params => {
      params.successCallback(params.data.callRecords);
    }
  }) as IDetailCellRendererParams<IAccount, ICallRecord>;

export function columnDefs(
  collapsible: boolean | undefined | null,
  MaximumInvestmentBudget: number | undefined | null
): (ColDef | ColGroupDef)[] | null | undefined {
  return [
    {
      field: "symbol",
      tooltipField: "isin",
      headerName: "نام نماد",
      headerComponentParams: {
        template:
          '<span data-test="41d92398-1569-4964-a9da-85b8e279e7f8" ref="eText" class="custom-header-cell-text"></span>'
      },
      sortable: false,
      minWidth: 181,
      cellRenderer: ({ data, ...props }: { data: IGetSingleDecisionAidDataItems }) => {
        let variant: keyof typeof colors = "lightGreen";
        if (data?.isSuggested && data?.isPurchasable) variant = "lightGreen";
        if (data?.isSuggested && !data?.isPurchasable) variant = "primaryGreen";
        if (!data?.isSuggested && data?.isPurchasable && MaximumInvestmentBudget) variant = "primaryYellow";
        if (!data?.isSuggested && data?.isPurchasable && !MaximumInvestmentBudget) variant = "lightRed";
        if (!data?.isSuggested && !data?.isPurchasable) variant = "lightRed";
        if (collapsible) return <CustomGroupCellRenderer variant={variant} data={data} {...props} />;
        return <div>{data ? data?.symbol : "---"}</div>;
      }
    },
    {
      field: "price",
      headerName: "قیمت",
      tooltipField: "isin",
      sortable: false,
      minWidth: 55,
      cellClass: "flex justify-center",
      headerComponentParams: {
        template:
          '<span data-test="3125249f-787b-407a-b52e-6baa89d4b058" ref="eText" class="custom-header-cell-text"></span>'
      },
      comparator: () => 0,
      cellRenderer: ({ data }: { data: IGetSingleDecisionAidDataItems }) => (
        <div>{data ? commaSeparator(data.price) : "---"}</div>
      )
    },
    {
      field: "volume",
      headerName: "حجم سفارش قابل معامله",
      tooltipField: "isin",
      sortable: false,
      minWidth: 181,
      cellClass: "flex justify-center",
      comparator: () => 0,
      headerComponentParams: {
        template:
          '<span data-test="62301ed2-38f7-4d75-9eca-12e6cf3c91fd" ref="eText" class="custom-header-cell-text"></span>'
      },
      cellRenderer: ({ data }: { data: IGetSingleDecisionAidDataItems }) => (
        <div>{data ? commaSeparator(data.chosenVolume) : "---"}</div>
      )
    },
    {
      field: "ytmInPercent",
      headerName: "مقدار YTM سفارش",
      tooltipField: "isin",
      sortable: false,
      minWidth: 181,
      cellClass: "flex justify-center",
      headerComponentParams: {
        template:
          '<span data-test="9a4928df-e9b6-40bb-b320-6d170c0f6312" ref="eText" class="custom-header-cell-text"></span>'
      },
      cellRenderer: ({ data }: { data: IGetDecisionAidDataItems }) => <div>{data ? data.ytmInPercent : "---"}</div>,
      comparator: () => 0
    },

    {
      field: "totalCompoundInterest",
      headerName: "سود سهامدار قبلی",
      tooltipField: "isin",
      sortable: false,
      minWidth: 181,
      cellClass: "flex justify-center",
      headerComponentParams: {
        template:
          '<span data-test="42afd962-f68d-4b2f-876e-680948407401" ref="eText" class="custom-header-cell-text"></span>'
      },
      cellRenderer: ({ data }: { data: IGetDecisionAidDataItems }) => (
        <div>{data ? commaSeparator(Math.ceil(data.totalCompoundInterest)) : "---"}</div>
      ),
      comparator: () => 0
    },

    {
      field: "totalTradeCost",
      headerName: "بهای تمام شده معامله",
      tooltipField: "isin",
      sortable: false,
      minWidth: 181,
      cellClass: "flex justify-center",
      headerComponentParams: {
        template:
          '<span data-test="ec46f092-80a3-4e8b-bf25-c4b215d1ae73" ref="eText" class="custom-header-cell-text"></span>'
      },
      cellRenderer: ({ data }: { data: IGetDecisionAidDataItems }) => (
        <div>{data ? commaSeparator(Math.ceil(data.totalTradeCost)) : "---"}</div>
      ),
      comparator: () => 0
    },
    {
      field: "bondType",
      headerName: "کوپن",
      tooltipField: "isin",
      cellClass: "flex justify-center",
      sortable: false,
      minWidth: 52,
      comparator: () => 0,
      headerComponentParams: {
        template:
          '<span data-test="b05d415e-78c3-4446-9012-20152dcbf3f6" ref="eText" class="custom-header-cell-text"></span>'
      },
      cellRenderer: "renderCoupon"
    },
    {
      field: "daysToMaturity",
      headerName: " روز تا سررسید",
      tooltipField: "isin",
      comparator: () => 0,
      sortable: false,
      minWidth: 92,
      cellClass: "flex justify-center",
      headerComponentParams: {
        template:
          '<span data-test="8782f8d6-4e5e-40e1-bce8-52e1737b8785" ref="eText" class="custom-header-cell-text"></span>'
      },
      cellRenderer: ({ data }: { data: IGetDecisionAidDataItems }) =>
        data ? (
          <div className="text-base mt-1">
            {data?.maturityDate ? dateConverter(data?.maturityDate).format("YYYY/MM/DD") : ""} <br />
            <p className=" text-xs">
              {data?.daysToMaturity > 0 ? (
                <div className="flex items-center gap-0.5">
                  <span>{data?.daysToMaturity}</span> <span className="[word-spacing:1px]">روز مانده</span>
                </div>
              ) : (
                ""
              )}
            </p>
          </div>
        ) : (
          "---"
        )
    }
  ];
}

export function CustomLoadingOverlay() {
  const { isCollapsed } = useToggleCollapse();
  const colNumbers = isCollapsed ? 15 : 10;

  return <TableLoading colNumbers={colNumbers} rowNumbers={12} />;
}

export function DetailCellRenderer() {
  return <h1 style={{ padding: "20px", color: "white" }}>Loading...</h1>;
}
