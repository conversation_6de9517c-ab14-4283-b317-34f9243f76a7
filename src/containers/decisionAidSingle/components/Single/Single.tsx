"use client";

import { useMemo } from "react";
import { ColDef } from "@ag-grid-community/core";
import { RowClassRules } from "@ag-grid-community/core/dist/types/src/entities/gridOptions";
import CollapsibleTable from "@/components/organisms/Table/CollapsibleTable/CollapsibleTable";
import NoData from "@/app/(home)/(decisionAid)/components/NoData/NoData";
import { useSearchParams } from "next/navigation";
import CustomTooltip from "../tableTooltip/TableTooltip";
import { DetailCellRenderer, columnDefs, detailCellRendererParamsFn } from "./utils";
import { ISingleProps } from "./type";

function Single(props: ISingleProps) {
  const searchParams = useSearchParams();
  const { filteredData, isPending, hasData } = props;
  const detailCellRendererParams = useMemo(() => detailCellRendererParamsFn(), []);
  const defaultColDef = useMemo<ColDef>(
    () => ({
      flex: 1,
      tooltipComponent: CustomTooltip,
      tooltipShowDelay: 0,
      tooltipHideDelay: 0
    }),
    []
  );

  const MaximumInvestmentBudget = Number(searchParams.get("MaximumInvestmentBudget")) || undefined;
  const MinimumYtm = Number(searchParams.get("MinimumYtm")) || undefined;

  const columnDefsData = useMemo(
    () => columnDefs(!!MinimumYtm, MaximumInvestmentBudget),
    [MinimumYtm, MaximumInvestmentBudget]
  );

  const getRowId = (paramsRowId: { data: { key: string } }) => paramsRowId.data.key;

  const newData = useMemo(
    () =>
      filteredData?.map(item => ({
        ...item,
        key: Math.random() * 10000
      })),
    [filteredData]
  );

  const lastPur = newData?.filter((itm: any) => itm?.isPurchasable)?.reverse()?.[0];

  const rowClassRules = {
    "row-line": (params: any) => params?.data?.key === lastPur?.key && MaximumInvestmentBudget
  } as RowClassRules;

  return (
    <CollapsibleTable
      rowClassRules={rowClassRules}
      getRowId={getRowId as any}
      masterDetail
      headerCenter
      tooltipShowDelay={0}
      noRowsOverlayComponent={NoData}
      noRowsOverlayComponentParams={{ hasData }}
      columnDefs={columnDefsData as any}
      detailCellRenderer={isPending ? DetailCellRenderer : null}
      detailCellRendererParams={detailCellRendererParams}
      defaultColDef={defaultColDef}
      data={newData?.length ? newData : []}
    />
  );
}

export default Single;
