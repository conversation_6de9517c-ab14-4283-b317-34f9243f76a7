/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import useToggleCollapse from "@/app/(home)/CollapseStore";
import Tick from "@/assets/icons/Check.svg";
import Close from "@/assets/icons/Close.svg";
import TrashIcon from "@/assets/icons/trash.svg";
import { PriceTitle } from "@/components/atoms/priceTitle";
import { PriceYtm } from "@/components/atoms/priceYtm";
import TableLoading from "@/components/organisms/Table/TableLoading";
import { TBondTableData, TBondTableResponse } from "@/queries/bondTableAPI";
import { dateConverter } from "@/utils/DateHelper";
import { commaSeparator } from "@/utils/helpers";
import { ColDef, ColGroupDef } from "ag-grid-community";

export function columnDefs(
  deleteSymbol: (value: TBondTableData) => void,
  setCouponSideBarId: (v: string) => void
): (ColDef | ColGroupDef)[] | null | undefined {
  return [
    {
      field: "symbol",
      headerName: "نام اوراق",
      maxWidth: 120,
      minWidth: 120,
      unSortIcon: true,
      headerClass: "mt-[2px] !ps-0",
      sortable: true,
      // sort: "asc",
      cellRenderer: ({ data }: { data: TBondTableData }) => (
        <div onClick={() => setCouponSideBarId(data?.isin)}>{data ? data.symbol : "---"}</div>
      ),
      comparator: () => 0
    },
    {
      field: "",
      headerName: "حذف",
      minWidth: 80,
      maxWidth: 80,
      headerClass: "mt-[2px]",
      // sort: "asc",
      cellRenderer: ({ data }: { data: TBondTableData }) => (
        <div
          data-test="2a282505-f62d-4c35-a9f0-7e0c1d42d966"
          className="flex justify-center items-center cursor-pointer"
          onClick={() => deleteSymbol(data)}
        >
          <TrashIcon className="w-4 h-[17px]" />
        </div>
      ),
      comparator: () => 0
    },
    {
      field: "lastTradePrice",
      headerName: "آخرین قیمت",
      width: 110,
      minWidth: 110,
      headerClass: "mt-[2px]",
      unSortIcon: true,
      sortable: true,
      cellRenderer: ({ data }: { data: TBondTableData }) => (
        <div className="">
          <PriceTitle
            changePrice={data?.lastTradePriceYtmYesterdayChange}
            closePrice={data?.lastTradePriceYtmInPercent}
            price={data?.lastTradePrice}
          />
        </div>
      ),
      comparator: () => 0
    },
    {
      field: "lastTradeDate",
      headerName: " زمان معامله",
      width: 110,
      minWidth: 110,
      headerClass: "mt-[2px]",
      unSortIcon: true,
      sortable: true,
      comparator: () => 0,
      cellRenderer: ({ data }: { data: TBondTableData }) => (
        <div className=" text-base mt-1">
          {data?.lastTradeTime && data?.lastTradeDate ? (
            <div>
              {data?.lastTradeTime && data?.lastTradeDate === "0001-01-01T00:00:00" ? <>---</> : data?.lastTradeTime}
              <br />
              <p className="text-[10px]">
                {data?.lastTradeDate && data?.lastTradeDate === "0001-01-01T00:00:00"
                  ? ""
                  : dateConverter(data?.lastTradeDate).format("YYYY/MM/DD")}
              </p>
            </div>
          ) : (
            "---"
          )}
        </div>
      )
    },
    {
      field: "closePrice",
      headerName: "قیمت پایانی",
      width: 111,
      minWidth: 111,
      headerClass: "mt-[2px]",
      unSortIcon: true,
      sortable: true,
      cellRenderer: ({ data }: { data: TBondTableData }) => (
        <div className="">
          <PriceTitle
            changePrice={data?.closePriceYtmYesterdayChange}
            closePrice={data?.closePriceYtmInPercent}
            price={data?.closePrice}
          />
        </div>
      ),
      comparator: () => 0
    },

    {
      field: "bestBuyPrice",
      headerName: "بهترین مظنه خرید",
      width: 140,
      minWidth: 140,
      headerClass: "mt-[2px]",
      unSortIcon: true,
      sortable: true,
      cellRenderer: ({ data }: { data: TBondTableData }) => (
        <div className="">
          <PriceYtm price={data?.bestBuyPrice} ytm={data?.bestBuyYtmInPercent} />
        </div>
      ),
      comparator: () => 0
    },

    {
      field: "bestSellPrice",
      headerName: "بهترین مظنه فروش",
      maxWidth: 180,
      minWidth: 180,
      headerClass: "mt-[2px]",
      unSortIcon: true,
      sortable: true,
      cellRenderer: ({ data }: { data: TBondTableData }) =>
        data ? (
          <div className="">
            <PriceYtm price={data?.bestSellPrice} ytm={data?.bestSellYtmInPercent} />
          </div>
        ) : (
          "---"
        ),
      comparator: () => 0
    },
    {
      field: "totalTradedVolume",
      headerName: "حجم معاملات",
      width: 119,
      minWidth: 119,
      headerClass: "mt-[2px]",
      unSortIcon: true,
      sortable: true,
      cellRenderer: ({ data }: { data: TBondTableData }) => (
        <div className=" text-center">{commaSeparator(data?.totalTradedVolume)}</div>
      ),
      comparator: () => 0
    },
    {
      field: "bondType",
      headerName: "دارای کوپن",
      width: 120,
      minWidth: 120,
      headerClass: "mt-[2px]",
      unSortIcon: true,
      sortable: true,
      comparator: () => 0,
      cellRenderer: ({ value }: { value: number }) => (
        <div className=" w-full block z-50">
          {value && value === 100 ? (
            <Tick className="w-6 h-6 text-semanticWithCoupon z-50" />
          ) : (
            <Close className="w-6 h-6 text-semanticWithoutCoupon z-50" />
          )}
          {!value && <span className="mr-2">---</span>}
        </div>
      )
    },
    {
      field: "maturityDate",
      headerName: "تاریخ سررسید",
      unSortIcon: true,
      sortable: true,
      headerClass: "mt-[2px]",
      comparator: () => 0,
      width: 152,
      minWidth: 152,

      cellRenderer: ({ data }: { data: TBondTableData }) =>
        data ? (
          <div className=" text-base mt-1 ">
            {data?.maturityDate ? dateConverter(data?.maturityDate).format("YYYY/MM/DD") : ""} <br />
            <p className=" text-xs">
              {data?.daysToMaturityDate > 0 ? (
                <div className="flex items-center gap-0.5">
                  <span>{commaSeparator(data?.daysToMaturityDate)}</span>{" "}
                  <span className="[word-spacing:1px]">روز مانده</span>
                </div>
              ) : (
                ""
              )}
            </p>
          </div>
        ) : (
          "---"
        )
    },
    {
      field: "duration",
      headerName: "دیرش",
      unSortIcon: true,
      sortable: true,
      comparator: () => 0,
      width: 79,
      minWidth: 79,
      headerClass: "mt-[2px]",
      cellRenderer: "renderFloatNumber"
    },
    {
      field: "modifiedDuration",
      headerName: "دیرش تعدیل شده",
      unSortIcon: true,
      sortable: true,
      comparator: () => 0,
      maxWidth: 180,
      minWidth: 180,
      headerClass: "mt-[2px]",
      cellRenderer: "renderFloatNumber"
    },
    {
      field: "convexity",
      headerName: "تحدب",
      unSortIcon: true,
      sortable: true,
      comparator: () => 0,
      width: 79,
      minWidth: 79,
      headerClass: "mt-[2px]",
      cellRenderer: "renderFloatNumber"
    }
  ];
}

export function CustomLoadingOverlay() {
  const { isCollapsed } = useToggleCollapse();
  const colNumbers = isCollapsed ? 15 : 10;

  return <TableLoading colNumbers={colNumbers} rowNumbers={12} />;
}

export const sortKeyName: { [key: string]: string } = {
  symbol: "SortOrderBondName",
  closePrice: "SortOrderClosingPrice",
  lastTradePrice: "SortOrderLastTradePrice",
  bestBuyPrice: "SortOrderBestBuyPrice",
  bestSellPrice: "SortOrderBestSellPrice",
  totalTradedVolume: "SortOrderTotalTradedVolume",
  maturityDate: "SortOrderDaysToMaturityDate",
  bondType: "SortOrderBondType",
  duration: "SortOrderDuration",
  modifiedDuration: "SortOrderModifiedDuration",
  convexity: "SortOrderConvexity",
  lastTradeDate: "SortOrderLastTradeDate"
};

export const selectExcelFields = (res: TBondTableResponse) => {
  const f = "YYYY/M/D";

  return res.data.items.map(i => ({
    "نام نماد": i.symbol,
    "تاریخ انتشار": dateConverter(i.issuanceDate).format(f),

    "تاریخ سررسید": dateConverter(i.maturityDate).format(f),
    "روز تا سررسید": i.daysToMaturityDate,

    "مبلغ اسمی": i.faceValue,
    "سود اسمی": i.nominalInterestRate,

    "دفعات پرداخت در سال": i.paymentCount,
    "تاریخ اولین پرداخت": dateConverter(i.firstPaymentDate).format(f),

    "تعداد انتشار": i.totalSheetsAmount,
    "نوع ورقه": i.bondTypeName,

    "قیمت پایانی": i.closePrice,
    "قیمت پایانی ytm": i.closePriceYtmInPercent,

    "قیمت آخر": i.lastTradePrice,
    "قیمت آخر ytm": i.lastTradePriceYtmInPercent,

    "بهترین مظنه خرید": i.bestBuyPrice,
    "بهترین مظنه خرید ytm": i.bestBuyYtmInPercent,

    "بهترین مظنه فروش": i.bestSellPrice,
    "بهترین مظنه فروش ytm": i.bestSellYtmInPercent,

    "تاریخ آخرین معامله": dateConverter(i.lastTradeDate).format(f),
    "ساعت آخرین معامله": i.lastTradeTime,

    "حجم معاملات": i.totalTradedVolume,
    دیرش: i.duration,

    "دیرش تعدیل شده": i.modifiedDuration,
    تحدب: i.convexity,

    "بالاترین قیمت": i.dayHighPrice,
    "بالاترین قیمت ytm": i.dayHighPriceYtmInPercent,

    "کمترین قیمت": i.dayLowPrice,
    "پایین ترین قیمت ytm": i.dayLowPriceYtmInPercent,

    "تعداد معاملات": i.totalNumberOfTrades,
    "ارزش معاملات": i.totalTradeValue
  }));
};
