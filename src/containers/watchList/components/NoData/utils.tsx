import { TBondTableData } from "@/queries/bondTableAPI";
import { ColDef, ColGroupDef } from "ag-grid-community";

import useToggleCollapse from "@/app/(home)/CollapseStore";
import { PriceTitle } from "@/components/atoms/priceTitle";
import { PriceYtm } from "@/components/atoms/priceYtm";
import TableLoading from "@/components/organisms/Table/TableLoading";
import { dateConverter } from "@/utils/DateHelper";
import { commaSeparator } from "@/utils/helpers";

export function columnDefs(setCouponSideBarId: (v: string) => void): (ColDef | ColGroupDef)[] | null | undefined {
  return [
    {
      field: "symbol",
      headerName: "نام اوراق",
      width: 96,
      minWidth: 96,
      unSortIcon: true,
      sortable: true,
      // sort: "asc",
      cellRenderer: ({ data }: { data: TBondTableData }) => (
        <div onClick={() => setCouponSideBarId(data?.isin)}>{data ? data.symbol : "---"}</div>
      ),
      comparator: () => 0
    },
    {
      field: "symbol",
      headerName: "حذف",
      width: 51,
      minWidth: 51,
      headerClass: "!pl-0 !pr-[10px] flex justify-center",
      // sort: "asc",
      comparator: () => 0
    },
    {
      field: "lastTradePrice",
      headerName: "اخرین قیمت",
      width: 110,
      minWidth: 110,

      unSortIcon: true,
      sortable: true,
      cellRenderer: ({ data }: { data: TBondTableData }) => (
        // <div className="mr-2">
        <PriceTitle
          changePrice={data?.lastTradePriceYtmYesterdayChange}
          closePrice={data?.lastTradePriceYtmInPercent}
          price={data?.lastTradePrice}
        />
        // </div>
      ),
      comparator: () => 0
    },
    {
      field: "lastTradeDate",
      headerName: " زمان معامله",
      width: 110,
      minWidth: 110,

      unSortIcon: true,
      sortable: true,
      comparator: () => 0,
      cellRenderer: ({ data }: { data: TBondTableData }) => (
        <div className=" text-base mt-1 mr-2">
          {data?.lastTradeTime && data?.lastTradeDate ? (
            <div>
              {data?.lastTradeTime && data?.lastTradeDate === "0001-01-01T00:00:00" ? <>---</> : data?.lastTradeTime}
              <br />
              <p className="text-[10px]">
                {data?.lastTradeDate && data?.lastTradeDate === "0001-01-01T00:00:00"
                  ? ""
                  : dateConverter(data?.lastTradeDate).format("YYYY/MM/DD")}
              </p>
            </div>
          ) : (
            "---"
          )}
        </div>
      )
    },
    {
      field: "closePrice",
      headerName: "قیمت پایانی",
      width: 111,
      minWidth: 111,

      unSortIcon: true,
      sortable: true,
      cellRenderer: ({ data }: { data: TBondTableData }) => (
        <PriceTitle
          changePrice={data?.closePriceYtmYesterdayChange}
          closePrice={data?.closePriceYtmInPercent}
          price={data?.closePrice}
        />
      ),
      comparator: () => 0
    },

    {
      field: "bestBuyPrice",
      headerName: "بهترین مظنه خرید",
      width: 140,
      minWidth: 140,

      unSortIcon: true,
      sortable: true,
      cellRenderer: ({ data }: { data: TBondTableData }) => (
        <PriceYtm price={data?.bestBuyPrice} ytm={data?.bestBuyYtmInPercent} />
      ),
      comparator: () => 0
    },

    {
      field: "bestSellPrice",
      headerName: "بهترین مظنه فروش",
      width: 150,
      minWidth: 150,

      unSortIcon: true,
      sortable: true,
      cellRenderer: ({ data }: { data: TBondTableData }) =>
        data ? <PriceYtm price={data?.bestSellPrice} ytm={data?.bestSellYtmInPercent} /> : "---",
      comparator: () => 0
    },
    {
      field: "totalTradedVolume",
      headerName: "حجم معاملات",
      width: 119,
      minWidth: 119,

      unSortIcon: true,
      sortable: true,
      cellRenderer: "renderYtmPrice",
      comparator: () => 0
    },
    {
      field: "bondType",
      headerName: "دارای کوپن",
      width: 120,
      minWidth: 120,
      cellClass: "flex justify-center",
      unSortIcon: true,
      sortable: true,
      comparator: () => 0,
      cellRenderer: "renderCoupon"
    },
    {
      field: "maturityDate",
      headerName: "تاریخ سررسید",
      unSortIcon: true,
      sortable: true,
      comparator: () => 0,
      width: 152,
      minWidth: 152,

      cellRenderer: ({ data }: { data: TBondTableData }) =>
        data ? (
          <div className=" text-base mt-1 ">
            {data?.maturityDate ? dateConverter(data?.maturityDate).format("YYYY/MM/DD") : ""} <br />
            <p className=" text-xs">
              {data?.daysToMaturityDate > 0 ? (
                <div className="flex items-center gap-0.5">
                  <span>{commaSeparator(data?.daysToMaturityDate)}</span>{" "}
                  <span className="[word-spacing:1px]">روز مانده</span>
                </div>
              ) : (
                ""
              )}
            </p>
          </div>
        ) : (
          "---"
        )
    },
    {
      field: "duration",
      headerName: "دیرش",
      unSortIcon: true,
      sortable: true,
      comparator: () => 0,
      width: 79,
      minWidth: 79,

      cellRenderer: "renderFloatNumber"
    },
    {
      field: "modifiedDuration",
      headerName: "دیرش تعدیل شده",
      unSortIcon: true,
      sortable: true,
      comparator: () => 0,
      width: 140,
      minWidth: 140,

      cellRenderer: "renderFloatNumber"
    },
    {
      field: "convexity",
      headerName: "تحدب",
      unSortIcon: true,
      sortable: true,
      comparator: () => 0,
      width: 79,
      minWidth: 79,

      cellRenderer: "renderFloatNumber"
    }
  ];
}
export function CustomLoadingOverlay() {
  const { isCollapsed } = useToggleCollapse();
  const colNumbers = isCollapsed ? 15 : 10;

  return <TableLoading colNumbers={colNumbers} rowNumbers={12} />;
}

export function CustomNoRows() {
  return <div className="flex items-center justify-center text-white h-full">نمادی یافت نشد</div>;
}

export const sortKeyName: { [key: string]: string } = {
  symbol: "SortOrderBondName",
  closePrice: "SortOrderClosingPriceYtm",
  lastTradePrice: "SortOrderLastTradePriceYtm",
  bestBuyPrice: "SortOrderBestBuyPriceYtm",
  bestSellPrice: "SortOrderBestSellPriceYtm",
  totalTradedVolume: "SortOrderTotalTradedVolume",
  maturityDate: "SortOrderDaysToMaturityDate",
  bondType: "SortOrderBondType",
  duration: "SortOrderDuration",
  modifiedDuration: "SortOrderModifiedDuration",
  convexity: "SortOrderConvexity",
  lastTradeDate: "SortOrderLastTradeDate"
};
