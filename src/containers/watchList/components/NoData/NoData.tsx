import { Table } from "@/components/organisms/Table";
import { ColDef, ColGroupDef, GridOptions } from "ag-grid-community";
import React, { Dispatch, SetStateAction } from "react";
import { IAddSymbolPost } from "@/queries/watchListAPI";
import styles from "../../WatchListTable.module.scss";
import AddSymbole from "../AddSymbole";
import { IIsinInputSearchItem } from "../WatchListIsinInput/type";

function NoData({
  columnDefs,
  isinInputValue,
  onSubmit,
  setIsinInputValue
}: {
  columnDefs: (ColDef<any> | ColGroupDef<any>)[] | null | undefined;
  isinInputValue: IIsinInputSearchItem | undefined;
  onSubmit: (data: IAddSymbolPost) => Promise<void>;
  setIsinInputValue?: Dispatch<SetStateAction<IIsinInputSearchItem | undefined>>;
}) {
  const gridOptions: GridOptions = {
    enableRtl: true,
    sortingOrder: ["asc", "desc"],
    noRowsOverlayComponent: AddSymbole,
    noRowsOverlayComponentParams: {
      isinInputValue,
      onSubmit,
      setIsinInputValue
    }
  };

  return (
    <Table
      className={styles.watchListTableContainer}
      noRowsOverlayComponent={AddSymbole}
      columnDefs={columnDefs}
      gridOptions={gridOptions}
      data={[]}
    />
  );
}

export default NoData;
