/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import React, { forwardRef, Ref, useEffect, useState } from "react";
import { twMerge } from "tailwind-merge";
import styles from "@/components/organisms/filter/Filter.module.scss";
import Input from "@/components/atoms/input";
import { PopOver } from "@/components/atoms/popper";
import AddIcon from "@/assets/icons/addIcon.svg";
import { ConvertEnToFa } from "@/utils/helpers";
import { IIsinInputProps, IIsinInputSearchItem } from "./type";

const WatchListIsinInput = forwardRef((props: IIsinInputProps, ref: Ref<HTMLInputElement>) => {
  const { onChange, value, preserveErrorMessage, onFocus, dataTestId, watchListDataSearch } = props;
  const [isOpen, setIsOpen] = useState(false);

  const [filterText, setFilterText] = useState<string | undefined>();

  const [filteredData, setFilteredData] = useState(watchListDataSearch?.data?.items);

  useEffect(() => {
    setFilterText(value);
  }, [value]);

  const searchIsins = (e: string) => {
    if (e) setIsOpen(true);
    else setIsOpen(false);
    const filteredItems = watchListDataSearch?.data?.items?.length
      ? watchListDataSearch?.data?.items?.filter((item: any) => item?.symbol?.includes(ConvertEnToFa(e)))
      : [];
    setFilteredData(filteredItems);
  };

  const content = (
    <div className="z-50 w-[326px]">
      <div
        className={twMerge(
          "bg-cardBackground w-full -mt-[14px] rounded border border-solid border-borderBorderAndDivider h-[304px] overflow-auto",
          styles?.customScrollBar
        )}
      >
        <div className="flex flex-col text-mainText py-[6px]">
          {filteredData?.length ? (
            filteredData?.map((item: IIsinInputSearchItem) => (
              <div
                data-test="63faf718-0782-43a8-88f0-c4529d22c937"
                key={item?.symbol}
                className={twMerge(
                  "flex justify-between w-full py-2 pr-3 pl-2  h-8 border border-solid border-transparent",
                  !item?.isInWatchlist &&
                    "cursor-pointer hover:bg-cardDarkestBlue active:bg-cardDarkestBlue active:border active:border-solid active:border-semanticPrimary"
                )}
                onClick={() => {
                  setIsOpen(false);
                  setFilterText(item?.symbol);
                  onChange?.(item);
                }}
              >
                <div
                  key={item?.isin}
                  className={twMerge(
                    "w-full text-[12px] ",
                    item?.isInWatchlist ? "text-textDisabled" : "text-mainText"
                  )}
                  data-test={`${item?.isin}-a156eebb-ad75-4cd0-ab30-181f9a7cb86f`}
                >
                  {item?.symbol}
                </div>

                {item?.isInWatchlist ? (
                  <div className="text-[10px] text-textDisabled text-nowrap">افزوده شده</div>
                ) : (
                  <div>
                    <AddIcon className="w-4 h-4" />
                  </div>
                )}
              </div>
            ))
          ) : (
            <div className="justify-center text-[14px] flex">هیچ موردی یافت نشد</div>
          )}
        </div>
      </div>
    </div>
  );
  return (
    <div className="w-[326px]">
      <Input
        {...props}
        ref={ref}
        onChange={(e: string) => {
          if (!e) onChange?.();
          setFilterText(e);
          searchIsins(e);
        }}
        onFocus={() => onFocus?.()}
        value={filterText}
        preserveErrorMessage={preserveErrorMessage}
        data-test={`${dataTestId ?? ""}c41f1d01-f373-485d-bfc4-01300b4dbd91`}
        inputSize="small"
      />

      <PopOver setIsOpen={setIsOpen} isOpen={isOpen} content={content} placement="bottom" />
    </div>
  );
});

export default WatchListIsinInput;
