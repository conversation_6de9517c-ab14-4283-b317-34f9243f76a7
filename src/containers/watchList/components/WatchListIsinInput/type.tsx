import { ISearchIsinByNameData } from "@/queries/calculatorAPI/types";
// eslint-disable-next-line import/no-cycle
import { IWatchListData } from "@/queries/watchListAPI";

export interface IIsinInputProps {
  dataTestId?: string;
  endAdornment?: React.ReactNode;
  className?: string;
  title?: string;
  onChange: (v?: IIsinInputSearchItem) => void;
  value?: string;
  cleared?: boolean;
  preserveErrorMessage: boolean;
  onFocus?: (v?: ISearchIsinByNameData) => void;
  watchListDataSearch?: IWatchListData;
}

export interface IIsinInputSearchItem {
  isin: string;
  symbol: string;
  instrumentName: string;
  isInWatchlist: string;
}
