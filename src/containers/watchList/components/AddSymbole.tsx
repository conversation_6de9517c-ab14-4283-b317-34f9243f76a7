import React, { useRef, useState } from "react";
import Button from "@/components/atoms/button";
import { successToast } from "@/utils/toast";
import AddIcon from "@/assets/icons/addIcon.svg";
import Add from "@/assets/icons/add.svg";
import TickSuccess from "@/assets/icons/tickSuccess.svg";
import { MAX_ROWS } from "@/utils/helpers";
import { useGetWatchListSearchQuery } from "@/queries/watchListAPI";
import { Spinner } from "@/components/atoms/spinner";
import WatchListIsinInput from "./WatchListIsinInput/WatchListIsinInput";
import { IIsinInputSearchItem } from "./WatchListIsinInput/type";

interface IAddSymbolProps {
  isinInputValue: IIsinInputSearchItem | undefined;
  onSubmit: ({ isin }: { isin: string }) => void;
  setIsinInputValue: (value: IIsinInputSearchItem | undefined) => void;
}

function AddSymbole({ isinInputValue, onSubmit, setIsinInputValue }: IAddSymbolProps) {
  const [showInput, setShowInput] = useState(false);

  const searchParams = {
    pageNumber: 1,
    pageSize: MAX_ROWS
  };
  const { data: watchListDataSearch, isLoading } = useGetWatchListSearchQuery(searchParams);

  const watchListInputRef = useRef<HTMLInputElement>(null);

  const handleAddIsinClick = () => {
    setShowInput(true);
  };

  if (isLoading) {
    return (
      <div className="w-full h-full flex justify-center items-center">
        <Spinner />
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col justify-center items-center gap-6 !pointer-events-auto pt-1">
      <div className="text-white text-base font-light leading-[25px] flex justify-center items-center">
        هنوز اوراقی به دیدبان اضافه نشده !
      </div>
      {!showInput && (
        <Button
          data-test="ba0fb73a-b2cb-4c62-929b-89d588924402-s1s2"
          startAdornment={<Add className="w-[17px] h-[17px]" />}
          onClick={handleAddIsinClick}
        >
          افزودن اوراق
        </Button>
      )}

      {showInput && (
        <WatchListIsinInput
          watchListDataSearch={watchListDataSearch}
          dataTestId="watch-list-table-"
          endAdornment={<AddIcon className="w-4 h-[17px]" />}
          className="h-10"
          ref={watchListInputRef}
          title="افزودن اوراق"
          value={isinInputValue?.symbol}
          preserveErrorMessage={false}
          onChange={v => {
            if (v?.isInWatchlist) return;
            if (v?.isin) {
              successToast({
                title: `نماد ${v?.symbol} با موفقیت به دیدبان اضافه شد`,
                successIcon: (
                  <div className="w-6 h-6 rounded bg-inputFill flex justify-center items-center">
                    <TickSuccess className="w-3 h-[9px]" />
                  </div>
                )
              });
              onSubmit({ isin: v?.isin ?? "" });
            }
            setIsinInputValue(v);
          }}
        />
      )}
    </div>
  );
}

export default AddSymbole;
