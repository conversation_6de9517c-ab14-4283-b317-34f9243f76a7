export interface IBoundTableLimitOrder {
  ytm: number;
  buyAmount: number;
  buyPrice: number;
  buyTime: string;
  buyVolume: number;
  buyYtm: number;
  buyYtmInPercent: number;
  index: number;
  sellAmount: number;
  sellPrice: number;
  sellTime: string;
  sellVolume: number;
  sellYtm: number;
  sellYtmInPercent: number;
}

export interface IBondTableSocket {
  marketUpdateType: number;
  message: {
    closePrice: number;
    closePriceYtmYesterdayChange: number;
    instrument: {
      isin: string;
      symbol: string;
      title: string;
    };
    limitOrders?: IBoundTableLimitOrder[];
  };
}
