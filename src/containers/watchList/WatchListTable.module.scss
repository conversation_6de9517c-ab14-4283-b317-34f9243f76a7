.watchListTableContainer {
  :global {
    .ag-header {
      min-height: 40px !important;
      height: 40px !important;
    }

    .ag-header-row {
      height: 40px !important;
      min-height: 40px !important;
    }
    .ag-sort-none-icon {
      padding: 0 !important;
    }
    .ag-sort-indicator-container {
      width: 14px !important;
      padding-right: 3px;
    }
    .ag-header-cell {
      padding-right: 12px;
    }

    .ag-header-row .ag-header-cell:not(:first-child) {
      padding-right: 0 !important;
    }

    .ag-header-row img.sort-selected-icon {
      top: 0;
    }

    .ag-sort-indicator-icon {
      padding-top: 3px;
    }
    .ag-header-row .ag-header-cell:nth-child(1) {
      padding-right: 11px !important;
      padding-top: 2px;
    }

    .ag-header-row .ag-header-cell:nth-child(2) {
      width: 70px !important;
      padding-right: 28px !important;
    }

    .ag-header-row .ag-header-cell:nth-child(3) {
      padding-right: 10px !important;
    }

    .ag-header-row .ag-header-cell:nth-child(4) {
      padding-right: 9px !important;
    }

    .ag-header-row .ag-header-cell:nth-child(5) {
      padding-right: 2px !important;
    }

    .ag-header-row .ag-header-cell:nth-child(6) {
      margin-left: 10px !important;
    }

    .ag-header-row .ag-header-cell:nth-child(7) {
      margin-left: 17px !important;
    }

    .ag-header-row .ag-header-cell:nth-child(8) {
      margin-left: 26px !important;
    }

    .ag-header-row .ag-header-cell:nth-child(9) {
      margin-left: 42px !important;
    }

    .ag-header-row .ag-header-cell:nth-child(10) {
      margin-left: 70px !important;
    }

    .ag-header-row .ag-header-cell:nth-child(11) {
      margin-left: 115px !important;
    }

    .ag-header-row .ag-header-cell:nth-child(12) {
      margin-left: 90px !important;
    }

    .ag-header-row .ag-header-cell:nth-child(13) {
      margin-left: 100px !important;
    }

    .ag-cell-value:nth-child(9) {
      margin-left: 20px !important;
    }

    .ag-cell-value:nth-child(10) {
      margin-left: 70px !important;
    }

    .ag-cell-value:nth-child(11) {
      margin-left: 130px !important;
    }

    .ag-cell-value:nth-child(12) {
      margin-left: 80px !important;
    }

    .ag-cell-value:nth-child(13) {
      margin-left: 120px !important;
    }
  }
}
