/* eslint-disable @typescript-eslint/no-unused-expressions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
// import { TLoginBody } from "@/queries/authAPI/types";

import ArrowRight from "@/assets/arrow-right.svg";
import <PERSON><PERSON><PERSON><PERSON> from "@/assets/saba-logo.svg";
import { animated, easings, useSpring } from "@react-spring/web";
import { useState } from "react";
import { twMerge } from "tailwind-merge";

import LoginForm from "./LoginForm";
import ResetPassForm from "./ResetPassForm";

function Login() {
  const [resetPass, setResetPass] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");

  const resetStyleProps = useSpring({
    to: resetPass ? { transform: "translateX(0px)" } : { transform: "translateX(700px)" },
    delay: 200,
    config: {
      easing: easings.linear,
      duration: 300
    }
  });

  const loginStyleProps = useSpring({
    to: resetPass ? { transform: "translateX(700px)" } : { transform: "translateX(0px)" },
    delay: 200,
    config: {
      easing: easings.linear,
      duration: 300
    }
  });

  return (
    <div className="flex w-full h-[100dvh] bg-darkBlack">
      <div className="flex w-full mr-9 2xl:mr-0  2xl:mt-[6.6%]  ">
        <div className="flex h-full relative  w-full  flex-col   overflow-hidden pt-[6.1%] 2xl:mr-[12.2%]">
          <div className="text-textWhite  mr-[25.5%] max-w-[320px] pt-2 text-center text-[27px] font-semibold text-mediumWhite">
            <div className=" flex flex-col items-center  pt-3 ">
              <SabaLogo className=" mb-4 ml-4" />
              <span className=" text-lg font-bold text-center "> سامانه تحلیلی بازار اوراق بدهی </span>
            </div>
            {resetPass && (
              <div
                className="absolute right-[13.5%] top-[205px] cursor-pointer text-sm font-normal text-mediumBlue"
                onClick={() => {
                  setResetPass(false);
                  setErrorMessage("");
                }}
                data-test="daf7de3f-b4c0-47ea-9f43-e4d8a65203ab"
              >
                <ArrowRight className="inline" />
                بازگشت
              </div>
            )}
          </div>

          <div className=" relative mt-[68px]">
            <animated.div
              style={{ ...loginStyleProps, width: "100%" }}
              className={twMerge(["absolute right-0 top-0 h-full  px-14"])}
            >
              <LoginForm setResetPass={setResetPass} errorMessage={errorMessage} setErrorMessage={setErrorMessage} />
            </animated.div>

            <animated.div
              style={{ ...resetStyleProps, width: "100%" }}
              className={twMerge(["absolute right-0 top-0 h-full  px-10"])}
            >
              <ResetPassForm setResetPass={setResetPass} errorMessage={errorMessage} />
            </animated.div>
          </div>
        </div>
      </div>

      <div className="w-full xl:h-[95%] xl:mt-[22px] bg-mackBook h-full bg-no-repeat xxl:h-auto 2xl:mt-7 2xl:bg-contain. 2xl:ml-[160px] bg-center " />
    </div>
  );
}

export default Login;
