import { z } from "zod";
import { PasswordSchema, TextareaSchema } from "@/components/organisms/formBuilder/utils";

export const FormSchema = z.object({
  username: z
    .string({ required_error: "نام کاربری اجباری است" })
    .nonempty("نام کاربری اجباری است")
    .describe("نام کاربری"),
  password: PasswordSchema(z.string().nonempty("رمز عبور اجباری است")).describe("رمز عبور")
});

export const ResetScheme = z.object({
  firstName: z.string({ required_error: "نام کاربری اجباری است" }).nonempty("نام کاربری اجباری است").describe("نام"),
  lastName: z
    .string({ required_error: "نام خانوادگی اجباری است" })
    .nonempty("نام خانوادگی اجباری است")
    .describe("نام خانوادگی"),
  mobileNumber: z
    .string({ required_error: " شماره تلفن اجباری است" })
    .nonempty(" شماره تلفن اجباری است")
    .describe("شماره تلفن"),
  description: TextareaSchema.describe("متن درخواست خود را بنویسید ...")
});

export const INCORRECT_PASSWORD = "E69CBC14-BE9D-11EE-AB0D-FFA131E130E0";
