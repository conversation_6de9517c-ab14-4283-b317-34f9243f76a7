/* eslint-disable @typescript-eslint/no-unused-expressions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
// import { TLoginBody } from "@/queries/authAPI/types";

import { FormBuilder } from "@/components/organisms/formBuilder";
import { useGoogleReCaptcha } from "react-google-recaptcha-v3";

import { useAddTicketMutation } from "@/queries/authAPI";
import { successToast, warningToast } from "@/utils/toast";
import { twMerge } from "tailwind-merge";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import values from "lodash/values";
import { IResetPassFormProps, TResetPass } from "./type";
import { ResetScheme } from "./utils";

function ResetPassForm({ errorMessage, setResetPass }: IResetPassFormProps) {
  const { executeRecaptcha } = useGoogleReCaptcha();

  const { mutate: addTicket, isPending } = useAddTicketMutation();

  const onReset = async (data: TResetPass) => {
    const { description, firstName, lastName, mobileNumber } = data;
    if (!executeRecaptcha) return;
    const captchaToken = await executeRecaptcha("reset");
    await addTicket(
      { description, firstName, lastName, mobileNumber, captchaToken },
      {
        onSuccess: res => {
          successToast({
            title: "پیام به پشتیبانی",
            subTitle: "پیام شما با موفقیت ارسال شد . منتظر پاسخگویی از سمت پشتیبانی باشید. از شکیبایی شما ممنونیم ."
          });
          setTimeout(() => res?.data && setResetPass(false), 500);
        },
        onError: error => {
          const errorData = error?.response?.data as any;

          warningToast({
            title: "خطا",
            subTitle: errorData?.errorMessage
          });
        }
      }
    );
  };

  const form = useForm<z.infer<typeof ResetScheme>>({
    resolver: zodResolver(ResetScheme),
    mode: "all"
  });
  const isError = !!values(form?.formState?.errors)?.length || !!errorMessage;

  return (
    <div className="max-w-[498px] mr-[8.5%]">
      <div className="pb-8  mt-3  text-base font-semibold text-mediumWhite">درخواست تغییر رمز</div>
      <FormBuilder
        schema={ResetScheme}
        form={form}
        onSubmit={onReset}
        formProps={{
          id: "forget-pass-form",
          isLoading: isPending,
          disabled: isPending,
          buttonText: "ارسال درخواست",
          containerClass: "grid grid-cols-2 w-full",
          bodyClassName: "",
          buttonClass: "mt-[4px] py-[12px] !px-[26px] w-full",
          footerClassName: twMerge("w-[497px] flex-row-reverse ", isError ? "pt-5" : "pt-7")
        }}
        props={{
          lastName: {
            wrapperClassName: "pr-3",
            inputSize: "large",

            isRequired: true,
            "data-test": "ef3089d4-5d4e-4e23-a1b9-7763a518e08d"
          },
          firstName: {
            wrapperClassName: "pl-3",
            inputSize: "large",

            isRequired: true,
            "data-test": "0ff9d6c5-e144-4a5d-83fc-cd5510d8ad6a"
          },
          mobileNumber: {
            wrapperClassName: "col-span-2",
            inputSize: "large",

            isError: !!errorMessage,
            errorMessage,
            isRequired: true,
            "data-test": "9dfbaaca-c1d7-48a5-9358-eadf531f9adf"
          },
          description: {
            wrapperClassName: "col-span-2",
            className: "min-h-[130px]",
            maxCharacter: 2600,
            "data-test": "721c6ee9-7d52-40b5-b575-3a71ca7b20ba"
          }
        }}
      />

      <div className=" mb-1 mt-[60px] text-center">
        <span className="mx-2 text-xs font-normal text-[#BAE7FF]">تماس با پشتیبانی :</span>
        <span className="  text-base font-bold text-[#BAE7FF]">۸۸۴۸۰۴۰۲-۰۲۱</span>
      </div>
      <p className="mx-auto text-center text-xs font-normal text-[#BDBDBD]">محصولی از شرکت خدمات مدیریت صباتامین</p>
    </div>
  );
}

export default ResetPassForm;
