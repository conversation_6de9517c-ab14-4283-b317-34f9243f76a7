export type TResetPass = { firstName: string; lastName: string; mobileNumber: string; description: string };

export type TLogin = { username: string; password: string };

export interface IResetPassFormProps {
  errorMessage: string;
  setResetPass: (value: boolean) => void;
}

export interface ILoginFormProps {
  setErrorMessage: (error: string) => void;
  setResetPass: (value: boolean) => void;
  errorMessage: string;
}
