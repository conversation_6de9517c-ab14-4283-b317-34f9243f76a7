/* eslint-disable react/button-has-type */
/* eslint-disable @typescript-eslint/no-unused-expressions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
// import { TLoginBody } from "@/queries/authAPI/types";

import { useRouter } from "next/navigation";
import { useGoogleReCaptcha } from "react-google-recaptcha-v3";

import { FormBuilder } from "@/components/organisms/formBuilder";
import cookies from "@/constants/cookies";
import routes from "@/constants/routes";
import { useLoginMutation } from "@/queries/authAPI";
import { setCookie } from "@/utils/cookie";
import { warningToast } from "@/utils/toast";
import { zodResolver } from "@hookform/resolvers/zod";
import values from "lodash/values";
import { useForm } from "react-hook-form";
import { twMerge } from "tailwind-merge";
import { z } from "zod";
import { ILoginFormProps, TLogin } from "./type";
import { FormSchema, INCORRECT_PASSWORD } from "./utils";

function LoginForm({ setErrorMessage, errorMessage, setResetPass }: ILoginFormProps) {
  const router = useRouter();
  const { executeRecaptcha } = useGoogleReCaptcha();

  const { mutate: login, isPending } = useLoginMutation();

  const onLogin = async (data: TLogin) => {
    const { username, password } = data;
    if (!executeRecaptcha) return;
    const captchaToken = await executeRecaptcha("login");
    await login(
      {
        username,
        password,
        captchaToken
      },
      {
        onSuccess: () => {
          setCookie(cookies.isLoggedIn, "true");
          router.replace(routes.home);
        },
        onError: error => {
          const errorData = error?.response?.data as any;

          if (errorData.errorCode === INCORRECT_PASSWORD) {
            setErrorMessage(errorData?.errorMessage);
          } else
            warningToast({
              title: "خطا سرور",
              subTitle: errorData?.errorMessage
            });
        }
      }
    );
  };

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    mode: "all"
  });
  const isError = !!values(form?.formState?.errors)?.length || !!errorMessage;

  return (
    <div className="max-w-[320px] mr-[21%]">
      <div className=" pb-7 pt-[28px] mb-1  text-base font-semibold text-mediumWhite">ورود کاربر</div>
      <FormBuilder
        schema={FormSchema}
        onSubmit={onLogin}
        formProps={{
          id: "loginForm",
          buttonText: <div className="flex items-center gap-1.5 w-full">ورود</div>,
          isLoading: isPending,
          footerClassName: "flex-col items-start",
          disabled: isPending,
          containerClass: "flex flex-col w-full",
          bodyClassName: "",
          buttonClass: twMerge("py-[12px] !px-[20px] w-full ", isError ? "mt-[10px]" : "mt-[42px]"),
          customActionElement: (
            <div
              className="cursor-pointer text-[14px] font-normal underline text-smokeWhite"
              onClick={() => {
                setResetPass(true);
                setErrorMessage("");
              }}
              data-test="ceaff9b2-e6f7-4cc4-a5a6-d9a0e3d99a9f"
            >
              فراموشی رمز عبور
            </div>
          )
        }}
        props={{
          username: {
            inputSize: "large",
            isError: !!errorMessage,
            errorMessage,
            inputWrapperProps: {
              hintClassName: "mb-4 block"
            },
            "data-test": "64dee3ca-87c2-4c17-95ae-01afc8d4e01e"
          },
          password: {
            inputSize: "large",
            isError: !!errorMessage,
            preserveErrorMessage: false,
            errorMessage,
            "data-test": "98e1eb98-87b2-4112-a3c9-6a23b29c3fea"
          }
        }}
      />

      <div className=" mb-1 mt-[150px] text-center">
        <span className="mx-2 text-xs font-normal text-[#BAE7FF]">تماس با پشتیبانی :</span>
        <span className="  text-base font-bold text-[#BAE7FF]">۸۸۴۸۰۴۰۲-۰۲۱</span>
      </div>
      <p className="mx-auto text-center text-xs font-normal text-[#BDBDBD]">محصولی از شرکت خدمات مدیریت صباتامین</p>
    </div>
  );
}

export default LoginForm;
