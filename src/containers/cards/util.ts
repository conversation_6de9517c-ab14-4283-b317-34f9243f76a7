/* eslint-disable @typescript-eslint/no-use-before-define */
/* eslint-disable no-plusplus */
/* eslint-disable @typescript-eslint/naming-convention */
/* eslint-disable guard-for-in */
/* eslint-disable no-restricted-syntax */

import { numbersUnit } from "@/utils/helpers";

import { dateConverter } from "@/utils/DateHelper";
import getLineChartOptions, { defaultColors, renderRecord } from "../charts/util";
import {
  FOCUS_SUB_TABS,
  FUND,
  FUND_SUB_TABS,
  FUND_TABS,
  TFundRiskRow,
  TFundRow,
  TSummaryMarketRiskOptions
} from "./type";

export const isNullOrEmpty = (v?: number | null) => v == null || v === undefined;

export const convertFundData = (flatData: TFundRiskRow[]) => {
  const data: TFundRow[] = [];

  flatData.forEach(row => {
    for (const key in FUND) {
      const { title, desc = "", enum1 } = FUND[key];
      if (enum1.includes(row.fundRiskIndex)) {
        const f: number = data.findIndex(c => c.id === key);
        if (f === -1) {
          data.push({ id: key, title, desc, past: row, future: null });
        } else {
          data[f].future = row;
        }
      }
    }
  });

  return data;
};

export const getSummaryMarketRisksOptions = (future: number, past: number, checked?: boolean) => {
  const list = [];

  if (future !== -1) {
    list.push({ color: "#26D5C0", value: future });
  }

  if (checked) {
    list.push({ color: "#C7DA41", value: past });
  }
  return list;
};

export const getSummaryMarketEfficiencyOptions = (params: TSummaryMarketRiskOptions) => {
  const { future, past, real, isShowRetrospect, isShowExpected, isShowReal } = params;

  const list = [];

  if (isShowExpected) {
    list.push({ color: "#26D5C0", value: future });

    if (isShowRetrospect) {
      list.push({ color: "#C7DA41", value: past });
    }
  }

  if (isShowReal) {
    list.push({ color: "#F4F4F4", value: real });
  }

  return list;
};

export const getSummaryInterestOptions = (value1: number, value2?: number, colors?: string[]) => {
  const list = [{ color: colors && colors[0] ? colors[0] : "#55C3FF", value: value1 }];
  if (value2) {
    list.push({ color: colors && colors[1] ? colors[1] : "#43E5A0", value: value2 });
  }
  return list;
};

export const getSummaryInterestOptions2 = (values: number[], colors: string[]) => {
  const list: any[] = [];

  values.forEach((value, index) => {
    list.push({ value, color: colors[index] });
  });

  return list;
};

export const getSeriesOfMarketRiskFormat = (props: {
  graphPoints: any[];
  checked: boolean;
  futureField: string;
  pastField: string;
  hasPercent?: boolean;
  colorsArg?: string[];
}) => {
  const { graphPoints, checked, futureField, pastField, hasPercent, colorsArg } = props;
  const count = graphPoints?.length || 0;
  let colors = colorsArg || defaultColors;

  if (futureField === "") {
    colors = colors.slice(1);
  }

  const futureData = graphPoints?.map((i, index) =>
    renderRecord({ dateTime: i.checkpointDate, value: i[futureField], index, count, color: colors?.[0] || null })
  );

  const pastData = graphPoints?.map((i, index) =>
    renderRecord({ dateTime: i.checkpointDate, value: i[pastField], index, count, color: colors?.[1] || null })
  );

  const series = [];

  if (futureField !== "") {
    series.push({ name: "آینده نگر", data: futureData || [], type: "area", fillColor: "transparent" });
  }

  series.push({ name: "گذشته نگر", data: pastData || [], visible: checked, type: "area", fillColor: "transparent" });

  return getLineChartOptions({ data: series, hasPercent, chartColors: colors });
};

export const getSeriesOfEfficiencyMarketRiskFormat = (props: {
  graphPoints: any[];
  isShowRetrospect: boolean;
  isShowExpected: boolean;
  isShowReal: boolean;
  futureField: string;
  pastField: string;
  realField?: string;
  hasPercent?: boolean;
  colorsArg?: string[];
}) => {
  const {
    graphPoints,
    isShowRetrospect,
    isShowExpected,
    isShowReal,
    futureField,
    pastField,
    realField,
    hasPercent,
    colorsArg
  } = props;

  const count = graphPoints?.length || 0;
  const colors = colorsArg || defaultColors;

  const futureData = graphPoints?.map((i, index) =>
    renderRecord({ dateTime: i.checkpointDate, value: i[futureField], index, count, color: colors?.[0] || null })
  );

  const pastData = graphPoints?.map((i, index) =>
    renderRecord({ dateTime: i.checkpointDate, value: i[pastField], index, count, color: colors?.[1] || null })
  );

  const realData = realField
    ? graphPoints?.map((i, index) =>
        renderRecord({ dateTime: i.checkpointDate, value: i[realField], index, count, color: colors?.[2] || null })
      )
    : [];

  const series = [];

  if (futureField !== "") {
    series.push({
      name: "آینده نگر",
      data: futureData || [],
      type: "area",
      fillColor: "transparent",
      visible: isShowExpected
    });
  }

  series.push({
    name: "گذشته نگر",
    data: pastData || [],
    visible: isShowRetrospect && isShowExpected,
    type: "area",
    fillColor: "transparent"
  });

  if (realData) {
    series.push({ name: "واقعی", visible: isShowReal, data: realData || [], type: "area", fillColor: "transparent" });
  }

  return getLineChartOptions({ data: series, hasPercent, chartColors: colors });
};

export const getSeriesOfInterestRiskFormat = (props: {
  graphPoints: any[];
  field1: { id: string; title: string };
  field2?: { id: string; title: string };
  moreFields?: { id: string; title: string }[];
  colors?: string[];
  checked?: boolean;
  hasPercent?: boolean;
}) => {
  const { graphPoints, field1, field2, moreFields, colors, hasPercent, checked = true } = props;
  const count = graphPoints?.length || 0;
  const chartColors = colors || ["#55C3FF", "#43E5A0"];
  let colorIndex = 0;

  const data1 = graphPoints
    ?.filter(item => item?.[field1?.id])
    ?.map((i, index) =>
      renderRecord({
        dateTime: i.checkpointDate,
        value: i[field1.id],
        index,
        count,
        color: chartColors?.[colorIndex] || null
      })
    );

  const series = [{ name: field1.title, data: data1 || [], type: "area", visible: true, fillColor: "transparent" }];

  if (field2) {
    colorIndex++;

    const data2 = graphPoints
      ?.filter(item => item?.[field2?.id])
      ?.map((i, index) =>
        renderRecord({
          dateTime: i.checkpointDate,
          value: i[field2?.id],
          index,
          count,
          color: chartColors?.[colorIndex] || null
        })
      );

    series.push({
      name: field2.title,
      data: data2 || [],
      type: "area",
      visible: checked,

      fillColor: "transparent"
    });
  }

  if (moreFields) {
    moreFields?.forEach(f => {
      colorIndex++;

      const data = graphPoints
        ?.filter(item => item?.[f?.id])
        ?.map((i, index) =>
          renderRecord({
            dateTime: i.checkpointDate,
            value: i[f.id],
            index,
            count,
            color: chartColors?.[colorIndex] || null
          })
        );

      series.push({ name: f.title, data: data || [], type: "area", visible: true, fillColor: "transparent" });
    });
  }

  return getLineChartOptions({ data: series, chartColors, hasPercent });
};

const hexToRgba = (hex: string, alpha: number) => {
  const r = parseInt(hex.slice(1, 3), 16);
  const g = parseInt(hex.slice(3, 5), 16);
  const b = parseInt(hex.slice(5, 7), 16);
  return `rgba(${r}, ${g}, ${b}, ${alpha})`;
};

export const getSeriesOfInfoCard = (props: { graphPoints: any[]; field1: { id: string; title: string } }) => {
  const { graphPoints, field1 } = props;
  const count = graphPoints?.length || 0;
  const chartColors = ["#2A9AB0"];

  // map start
  const data1 = graphPoints
    ?.filter(item => item?.[field1?.id])
    ?.map((i, index) => {
      const list = renderRecord({
        dateTime: i.checkpointDate,
        value: i[field1.id],
        index,
        count,
        color: chartColors?.[0] || null
      });

      if (index < count - 1) {
        return { ...list, marker: { enabled: false } };
      }

      return { ...list, marker: { enabled: true, radius: 3, states: { hover: { enabled: false } } } };
    });
  // map end

  const series = [{ name: field1.title, data: data1 || [], type: "area", visible: true, fillColor: "transparent" }];

  const options = getLineChartOptions({ data: series, chartColors, hasPercent: false });
  const values = data1.map(i => i.y) || [];
  const max = Math.max.apply(null, values);
  const tickInterval = Math.floor(max / 4);

  return {
    ...options,
    chart: { ...options.chart, marginTop: 8, marginBottom: 5, marginRight: 5, zooming: { enabled: false } },
    xAxis: { visible: false },
    yAxis: {
      ...options.yAxis,
      startOnTick: false,
      gridLineColor: "#545454",
      tickInterval,
      labels: {
        useHTML: true,
        /* @ts-ignore */
        // eslint-disable-next-line
        formatter: function () {
          /* @ts-ignore */
          return this.value ? `${Math.trunc(toBillion(this.value))}B` : "0";
        },
        style: { color: "#F4F4F4", fontFamily: "var(--font-yekan)", fontSize: "10px" }
      }
    },
    tooltip: { enabled: false },
    plotOptions: { ...options.plotOptions, series: { ...options.plotOptions?.series, enableMouseTracking: false } }
  };
};

export const getSeriesOfTransactionFormat = ({
  data
}: {
  data?: {
    checkpointDate: string;
    totalDebitAmount: number;
    totalDepositAmount: number;
    depositTransactions: {
      reasonCategory: number;
      symbol: string;
      reasonDescription: string;
      contractNumber: string | null;
      bondOriginType: number;
      amount: number;
    }[];
    withdrawalTransactions: {
      reasonCategory: number;
      symbol: string;
      reasonDescription: string;
      contractNumber: string | null;
      bondOriginType: number;
      amount: number;
    }[];
  }[];
}) => {
  // Create multiple series for deposit transactions with gradient color
  const createDepositSeries = () => {
    const maxDepositCount = data?.length ? Math.max(...data.map(item => item.depositTransactions.length || 0)) : 0;
    const depositSeries = [];

    for (let i = 0; i < maxDepositCount; i++) {
      depositSeries.push({
        name: `ورودی ${i + 1}`,
        stack: "chartName",
        colorByPoint: true,

        data: data
          ?.filter(item => !!item?.depositTransactions?.length)
          ?.map(item => {
            const tx = item.depositTransactions[i];
            const amount = tx?.amount ?? 0;
            const colorHex = BondOriginTypes?.[tx?.reasonCategory as keyof typeof BondOriginTypes]?.color || "#00AA63"; // fallback green
            const baseOpacity = 0.4 + ((i + 1) / maxDepositCount) * 0.6;

            return {
              x: new Date(item.checkpointDate).getTime(),
              y: tx ? amount : 0,
              color: {
                linearGradient: { x1: 0, y1: 0, x2: 0, y2: 1 },
                stops: [
                  [0, hexToRgba(colorHex, baseOpacity)],
                  [1, hexToRgba(colorHex, baseOpacity * 0.8)]
                ]
              }
            };
          })
      });
    }

    return depositSeries;
  };

  // Create multiple series for withdrawal transactions with gradient color

  const createWithdrawalSeries = () => {
    const maxWithdrawalCount = data?.length
      ? Math.max(...data.map(item => item.withdrawalTransactions.length || 0))
      : 0;
    const withdrawalSeries = [];

    for (let i = 0; i < maxWithdrawalCount; i++) {
      withdrawalSeries.push({
        name: `خروج ${i + 1}`,
        stack: "chartName",

        // We will use color per point below
        colorByPoint: true,

        data: data
          ?.filter(item => !!item?.withdrawalTransactions?.length)
          ?.map(item => {
            const tx = item.withdrawalTransactions[i];
            const amount = tx?.amount ?? 0;
            const colorHex = BondOriginTypes[tx?.reasonCategory as keyof typeof BondOriginTypes]?.color || "#E51A1A"; // fallback color
            const baseOpacity = 0.4 + ((i + 1) / maxWithdrawalCount) * 0.6;

            return {
              x: new Date(item.checkpointDate).getTime(),
              y: tx ? -amount : 0,
              color: {
                linearGradient: { x1: 0, y1: 1, x2: 0, y2: 0 },
                stops: [
                  [0, hexToRgba(colorHex, baseOpacity)],
                  [1, hexToRgba(colorHex, baseOpacity * 0.8)]
                ]
              }
            };
          })
      });
    }

    return withdrawalSeries;
  };

  // Combine deposit and withdrawal series
  const allSeries = [...createDepositSeries(), ...createWithdrawalSeries()];

  const allYValues = allSeries.flatMap(series => (series?.data ?? []).map(point => point.y));

  const dataMax = Math.max(...allYValues);

  // Calculate the total number of data points for scrollbar configuration
  const totalDataPoints = data?.length || 0;
  const maxVisiblePoints = 20;

  // Calculate the initial min and max for the xAxis to show only first 32 columns
  const sortedDates = data?.map(item => new Date(item.checkpointDate).getTime()).sort((a, b) => a - b) || [];
  const initialMin = sortedDates[0];
  const initialMax = sortedDates[Math.min(maxVisiblePoints - 1, sortedDates.length - 1)];

  return {
    chart: {
      type: "column",
      animation: false,
      backgroundColor: "transparent",
      marginTop: 50,
      zooming: { type: "x" },
      // Enable panning for better user experience
      panning: {
        enabled: true,
        type: "x"
      },
      panKey: "shift"
    },
    title: { text: "" },
    subtitle: { text: "" },

    // Add scrollbar configuration
    scrollbar: {
      enabled: totalDataPoints > maxVisiblePoints,
      liveRedraw: true,
      margin: 0,
      height: 8,
      buttonArrowColor: "#F4F4F4",
      buttonBackgroundColor: "#545454",
      buttonBorderColor: "#858585",
      rifleColor: "#F4F4F4",
      trackBackgroundColor: "#28282C",
      trackBorderColor: "#545454"
    },

    threshold: 10,
    xAxis: {
      // Set initial min and max to show only first 32 columns
      min: totalDataPoints > maxVisiblePoints ? initialMin : undefined,
      max: totalDataPoints > maxVisiblePoints ? initialMax : undefined,

      // Enable scrollbar on xAxis
      scrollbar: {
        enabled: totalDataPoints > maxVisiblePoints
      },

      // crosshair: true
      //   ? {
      //       width: 2,
      //       color: "#858585",
      //       dashStyle: "shortdot"
      //     }
      //   : false,
      tickPositioner() {
        // Get the month boundaries from our data
        // const monthBoundaries = getJalaliMonthBoundaries(data);

        // if (props?.options?.xAxis?.tickPositioner) {
        //   return props?.options?.xAxis?.tickPositioner;
        // }
        return undefined;
      },
      // ordinal: true,

      // tickPositioner: props?.options?.xAxis?.tickPositioner || (() => undefined),
      labels: {
        formatter() {
          const d = new Date((this as any)?.value);
          const format = "YYYY/MM/DD";
          return dateConverter(d.toString()).locale("fa").format(format);
        },
        y: 34,
        style: {
          color: "#F4F4F4",
          fontFamily: "yekan-bakh",
          fontSize: "12px",
          fontWeight: "400"
        }
      }
    },
    // xAxis: {
    // type: "datetime",
    // categories,
    // tickPositioner() {
    //   // Get the month boundaries from our data
    //   const monthBoundaries = getJalaliMonthBoundaries(categories);
    //   // return showMonthlyPeriod ? monthBoundaries : undefined;
    // },
    // plotBands:
    //   Number(categories?.length) > 1 &&
    //   categories?.map((item, index) => ({
    //     color: "rgba(61, 61, 61, 0.3)",
    //     from: index ? index - 0.45 : -1,
    //     to: index + 0.45
    //   })),
    // tickLength: 0,
    // lineColor: "#545454",
    // lineWidth: 1,
    // gridLineColor: "#545454",
    // gridLineWidth: 1,
    // gridLineDashStyle: "Dash",
    // tickPositioner() {
    //   // Get the month boundaries from our data
    //   const monthBoundaries = getJalaliMonthBoundaries(data);
    //   return showMonthlyPeriod ? monthBoundaries : undefined;
    // },
    // tickPositioner: props?.options?.xAxis?.tickPositioner || (() => undefined),
    // labels: {
    //   formatter() {
    //     const d = new Date((this as any)?.value);
    //     const format = showMonthlyPeriod ? "MMMM" : "MM/DD";
    //     return dateConverter(d.toString()).locale("fa").format(format);
    //   },
    //   y: 34,
    //   style: {
    //     color: "#F4F4F4",
    //     fontSize: "16px",
    //     fontWeight: "700"
    //   }
    // }
    // labels: {
    //   /* @ts-ignore */
    //   formatter() {
    //     // @ts-ignore
    //     return this.value && persianMonths?.[this?.value?.toString().slice(-2)];
    //   },
    //   style: {
    //     color: "#F4F4F4",
    //     fontSize: "16px",
    //     fontWeight: "400",
    //     fontFamily: "var(--font-yekan)"
    //   }
    // }
    // },
    yAxis: {
      title: false,
      gridLineColor: "#545454",
      gridLineWidth: 1,
      gridLineDashStyle: "dash",
      min: -dataMax,
      max: dataMax,

      // Explicitly set tick positions
      // tickPositions: [-100, -75, -50, -25, 0, 25, 50, 75, 100],
      // tickPositioner() {
      //   // const { dataMin, dataMax } = this;

      //   console.log("aa", allYValues, dataMin, dataMax);

      //   const maxAbs = Math.max(Math.abs(dataMin), Math.abs(dataMax));
      //   console.log("maxAbs", maxAbs);
      //   const step = maxAbs / 4;

      //   // Create symmetric ticks around 0
      //   const ticks = [];
      //   for (let i = -4; i <= 4; i++) {
      //     ticks.push(Number((i * step).toFixed(2))); // Round to 2 decimals
      //   }

      //   return ticks;
      // },

      labels: {
        overflow: "justify",
        formatter(): string {
          return numbersUnit((this as any).value);
        },
        style: { color: "#F4F4F4", fontFamily: "yekan-bakh" }
      },

      // Create a horizontal line at zero
      plotLines: [{ value: 0, color: "#545454", width: 1 }]
    },
    tooltip: {
      useHTML: true,
      shadow: false,
      shared: false, // Keep this as false for proper hiding behavior
      backgroundColor: "#28282C",
      borderColor: "#858585",
      borderRadius: 8,
      borderWidth: 1,
      fontFamily: "yekan-bakh",
      padding: 12,
      hideDelay: 0, // Set to 0 for immediate hiding when cursor moves away
      animation: false, // Disable fade-out animation for instant hiding
      formatter() {
        // With shared: false, we need to access the x value rather than point index
        // to correctly identify which data point we're hovering over
        const xValue = (this as any).x;

        // Find the corresponding point data by matching x value (date) instead of index
        const pointData = data?.find(item => {
          // You may need to adjust this comparison based on how your dates are stored
          const itemDate = dateConverter(item?.checkpointDate ?? "").format("YYYY/MM/DD");
          const tooltipDate = dateConverter(xValue).format("YYYY/MM/DD");
          return itemDate === tooltipDate;
        });

        if (!pointData) return false;

        let s = `<div class="font-[yekan-bakh]"><p class="!text-[#FFFFFF] text-sm text-center mb-4 ">${dateConverter(
          pointData?.checkpointDate ?? ""
        ).format("YYYY/MM/DD")}</p>`;

        if ((pointData?.depositTransactions?.length ?? 0) > 0) {
          s += `<div class="flex items-center justify-between mb-3 !rtl w-[323px]" >
          <span class="text-[#FFFFFF] text-sm">${numbersUnit(pointData?.totalDebitAmount ?? 0)} </span>
          <span class="text-[#FFFFFF] text-sm">ورودی</span>
          </div>`;
          pointData?.depositTransactions?.forEach(tx => {
            s += `
              <div class="flex items-center justify-between text-white gap-2 mb-3">
                <b class="whitespace-nowrap">${numbersUnit(tx?.amount)}  </b>
                <div class="flex items-center gap-1">
    
                  <div class="flex items-center text-xs text-[#BDBDBD] flex-row-reverse ">
                  <span class=""> ${
                    BondOriginTypes?.[tx.reasonCategory as keyof typeof BondOriginTypes]?.title ?? ""
                  }</span>
                  <span class="mr-0.5">${tx.symbol ? `${tx.symbol}` : ""}<span>
                  <span class="mr-0.5">${tx.contractNumber ? `قراردارد ${tx.contractNumber}` : ""}<span>
                  <span class="mr-0.5">${
                    (BondOriginTypes?.[tx.reasonCategory as keyof typeof BondOriginTypes] as any)?.suffix ?? ""
                  }<span>
    
                </div>
    
                 <i style="background:${BondOriginTypes[tx.reasonCategory as keyof typeof BondOriginTypes]
                   ?.color}; width:12px; height:12px; display:inline-block; border-radius:2px;"></i>
    
                </div>
    
    
              </div>
            `;
          });
        }

        if ((pointData?.withdrawalTransactions?.length ?? 0) > 0) {
          const extraClass = pointData?.depositTransactions?.length
            ? "border-t border-t-[#676767] pt-3 mt-4 "
            : "border-t border-t-transparent";

          s += `<div class="flex items-center justify-between mb-3 w-[323px] text-left ${extraClass}">
          <span class="text-[#FFFFFF] text-sm">${
            pointData?.totalDepositAmount ? numbersUnit(Math.abs(pointData?.totalDepositAmount)) : 0
          } </span>
          <span class="text-[#FFFFFF] text-sm">خروجی</span>
          </div>`;
          pointData?.withdrawalTransactions?.forEach(tx => {
            s += `
              <div class="flex items-center justify-between text-white gap-2 mb-3">
          <b class="whitespace-nowrap">${tx?.amount ? numbersUnit(Math.abs(tx?.amount)) : 0} </b>
                <div class="flex items-center gap-1">
    
                  <div class="flex items-center text-xs text-[#BDBDBD] text-right flex-row-reverse">
                  <span class="mr-0.5"> ${BondOriginTypes?.[tx.reasonCategory as keyof typeof BondOriginTypes]
                    ?.title}</span>
                  <span class="mr-0.5">${tx.symbol ? `${tx.symbol}` : ""}<span>
                  <span class="mr-0.5">${tx.contractNumber ? `قراردارد ${tx.contractNumber}` : ""}<span>
                  <span class="mr-0.5">${
                    (BondOriginTypes?.[tx.reasonCategory as keyof typeof BondOriginTypes] as any)?.suffix ?? ""
                  }<span>
    
                </div>
    
                 <i style="background:${BondOriginTypes[tx.reasonCategory as keyof typeof BondOriginTypes]
                   ?.color}; width:12px; height:12px; display:inline-block; border-radius:2px;"></i>
    
                </div>
    
    
              </div>
            `;
          });
        }

        s += "</div></div>";
        return !!pointData?.withdrawalTransactions?.length || !!pointData?.depositTransactions?.length ? s : false;
      }
    },

    series: allSeries,
    plotOptions: {
      series: { states: { inactive: { enabled: false } } },
      area: {
        threshold: 10
      },
      column: {
        stacking: "normal",
        pointWidth: 22,
        pointPadding: 0.3,
        borderWidth: 0,
        minPointLength: 3,
        groupPadding: 0.2
      }
    },
    credits: { enabled: false },
    legend: { enabled: false }
  };
};

export const BondOriginTypes = {
  0: { name: "None", title: "نامشخص", color: "" },
  100: { name: "ReceiveCouponInterest", title: "کوپن", color: "#81F4C3" },
  200: { name: "ReceiveContractInterest", title: "سود", color: "#43E5A0" },
  300: { name: "SellFacilitiesContract", title: "فروش", color: "#11563A" },
  400: { name: "SellFacilitiesBoard", title: "فروش", color: "#0FA968", suffix: "(تسهیلات)" },
  500: { name: "SellAssetBoard", title: "فروش", color: "#108554", suffix: "(دارایی)" },
  600: { name: "SellAssetContract", title: "فروش", color: "#03301F", suffix: "(دارایی)" },
  700: { name: "SellMaturedBoardAsset", title: "سررسید", color: "#1ACD81", suffix: "(تسهیلات)" },
  800: { name: "PayCouponInterest", title: "سود", color: "#841818" },
  900: { name: "PayContractInterest", title: "سود", color: "#480707" },
  1000: { name: "BuyContractFacility", title: "خرید ", color: "#F83B3B", suffix: "(تسهیلات)" },
  1100: { name: "BuyAssetContract", title: "خرید", color: "#C11414", suffix: "(دارایی)" },
  1200: { name: "BuyAssetBoard", title: "خرید", color: "#FFA0A0" },
  1300: { name: "BuyMaturedFacilities", title: "سررسید", color: "#FF5E5E" }
};

export const TransactionReasonCategory = {
  0: { name: "None", title: "نامشخص" },
  100: { name: "ReceivedInterest", title: "سود (دریافتی)" },
  200: { name: "Sale", title: "فروش" },
  300: { name: "PayedInterest", title: "سود (پرداخت)" },
  400: { name: "Purchase", title: "خرید" }
};

// export const  DepositTransactionsColor = {

//   0:,
//   100: ,
//   200: ,
//   300: ,
//   400:
// }

export const withdrawalTransactionsColor = {};

export const tabs = [
  { id: FUND_TABS.MARKET_RISK, title: "ریسک بازار" },
  { id: FUND_TABS.INTEREST_RISK, title: "ریسک نرخ بهره" },
  { id: FUND_TABS.ADAPTIVE_RISK, title: "ریسک تطبیق" },
  { id: FUND_TABS.OPERATIONAL_RISK, title: "ریسک عملیاتی" },
  { id: FUND_TABS.FUND_ASSETS_LIQUID, title: "ریسک نقدشوندگی" },
  { id: FUND_TABS.LIQUIDITY_RISK, title: "ریسک نقدینگی" },
  { id: FUND_TABS.FOCUS_RISK, title: "ریسک تمرکز" }
];

export const operationRisk_subTabs = [
  { id: FUND_SUB_TABS.EVALUATION, title: "ارزیابی ریسک ها" },
  { id: FUND_SUB_TABS.RISK_MANAGEMENT, title: "استراتژی مدیریت ریسک" }
];

export const focusRisk_subTabs = [
  { id: FOCUS_SUB_TABS.SHAREHOLDERS, title: "سهامدارها" },
  { id: FOCUS_SUB_TABS.ASSETS, title: "دارایی‌ها" }
];

export const getColorByValue = (n?: number) => {
  let color = "text-[#A5F539]";
  if (n === undefined) {
    color = "text-[#676767]";
  } else if (n <= 40) {
    color = "text-[#FF5E5E]";
  } else if (n > 40 && n <= 70) {
    color = "text-[#F1C21B]";
  }

  return color;
};

export const getBorderColorByValue = (n: number) => {
  let color = "border-[#A5F539]";
  if (n <= 40) {
    color = "border-[#FF5E5E]";
  } else if (n > 40 && n <= 70) {
    color = "border-[#F1C21B]";
  }

  return color;
};

export const efficiencyColors = { future: "#26D5C0", past: "#C7DA41", real: "#F4F4F4" };

export const columnChartColorsLight = ["#108554", "#FAEB8E", "#E1AB11", "#E35050"];
export const columnChartColorsDark = ["#0d6a43", "#c8bc72", "#b4890e", "#b64040"];
