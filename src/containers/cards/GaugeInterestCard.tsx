import { toDecimals, toFixedNumber } from "@/utils/helpers";
import { parseAsBoolean, useQueryStates } from "nuqs";
import { twMerge } from "tailwind-merge";
import CircleGauge from "@/components/atoms/circleGauge";
import InfoCardTooltip from "./InfoCardTooltip";
import { IGaugeInterestCardProps } from "./type";
import { isNullOrEmpty } from "./util";

function GaugeInterestCard(props: IGaugeInterestCardProps) {
  const { id, title, desc, structor = [], market, value, isSelected, onCardClick, showAlert } = props;

  const [queryStates] = useQueryStates({
    isShowMarketValue: parseAsBoolean.withDefault(true)
  });
  const { isShowMarketValue } = queryStates;

  const marketFormated = market || market === 0 ? toFixedNumber(market, 3) : null;
  const valueFormated = value || value === 0 ? toFixedNumber(value || 0, 3) : null;
  const values = !isShowMarketValue && id === "YTM" ? [valueFormated] : [valueFormated, marketFormated];

  const unit = "";
  const tooltipData = [];

  const findY = () => {
    const v = value || 0;

    if (valueFormated === null) return "-";
    if (id === "YTM") return `${toDecimals(v * 100, 2)}٪`;
    return toDecimals(v, 2);
  };

  if (showAlert && id === "YTM") {
    tooltipData.push({ color: "#F83B3B", name: "ترکیب پرتفوی اوراق تغییر کرده", y: null, textColor: "#F83B3B" });
  }

  if (!isNullOrEmpty(value)) {
    tooltipData.push({
      color: "#8871BA",
      name: "پرتفو",
      y: findY()
    });
  } else if (isNullOrEmpty(value)) {
    tooltipData.push({ color: "#8871BA", name: "پرتفو", y: "-" });
  }

  if (isShowMarketValue) {
    if (!isNullOrEmpty(market) && id === "YTM") {
      tooltipData.push({
        color: "white",
        name: "بازار",
        y: marketFormated !== null ? `${toDecimals((market || 0) * 100, 2)}%` : "-"
      });
    } else if (isNullOrEmpty(market) && id === "YTM") {
      tooltipData.push({ color: "white", name: "بازار", y: "-" });
    }
  }

  const onClickHandle = () => onCardClick(id);

  const findValue = () => {
    if (id === "YTM") return values?.map(item => (item !== null ? item * 100 : null));
    if (id === "CONVEXITY") return values[0];
    return values;
  };

  return (
    <div
      onClick={onClickHandle}
      className={twMerge(
        "flex flex-col h-full rounded-lg p-2 pb-[7px] justify-between cursor-pointer bg-[#1F1F22] border border-cardBackground hover:border-borderBorderAndDivider",
        isSelected && "border-2 !border-mainBlue"
      )}
      aria-label={`Gauge Interest Card ${id}`}
    >
      <div className="flex flex-col gap-2">
        <div className="flex justify-between">
          <h5>{title}</h5>
          <InfoCardTooltip id={id} data={tooltipData} unit={unit} showAlert={showAlert} />
        </div>
        <span className="text-xs leading-4">{desc}</span>
      </div>
      <div className="flex justify-center items-center mt-12">
        <CircleGauge
          size="medium"
          data={structor}
          labelFormatter={item => `${item?.min}`}
          value={findValue()}
          lastLabel={
            structor?.length > 0
              ? `${structor?.sort((a, b) => Number(a?.max) - Number(b?.max))?.[structor.length - 1]?.max}`
              : ""
          }
          insideCircleClassName="bg-dark_black"
          arrowColors={id === "CONVEXITY" ? ["#8871BA"] : ["#8871BA", "white", "#B5179E"]}
          isSelected={isSelected}
        />
      </div>
    </div>
  );
}

export default GaugeInterestCard;
