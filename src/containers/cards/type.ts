import { IZone } from "@/components/atoms/circleGauge/types";

/* eslint-disable @typescript-eslint/naming-convention */
export enum FUND_TABS {
  MARKET_RISK = 1,
  INTEREST_RISK = 2,
  ADAPTIVE_RISK = 3,
  OPERATIONAL_RISK = 4,
  FUND_ASSETS_LIQUID = 5,
  LIQUIDITY_RISK = 6,
  FOCUS_RISK = 7
}

export enum FUND_SUB_TABS {
  EVALUATION = 1,
  RISK_MANAGEMENT = 2
}

export enum FOCUS_SUB_TABS {
  SHAREHOLDERS = 1,
  ASSETS = 2
}

export enum EFFICIENCY_GAUGE {
  EXPECTED = "isShowExpected",
  REAL = "isShowReal"
}

// Operational Risks - Risks by risk manager
export const RISKS_BY_MANAGER = {
  STOCK_MATTERS_RISK: "امور سهام",
  ASSEMBLIES_COMPANIES_AFFAIR_RISK: "امور مجامع و شرکت ها",
  MARKET_MAKING_RISK: "بازار گردانی",
  PLANNING_STRATEGY_RISK: "برنامه ریزی و راهبرد",
  JURIDICAL_RISK: "حقوقی",
  INVESTMENT_RISK: "سرمایه گذاری",
  IT_RAND_RISK: "فناوری اطلاعات  ",
  FINANCIAL_َADMINISTRATIVE_RISK: "مالی و اداری"
};

export interface IFundChartFilter {
  isCheckedRetrospect: boolean;
  className?: string;
  onChange: () => void;
}

export interface IGaugeMarketCardProps {
  id: string;
  title: string;
  desc: string;
  structor: IZone[];
  past?: number;
  future?: number;
  pastJensens?: number;
  futureJensens?: number;
  isSelected: boolean;
  isShowRetrospect: boolean;
  onCardClick: (id: string) => void;
}

export interface IGaugeEfficiencyCardProps {
  id: string;
  title: string;
  desc: string;
  structor: IZone[];
  past?: number;
  future?: number;
  real?: number;
  isSelected: boolean;
  isShowRetrospect: boolean;
  onCardClick: (id: string) => void;
}

export interface IGaugeInterestCardProps {
  id: string;
  type?: string;
  title: string;
  desc: string;
  structor: IZone[];
  value?: number | null;
  market?: number | null;
  isSelected: boolean;
  onCardClick: (id: string) => void;
  showAlert?: boolean;
}

export interface IDeviationMarketCardProps {
  id: string;
  title: string;
  desc: string;
  pastValue?: number;
  futureValue?: number;
  isSelected: boolean;
  onCardClick: (id: string) => void;
  isShowRetrospect: boolean;
}

export type TSummaryMarketRiskOptions = {
  future: number;
  past: number;
  real: number;
  isShowRetrospect?: boolean;
  isShowExpected?: boolean;
  isShowReal?: boolean;
};

export type TFundRiskRow = {
  fundRiskIndex: number;
  value: number;
  goodValue: number;
  badValue: number;
  bestValue: number;
  worseValue: number;
};

export type TFundRow = {
  id: string;
  title: string;
  desc: string;
  future: TFundRiskRow | null;
  past: TFundRiskRow | null;
};

type T = { [key: string]: { title: string; desc?: string; enum1: number[] } };

export enum FUND_TYPES {
  EFFICIENCY = "efficiency",
  BETA = "beta",
  DEVIATION = "deviation",
  TRAINER = "trainer",
  SHARP = "sharp",
  ALPHA = "alpha",
  ALPHA_ADJUSTED = "alpha_adjusted",
  RISK_VALUE = "riskValue",
  HISTOGRAM = "histogram"
}

// Focus
export enum FUND_FOCUS {
  OWNERS = "OWNERS",
  HHI = "HHI",
  CR = "CR"
}

export enum FUND_FOCUS_Assets {
  HHI = "HHI",
  CR = "CR"
}

type FT = { [key: string]: string };

export enum FUND_INTEREST {
  YTM = "YTM",
  YTC = "YTC",
  CONVEXITY = "CONVEXITY",
  DURATION = "DURATION",
  DURATION_MODIFIED = "DURATION_MODIFIED",
  WEIGHTED_AVERAGE = "WEIGHTED_AVERAGE",
  DURATION_COUPON = "DURATION_COUPON",
  DURATION_ZERO_COUPON = "DURATION_ZERO_COUPON"
}

export const FUND_LABELS: FT = {
  // Interests
  [FUND_INTEREST.YTM]: "YTM",
  [FUND_INTEREST.YTC]: "YTC پرتفو",
  [FUND_INTEREST.CONVEXITY]: "تحدب پرتفو",
  [FUND_INTEREST.DURATION]: "دیرش",
  [FUND_INTEREST.DURATION_MODIFIED]: "دیرش پرتفو",
  [FUND_INTEREST.WEIGHTED_AVERAGE]: "نرخ بهره بدون ریسک",
  [FUND_INTEREST.DURATION_COUPON]: "دیرش اوراق کوپن‌ دار",
  [FUND_INTEREST.DURATION_ZERO_COUPON]: "دیرش اوراق بدون کوپن",

  // Markets
  [FUND_TYPES.EFFICIENCY]: "بازدهی",
  [FUND_TYPES.BETA]: "ضریب بتا",
  [FUND_TYPES.DEVIATION]: "انحراف معیار",
  [FUND_TYPES.TRAINER]: "ترینر",
  [FUND_TYPES.SHARP]: "شارپ",
  [FUND_TYPES.ALPHA_ADJUSTED]: "آلفای تعدیل‌شده",
  [FUND_TYPES.RISK_VALUE]: "ارزش در معرض خطر (VAR)",

  // Focus
  [FUND_FOCUS.OWNERS]: "درصد مالکیت",
  [FUND_FOCUS.CR]: "CR",
  [FUND_FOCUS.HHI]: "HHI"
};

export const FUND: T = {
  [FUND_TYPES.EFFICIENCY]: { title: FUND_LABELS[FUND_TYPES.EFFICIENCY], enum1: [100, 200] },
  [FUND_TYPES.BETA]: { title: FUND_LABELS[FUND_TYPES.BETA], enum1: [101, 201] },
  [FUND_TYPES.DEVIATION]: { title: FUND_LABELS[FUND_TYPES.DEVIATION], enum1: [102, 202] },

  [FUND_TYPES.TRAINER]: { title: FUND_LABELS[FUND_TYPES.TRAINER], enum1: [103, 203] },
  [FUND_TYPES.SHARP]: { title: FUND_LABELS[FUND_TYPES.SHARP], enum1: [104, 204] },
  [FUND_TYPES.ALPHA]: { title: FUND_LABELS[FUND_TYPES.ALPHA], enum1: [105, 205] },
  [FUND_TYPES.RISK_VALUE]: { title: FUND_LABELS[FUND_TYPES.RISK_VALUE], desc: "ضریب اطمینان 99%", enum1: [106, 206] }
};

export interface IInterestRiskMarketCardData {
  couponBondsConvexity: number;
  couponBondsDuration: number;
  couponBondsModifiedDuration: number;
  couponBondsYtm: number;
  riskFreeRateIndex: number;
  totalConvexity: number;
  totalDuration: number;
  totalModifiedDuration: number;
  totalYtm: number;
  zeroCouponBondsConvexity: number;
  zeroCouponBondsDuration: number;
  zeroCouponBondsModifiedDuration: number;
  zeroCouponBondsYtm: number;
}

export interface IInterestRiskMarketCardResponse {
  data: IInterestRiskMarketCardData;
  isSuccess?: boolean;
  errorCode?: string | null;
  errorMessage?: string | null;
}
