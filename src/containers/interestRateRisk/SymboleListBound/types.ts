export interface ISymbolListBoundData {
  isin: string;
  amount: number;
  bondType: number;
  bondTypeName: string;
  symbol: string;
  maturityDate: string;
  price: number;
  ytm: number | null;
  ytmInPercents: number | null;
  preferredYtm: number;
  preferredYtmInPercents: number;
  duration: number | null;
  modifiedDuration: number | null;
  convexity: number | null;
  portfolioWeightInPercents: number | null;
}

export interface ISymbolListBoundResponse {
  data: ISymbolListBoundData[];
  isSuccess?: boolean;
  errorCode?: string | null;
  errorMessage?: string | null;
}
