/* eslint-disable react/no-unescaped-entities */

"use client";

import { useMemo } from "react";

import { Spinner } from "@/components/atoms/spinner";
import { Table } from "@/components/organisms/Table";
import NoData from "@/components/organisms/Table/NoData";
import { CustomLoadingOverlay, getRowId } from "@/components/organisms/Table/utils";
import { useSocketInitialData } from "@/hooks/useSocketInitialData";
import socketUrls from "@/constants/socketUrls";
import useDelayedStack from "@reactutils/use-delayed-stack";
import useSignalR from "@/hooks/useSignalR/useSignalR";
import { columnDefs, gridOptions } from "./utils";
import { ISymbolListBoundData, ISymbolListBoundResponse } from "./types";
import Styles from "./SymbolList.module.scss";

interface ISymbolListBoundProps {
  gridRef: any;
}

export default function SymbolListBound({ gridRef }: ISymbolListBoundProps) {
  const {
    data: fundAssets,
    isLoading: isFundAssetsLoading,
    error: isFundAssetsError,
    setData
  } = useSocketInitialData<ISymbolListBoundResponse>({
    url: socketUrls.interestRateRisk,

    streamName: socketUrls.interestRateRiskStreamName,
    isInvoke: true
  });

  function updateReactQueryTradeValueData(socketData: ISymbolListBoundData[]) {
    const fundAssetsData = fundAssets?.data?.length ? fundAssets?.data : [];

    const updatedBaseArray = fundAssetsData.map(baseItem => {
      const updatedItem = socketData.find(item => item.isin === baseItem.isin);

      return updatedItem?.isin ? updatedItem : baseItem;
    });

    setData({ data: updatedBaseArray } as ISymbolListBoundResponse);
  }

  const [pushToStackMarketValue] = useDelayedStack((data: ISymbolListBoundData[]) => {
    updateReactQueryTradeValueData(data?.[0] as any);
  }, 300);

  useSignalR(socketUrls.interestRateRisk, socketUrls.interestRateRiskSocketStreamName, {
    next: (item: ISymbolListBoundData) => {
      pushToStackMarketValue(item);
    },
    error: () => {}
  });

  // eslint-disable-next-line no-nested-ternary
  const data = isFundAssetsLoading ? null : fundAssets?.data ? fundAssets?.data : [];

  const columnDefsData = useMemo(() => columnDefs(), []);

  if (isFundAssetsLoading) {
    return (
      <div className="flex items-center justify-center p-10">
        <Spinner />
      </div>
    );
  }

  if (isFundAssetsError) {
    <div className="flex items-center justify-center p-10">
      <span className="text-13 text-white">خطایی رخ داده است!</span>
    </div>;
  }

  if (isFundAssetsLoading) {
    return (
      <div className="flex items-center justify-center p-10">
        <Spinner />
      </div>
    );
  }

  if (isFundAssetsError) {
    <div className="flex items-center justify-center p-10">
      <span className="text-13 text-white">خطایی رخ داده است!</span>
    </div>;
  }

  return (
    // eslint-disable-next-line react/jsx-no-useless-fragment
    <>
      {isFundAssetsLoading || (!isFundAssetsLoading && data?.length) ? (
        <Table
          ref={gridRef}
          data={data}
          columnDefs={columnDefsData}
          gridOptions={gridOptions}
          searchKey="symbol"
          loadingOverlayComponent={CustomLoadingOverlay}
          noRowsOverlayComponent={CustomLoadingOverlay}
          className={Styles.tableContainer}
          getRowId={getRowId as any}
        />
      ) : (
        !isFundAssetsLoading &&
        !data?.length && (
          <NoData columnDefs={columnDefsData} errorTitle="ارتباط با سرور برقرار نیست" noRowsTitle="موردی یافت نشد" />
        )
      )}
    </>
  );
}
