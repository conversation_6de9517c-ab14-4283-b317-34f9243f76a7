/* eslint-disable no-nested-ternary */
// import * as XLSX from "xlsx";
import Checked from "@/assets/icons/boundTypeChecked.svg";
import UnChecked from "@/assets/icons/boundTypeUnChecked.svg";
import { toFixed } from "@/containers/tradeHistory/util";
import { dateConverter } from "@/utils/DateHelper";
import { inBillion } from "@/utils/helpers";
import { GridOptions } from "ag-grid-community";
import { ISymbolListBoundData } from "./types";

// const exportFundAssetsToExcel = (response: ISymbolListBoundData[]) => {
//   /* ------- Prepare header row ------- */
//   const headerRow = [
//     { headerName: "نام نماد", field: "symbol" },
//     { headerName: "ارزش روز نماد", field: "currentInstrumentValue" },
//     { headerName: "YTM", field: "ytmPercent" },
//     { headerName: "YTC", field: "ytcPercent" },
//     { headerName: "دیرش", field: "duration" },
//     { headerName: "دیرش تعدیل شده", field: "modifiedDuration" },
//     { headerName: "تحدب", field: "convexity" },
//     { headerName: "وزن", field: "weightPercent" },
//     { headerName: " تاریخ سررسید", field: "maturity" }
//   ];

//   /* -------------- Prepare data rows -------------- */
//   const rows = response.map(asset => ({
//     symbol: asset.symbol,
//     currentInstrumentValue: asset.currentInstrumentValue,
//     ytmPercent: asset.ytmPercent,
//     ytcPercent: asset.ytcPercent,
//     duration: asset.duration,
//     modifiedDuration: asset.modifiedDuration,
//     convexity: asset.convexity,
//     weightPercent: asset.weightPercent,
//     maturity: asset.maturity ? gregorianDateToJalali(asset.maturity)?.format("YYYY/MM/DD") : ""
//   }));

//   /* -------------- Create a worksheet with custom headers -------------- */
//   const worksheet = XLSX.utils.json_to_sheet(rows);

//   // Set the correct header names
//   headerRow.forEach((header, index) => {
//     worksheet[XLSX.utils.encode_cell({ r: 0, c: index })] = { v: header.headerName };
//   });

//   /* ------------- Create a new workbook and append the worksheet ------------- */
//   const workbook = XLSX.utils.book_new();
//   XLSX.utils.book_append_sheet(workbook, worksheet, "Fund Assets");

//   /* -------------------------- Export the Excel file ------------------------- */
//   XLSX.writeFile(workbook, "fund_assets.xlsx");
// };

// export const handleExportBoundAssets = (data?: ISymbolListBoundData[]) => {
//   if (!data?.length) return;

//   exportFundAssetsToExcel(data);
// };

export function columnDefs() {
  return [
    {
      headerName: "نام اوراق",
      unSortIcon: true,
      sortable: true,
      field: "symbol",
      filter: "agTextColumnFilter",
      cellRenderer: ({ data }: { data: ISymbolListBoundData }) => <div>{data?.symbol || "-"}</div>
    },
    {
      headerName: "ارزش روز",
      unSortIcon: true,
      sortable: true,
      field: "price",
      cellRenderer: ({ data }: { data: ISymbolListBoundData }) => (
        <div className="ltr pr-3">{data?.price ? `${inBillion(data?.price)}` : "-"}</div>
      )
    },

    {
      headerName: "YTM محاسباتی",
      unSortIcon: true,
      sortable: true,
      field: "ytmPercent",
      cellRenderer: ({ data }: { data: ISymbolListBoundData }) => (
        <div className="">{data?.ytmInPercents ? `${toFixed(data?.ytmInPercents)}%` : "-"}</div>
      )
    },
    {
      headerName: "YTM ترجیحی",
      unSortIcon: true,
      sortable: true,
      field: "ytmPercent",
      cellRenderer: ({ data }: { data: ISymbolListBoundData }) => (
        <div className="">{data?.preferredYtmInPercents ? `${toFixed(data?.preferredYtmInPercents)}%` : "-"}</div>
      )
    },
    // {
    //   headerName: "YTC",
    //   unSortIcon: true,
    //   sortable: true,
    //   sortingOrder: ["desc", "asc"],
    //   field: "ytcPercent",
    //   cellRenderer: ({ data }: { data: ISymbolListBoundData }) => (
    //     <div>{data?.ytcPercent ? toFixed(data?.ytcPercent) : "-"}</div>
    //   )
    // },
    {
      headerName: "دیرش",
      unSortIcon: true,
      sortable: true,
      field: "duration",
      cellRenderer: ({ data }: { data: ISymbolListBoundData }) => (
        <div className="">{data?.duration ? toFixed(data?.duration) : "-"}</div>
      )
    },
    {
      headerName: "دیرش تعدیل شده",
      unSortIcon: true,
      sortable: true,
      minWidth: 180,
      field: "modifiedDuration",
      cellRenderer: ({ data }: { data: ISymbolListBoundData }) => (
        <div className="pr-3">{data?.modifiedDuration ? toFixed(data?.modifiedDuration) : "-"}</div>
      )
    },
    {
      headerName: "تحدب",
      unSortIcon: true,
      sortable: true,
      field: "convexity",
      cellRenderer: ({ data }: { data: ISymbolListBoundData }) => (
        <div className="pr-3">{data?.convexity ? toFixed(data?.convexity) : "-"}</div>
      )
    },
    {
      headerName: "وزن اوراق در پرتفوی",
      unSortIcon: true,
      sortable: true,
      minWidth: 200,

      field: "portfolioWeightInPercents",
      cellRenderer: ({ data }: { data: ISymbolListBoundData }) => (
        <div className="pr-3">
          {data?.portfolioWeightInPercents ? `${toFixed(data?.portfolioWeightInPercents)}%` : "-"}
        </div>
      )
    },
    {
      headerName: "دارای کوپن",
      unSortIcon: true,
      sortable: true,
      field: "bondType",
      filter: "agTextColumnFilter",
      cellRenderer: ({ data }: { data: ISymbolListBoundData }) => (
        <div className="pr-8">
          {data?.bondType === 100 ? <Checked /> : data?.bondType === 200 ? <UnChecked /> : "-"}
        </div>
      )
    },
    {
      headerName: "تاریخ سررسید",
      unSortIcon: true,
      sortable: true,
      field: "maturityDate",
      cellRenderer: ({ data }: { data: ISymbolListBoundData }) => (
        <div className="pr-3">{data?.maturityDate ? dateConverter(data?.maturityDate)?.format("YYYY/MM/DD") : "-"}</div>
      )
    }
  ];
}

export const gridOptions: GridOptions = {
  enableRtl: true,
  onGridSizeChanged: () => {
    gridOptions.api?.sizeColumnsToFit();
  }
};

export function CustomNoRows() {
  return <div className="flex items-center justify-center text-white h-full">موردی یافت نشد</div>;
}

export function CustomNetworkError() {
  return <div className="flex items-center justify-center text-white h-full">ارتباط با سرور برقرار نیست</div>;
}
