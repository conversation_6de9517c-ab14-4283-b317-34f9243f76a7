/* eslint-disable @typescript-eslint/naming-convention */
/* eslint-disable react/no-unescaped-entities */

"use client";

import Search from "@/assets/icons/detail-search.svg";
import useUserModalStore from "@/store/userModalStore/UserModalStore";
import { usePathname, useRouter } from "next/navigation";
import { useRef, useState } from "react";
import Close from "@/assets/icons/Cancel.svg";
import Input from "@/components/atoms/input";
import SymbolListBound from "./SymboleListBound";

export default function SymboleListModal() {
  const pathname = usePathname();
  const gridRef = useRef<any>(null);
  const { closeUserModal } = useUserModalStore();

  const router = useRouter();
  const [filterText, setFilterText] = useState("");

  const onSearch = (value: string) => {
    gridRef.current?.onSearch(value);
    setFilterText(value);
  };

  return (
    <div className="h-[97vh] ">
      <div className="flex items-center justify-between">
        <div className="flex items-center justify-between w-full">
          <span className="text-base font-bold text-[#EFEFEF]">لیست اوراق</span>
          {/* <Excel
            className="cursor-pointer"
            data-test="03e518ff-102f-4be7-9fd0-bb6b4521c2c6"
            onClick={() => {
              if (tabState === 100) {
                handleExport(fundAssets?.data);
              } else {
                handleExportBoundAssets(fundBoundAssets?.data);
              }
            }}
          /> */}
          <Close
            width={14}
            height={14}
            className="cursor-pointer w-6 h-6"
            onClick={() => {
              router.replace(pathname);
              closeUserModal();
            }}
          />
        </div>
      </div>

      <div className="flex items-start justify-between pt-3 pr-3.5 pl-3 bg-[#1F1F22] mt-3 rounded-t-[4px]">
        <Input
          value={filterText}
          onChange={onSearch}
          preserveErrorMessage
          inputSize="small"
          placeHolder="جستجو (نام نماد)"
          startAdornment={<Search />}
          className="w-[200px]"
          data-test="531cbdaa-8c85-4833-9354-f242a1279966"
        />
      </div>

      <div className="h-[calc(100%_-_160px)] -mt-3">
        <SymbolListBound gridRef={gridRef} />
      </div>
    </div>
  );
}
