/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable @typescript-eslint/naming-convention */
import { useEffect } from "react";
import { parseAsBoolean, useQueryStates } from "nuqs";
import useUserModalStore from "@/store/userModalStore/UserModalStore";
import SymbolButton from "@/assets/icons/SymbolButton.svg";
import RangeIcon from "@/assets/icons/info.svg";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useSocketInitialData } from "@/hooks/useSocketInitialData";
import socketUrls from "@/constants/socketUrls";
import useDelayedStack from "@reactutils/use-delayed-stack";
import useSignalR from "@/hooks/useSignalR/useSignalR";
import InterestRateSensitivityCard from "@/containers/cards/InterestRateSensitivityCard";
import {
  ConvexityData,
  DurationData,
  FUND_INTEREST,
  FUND_LABELS,
  PortfolioData,
  PortfolioDataResponse,
  YtmData
} from "./types";
import SymbolListModal from "./SymboleListBound/SymboleListModal";
import InterestRiskFilter from "./InterestRiskFilter";
import InterestRiskMarketCard from "../cards/InterestRiskMarketCard";
import GaugeInterestDurationCard from "../cards/GaugeInterestDurationCard";
import GaugeInterestCard from "../cards/GaugeInterestCard";
import DebtMarketRadios from "../charts/interestRisk/DebtMarkeRadios";
import RenderInterestChart from "../charts/chartLayouts/RenderInterestChart";

export interface IInterestsRisks {
  type: string;
  isShowRetrospect: boolean;
  onCardClick: (id: string) => void;
}

function InterestsRisk(props: IInterestsRisks) {
  const { type, onCardClick } = props;
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const router = useRouter();

  const modalType = searchParams.get("modalType");

  const { openUserModal, closeUserModal } = useUserModalStore();

  const { data: fundQuery, setData } = useSocketInitialData<PortfolioDataResponse>({
    url: socketUrls.interestRiskPortfolioCardInvoke,
    streamName: socketUrls.interestRiskPortfolioCardStreamNameInvoke,
    isInvoke: true
  });

  const { data: fundData } = fundQuery || {};

  function updateReactQueryTradeValueData(socketData: PortfolioData[]) {
    const updatedBase = socketData?.at(-1);

    setTimeout(() => {
      setData({ data: updatedBase } as PortfolioDataResponse);
    }, 3000);
  }

  const [pushToStackMarketValue] = useDelayedStack((data: PortfolioData[]) => {
    updateReactQueryTradeValueData(data);
  }, 300);

  useSignalR(socketUrls.interestRiskPortfolioCard, socketUrls.interestRiskPortfolioCardStreamName, {
    next: (item: any) => {
      pushToStackMarketValue(item);
    },
    error: () => {}
  });

  const [queryStates, setQueryStates] = useQueryStates({
    isShowMarketValue: parseAsBoolean.withDefault(true)
  });
  const { isShowMarketValue } = queryStates;

  const onShowBazar = () => setQueryStates({ isShowMarketValue: !isShowMarketValue });

  const isShowDebtMarket = [
    FUND_INTEREST.WEIGHTED_AVERAGE.toString(),
    FUND_INTEREST.DURATION_COUPON.toString(),
    FUND_INTEREST.DURATION_ZERO_COUPON.toString()
  ].includes(type);

  const onLineCardClick = (id: string) => onCardClick(id);
  const onMarketCardClick = () => onCardClick(FUND_INTEREST.WEIGHTED_AVERAGE);
  const onBackClick = () => onCardClick(FUND_INTEREST.YTM);

  // const onClickMaximize = () => {
  //   openUserModal(<ChartModal fundId={fundId} onCloseModal={closeUserModal} hasCheckbox={false} />, {
  //     center: true,
  //     width: "100%",
  //     height: "100%",
  //     className: "p-3 bg-black",
  //     rootClassName: Styles.modalLineChart
  //   });
  // };

  const getInterestCard = (id: string, i?: YtmData | ConvexityData | DurationData) => {
    let structor =
      i?.indexRanges?.map((j, k) => ({
        ...j,
        id: k.toString()
      })) || [];

    if (id === FUND_INTEREST.YTM) {
      structor =
        i?.indexRanges?.map((j, k) => ({
          ...j,
          id: k.toString(),
          min: j.min ? j.min * 100 : j.level,
          max: j.max ? j.max * 100 : j.level
        })) || [];
    }

    return {
      id,
      type,
      title: FUND_LABELS[id],
      structor,
      market: i?.marketValue,
      value: i?.portfolioValue,
      isSelected: type === id,
      onCardClick: (cid: string) => onLineCardClick(cid)
    };
  };

  useEffect(() => {
    if (modalType === "symbolList") {
      openUserModal(<SymbolListModal />, {
        width: "98%",
        height: "calc(100% - 32px)",
        onClose: () => {
          router.replace(pathname);
          closeUserModal();
        }
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [modalType, pathname]);

  return (
    <div className="flex flex-col grow bg-[#343438] rounded-lg p-2 h-full">
      {isShowDebtMarket ? (
        <DebtMarketRadios type={type} onCardClick={onCardClick} onBackClick={onBackClick} />
      ) : (
        <>
          <div className="flex items-center justify-between">
            <InterestRiskFilter onChange={onShowBazar} isShowMarketValue={isShowMarketValue} />

            <div
              className="w-fit flex items-center gap-3 rounded border border-[#0C82F9] p-2.5 h-8 cursor-pointer ml-0.5"
              onClick={() => {
                router.push(`${pathname}?modalType=symbolList`);
              }}
            >
              <SymbolButton />
              <span className="text-[#EFEFEF] text-sm">لیست اوراق</span>
            </div>
          </div>
          <div className="grid grid-cols-[1fr_1fr_305px_352px] 2xl:grid-cols-[1fr_1.5fr_1.5fr_1.5fr_1.5fr] gap-2 mt-2 mb-1.5 ">
            <InterestRateSensitivityCard />
            <GaugeInterestCard {...getInterestCard(FUND_INTEREST.YTM, fundData?.ytm)} desc="شاخص عملکردی" />

            <GaugeInterestCard {...getInterestCard(FUND_INTEREST.CONVEXITY, fundData?.convexity)} desc="شاخص ریسک" />

            <GaugeInterestDurationCard
              {...getInterestCard(
                type === FUND_INTEREST.DURATION_MODIFIED ? FUND_INTEREST.DURATION_MODIFIED : FUND_INTEREST.DURATION,
                type === FUND_INTEREST.DURATION_MODIFIED ? fundData?.modifiedDuration : fundData?.duration
              )}
              desc="شاخص ریسک"
            />

            <InterestRiskMarketCard onCardClick={onMarketCardClick} />
          </div>
        </>
      )}

      <div className="flex grow relative">
        <RenderInterestChart type={type} />
        {/* <MaximizeIcon
          onClick={onClickMaximize}
          className="absolute w-4 h-4 left-3 bottom-4 cursor-pointer"
          aria-label="maximize chart"
        /> */}
      </div>

      <div className="flex items-center gap-2 mt-2">
        <RangeIcon width="16" height="16" />

        <span className="text-xs text-[#BDBDBD]">بازه زمانی: سه ساله</span>
      </div>
    </div>
  );
}

export default InterestsRisk;
