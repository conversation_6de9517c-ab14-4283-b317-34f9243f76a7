const socketUrls = {
  priceChangeYtmUrl: "/hub/v1/bond",
  priceChangeYtmStreamName: "PriceChangeYtmStream",
  totalMarketValueUrl: "/hub/v1/DashboardTotalValue",
  durationValueValueUrl: "/hub/v1/DashboardAverageDuration",
  TopSixYtmUrl: "/hub/v1/DashboardTopYtm",
  BondDetailLastState: "BondDetailLastState",
  TotalValueLastState: "TotalValueLastState",
  TodayAverageYtmLastState: "TodayAverageYtmLastState",
  TopSixYtmStreamName: "TopSixYtmStream",
  TopSixYtmLastState: "TopSixYtmLastState",
  totalMarketValueStreamName: "TotalValueStream",
  totalMarketYtmUrl: "/hub/v1/DashboardAverageYtm",
  totalMarketYtmStreamName: "TodayAverageYtm",
  orderBookUrl: "/hub/v1/BondDetail",
  orderBookStreamName: "OrderBook",
  marketMapUrl: "/hub/v1/marketMap",
  marketMapStreamName: "BondMarketMap",
  interestRateRisk: "/hub/v1/Portfolio",
  interestRateRiskStreamName: "PortfolioBondsLastState",
  interestRateRiskSocketStreamName: "PortfolioBonds",
  durationValueValueInvokeStream: "TodayAverageDurationLastState",

  interestRiskMarketCardInvoke: "/hub/v1/DashboardReturnRateRiskMeasures",
  interestRiskMarketCardStreamNameInvoke: "MarketReturnRateRiskMeasuresLastState",
  interestRiskMarketCard: "/hub/v1/DashboardReturnRateRiskMeasures",
  interestRiskMarketCardStreamName: "MarketReturnRateRiskMeasures",
  interestRiskPortfolioCardInvoke: "/hub/v1/DashboardReturnRateRiskMeasures",
  interestRiskPortfolioCardStreamNameInvoke: "PortfolioReturnRateRiskMeasuresLastState",
  interestRiskPortfolioCard: "/hub/v1/DashboardReturnRateRiskMeasures",
  interestRiskPortfolioCardStreamName: "PortfolioReturnRateRiskMeasures",
  durationValueValueStream: "TodayAverageDuration"
};

export default socketUrls;
