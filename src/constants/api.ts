import { IVarFilterParams } from "@/components/molecules/DateFilter/types";
import { IGetBondChartParams, ITradeHistoryParams } from "@/containers/tradeHistory/types";
import { TBondTableParams } from "@/queries/bondTableAPI/types";
import { ISearchIsinByNameFilterText } from "@/queries/calculatorAPI/types";
import { ITransactionsParams, ITransactionsTableParams } from "@/queries/TransactionsAPI/types";
import { insertParamsToUrl } from "@/utils/helpers";

export const clientBaseUrl = "/api/frontend/v1";

const endpoints = {
  login: `${clientBaseUrl}/auth/login`,
  logout: `${clientBaseUrl}/auth/logout`,
  addTicket: `${clientBaseUrl}/tickets`,
  profile: `${clientBaseUrl}/profile`,
  getTradeValues: (p: ITradeHistoryParams) =>
    `${clientBaseUrl}/bondtradeshistoryreport/gettradevaluesreport?FromDate=${p.fromDate}&ToDate=${p.toDate}`,
  getDurationValues: (p: ITradeHistoryParams) =>
    `${clientBaseUrl}/bondDurationAverage/getlastyeardailyDurationAveragereport?FromDate=${p.fromDate}&ToDate=${p.toDate}`,
  getTradeYtm: (p: ITradeHistoryParams) =>
    `${clientBaseUrl}/bondytmaverage/getlastyeardailyytmaveragereport?FromDate=${p.fromDate}&ToDate=${p.toDate}`,
  bondTable: (params: TBondTableParams) => insertParamsToUrl(`${clientBaseUrl}/Bond`, params),
  bondExcel: "/api/frontend/v1/bond/excel-export",
  bondTablePin: (params: TBondTableParams) => insertParamsToUrl(`${clientBaseUrl}/dashboardbondpin`, params),
  tablePin: (isin?: string) => `${clientBaseUrl}/dashboardbondpin${isin ? `/${isin}` : ""}`,

  watchList: (params: TBondTableParams) => insertParamsToUrl(`${clientBaseUrl}/watchlist`, params),
  postAddSymbolToWatchList: "/api/frontend/v1/watchlist",
  deleteOneSymbol: (id: string) => `/api/frontend/v1/watchlist/${id}`,
  getWatchListSearch: (params: TBondTableParams) =>
    insertParamsToUrl(`${clientBaseUrl}/watchlist/getwatchlistbondsautocomplete`, params),

  getAggregatedInvestmentSuggestions: (params: TBondTableParams) =>
    insertParamsToUrl(`${clientBaseUrl}/investmentsuggestion/getaggregatedinvestmentsuggestions`, params),
  getSingleInvestmentSuggestions: (params: TBondTableParams) =>
    insertParamsToUrl(`${clientBaseUrl}/investmentsuggestion/getsingularinvestmentsuggestions`, params),

  getTopYtmList: "/api/frontend/v1/dashboardtopytm/topsixytmstream",
  bonds: `${clientBaseUrl}/bond`,
  bond: (id?: string) => `${clientBaseUrl}/bond/${id}`,
  getBondChart: (params: IGetBondChartParams) =>
    `${clientBaseUrl}/Bond/trade-history/${params.id}?isCheckOffDays=true&FromDate=${params.fromDate}&ToDate=${params.toDate}`,
  orderBook: (id?: string) => `${clientBaseUrl}/bonddetail/orderbook/${id}`,
  postCalculateYtm: "/api/frontend/v1/calculator/calculateytm",
  postCalculateBuyPrice: "/api/frontend/v1/calculator/calculatebuyprice",
  postCalculateSellPrice: "/api/frontend/v1/calculator/calculatesellprice",

  getRecommendedBonds: (p: ISearchIsinByNameFilterText) =>
    `${clientBaseUrl}/calculator/getrecommendedbonds?FilterText=${p.FilterText}`,
  GetLastYearMonthlyYtmAverage: `${clientBaseUrl}/bondytmaverage/getlastyearmonthlyytmaverage`,
  GetTodayAverageYtm: `${clientBaseUrl}/bondytmaverage/gettodayaverageytm`,
  GetDashboardTotalValueChart: `${clientBaseUrl}/bondtradeshistoryreport/getyearlytradevaluesreport`,
  GetTotalChartValueDuration: `${clientBaseUrl}/bondDurationAverage/getlastyearmonthlyDurationAverage`,
  GetYearlyTradeValues: `${clientBaseUrl}/dashboardtotalvalue/currentdatetotalvaluedividedbybondtype`,
  getMarketMap: `${clientBaseUrl}/marketmap`,
  getTodayAverageYtm: `${clientBaseUrl}/marketmap/gettodayaverageytm`,

  getMarketStatus: "/api/frontend/v1/marketstatus/marketstatus",

  getFundYtmHistoryChart: (params: IVarFilterParams) =>
    insertParamsToUrl(`${clientBaseUrl}/dashboardGraph/GetPortfolioYtmHistoryGraph`, params),
  getFundConvexityHistoryChart: (params: IVarFilterParams) =>
    insertParamsToUrl(`${clientBaseUrl}/dashboardGraph/GetPortfolioConvexityHistoryGraph`, params),
  getFundDurationHistoryChart: (params: IVarFilterParams) =>
    insertParamsToUrl(`${clientBaseUrl}/dashboardGraph/GetPortfolioDurationHistoryGraph`, params),
  getRiskFreeRateHistoryChart: (params: IVarFilterParams) =>
    insertParamsToUrl(`${clientBaseUrl}/dashboardGraph/GetRiskFreeRateHistoryGraph/`, params),
  getMarketCouponBondsDurationHistoryChart: (params: IVarFilterParams) =>
    insertParamsToUrl(`${clientBaseUrl}/dashboardGraph/GetMarketCouponBondsDurationHistoryGraph/`, params),
  getMarketZeroCouponBondsDurationHistoryChart: (params: IVarFilterParams) =>
    insertParamsToUrl(`${clientBaseUrl}/dashboardGraph/GetMarketZeroCouponBondsDurationHistoryGraph`, params),

  // transactions API

  getTransactionsChart: (params: ITransactionsParams) =>
    insertParamsToUrl(`${clientBaseUrl}/dashboardGraph/GetPortfolioCashFlowGraph`, params),
  getTransactionsTable: (params: ITransactionsTableParams) =>
    insertParamsToUrl(`${clientBaseUrl}/portfolio/GetPortfolioCashFlowTransactions`, params),
  getFundInterestSensitivities: (interestChange: number) =>
    `${clientBaseUrl}/Portfolio/GetInterestSensitivityByInterestChange?InterestChange=${interestChange}`,
  getPiePortfolio: `${clientBaseUrl}/Portfolio/getPiePortfolio`,
  getPortfolioWithPurchaseDetail: (params: ITransactionsTableParams) =>
    insertParamsToUrl(`${clientBaseUrl}/Portfolio/getPortfolioWithPurchaseDetail`, params),

  getFundInterestSensitivitiesByChange: () => `${clientBaseUrl}/Portfolio/GetAssumedInterestSensitivities`
};

export default endpoints;
