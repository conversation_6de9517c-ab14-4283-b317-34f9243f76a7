/* eslint-disable import/no-extraneous-dependencies */
/* eslint-disable @typescript-eslint/return-await */

import routes from "@/constants/routes";
import { removeCookieAndRedirectToLogin } from "@/utils/logout";
import { errorToast } from "@/utils/toast";
import axios, { AxiosError } from "axios";

const notAllowedStatusCodes = [401];
const errorStatusCodes = [500];

export function axiosService(conf: any) {
  const axiosInstance = axios.create(conf);

  return axiosInstance;
}

export const baseURL = process.env.NEXT_PUBLIC_DATA_BASE_URL;

export const apiService = axiosService({
  baseURL,
  headers: {
    "Content-Type": "application/json",
    Accept: "application/json"
  },
  withCredentials: true
});

// commented out because we are not using cookies for auth, instead we now use http-only cookies setted by the server
// apiService.interceptors.request.use(request => {
//   const accessToken = getCookie(cookies.access_token);

//   if (request.headers && accessToken) {
//     request.headers.Authorization = `Bearer ${accessToken}`;
//   }

//   return request;
// });

apiService.interceptors.response.use(
  response => response,
  async (error: AxiosError) => {
    try {
      const { config, response } = error;

      // i.e. if the error is a network error, then we will show a notification
      // if (message === "Network Error" && typeof window !== "undefined") {
      //   console.error("Network error");
      // }

      if (
        config &&
        response &&
        notAllowedStatusCodes.includes(response.status) &&
        typeof window !== "undefined" &&
        ![routes.login].includes(window.location.pathname)
      ) {
        removeCookieAndRedirectToLogin();
      }

      if (
        config &&
        typeof window !== "undefined" &&
        ((response && errorStatusCodes.includes(response.status)) || error.code === "ERR_NETWORK")
      ) {
        errorToast({
          title: "ارتباط با سرور برقرار نیست"
        });
      }

      return Promise.reject(error);
    } catch (err) {
      // logout();
      return Promise.reject(err);
    }
  }
);
