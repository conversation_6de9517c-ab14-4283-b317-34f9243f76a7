import { IBreadCrumbItem } from "@/components/molecules/nextBreadCrumb/types";
import Header from "@/components/organisms/Header/Header";
import Sidebar from "@/components/organisms/sidebar/Sidebar";
import "@/styles/globals.css";

function HomeLayout({ children, breadCrumbItems }: { children: React.ReactNode; breadCrumbItems?: IBreadCrumbItem[] }) {
  return (
    <div className="flex justify-between h-full w-full">
      <Sidebar />
      <div className="w-[calc(100%-56px)] flex flex-col h-full bg-bodyBackground">
        <Header breadCrumbItems={breadCrumbItems || []} />
        <div className="w-full h-0 flex-1">{children}</div>
      </div>
    </div>
  );
}

export default HomeLayout;
