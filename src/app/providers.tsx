"use client";

import React from "react";
import { getQueryClient } from "@/services/reactQuery";
import { QueryClientProvider } from "@tanstack/react-query";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import "@/styles/toast.scss";
import "rc-dialog/assets/index.css";

import UserModalProvider from "@/components/molecules/userModalProvider/UserModalProvider";

export default function Providers({ children }: { children: React.ReactNode }) {
  const [queryClient] = React.useState(() => getQueryClient());

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      <ToastContainer position="bottom-right" />
      <UserModalProvider />
    </QueryClientProvider>
  );
}
