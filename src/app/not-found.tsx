import Link from "next/link";
import { Metadata } from "next";
import Button from "@/components/atoms/button";
import Icon from "@/assets/icons/404.svg";
import HomeLayout from "./HomeLayout";

export const metadata: Metadata = {
  title: "اوراق بدهی"
};

function NotFoundPage() {
  return (
    <HomeLayout>
      <div className="flex flex-col items-center justify-center w-full h-full text-base !bg-cardBackground text-white md:pl-14">
        <Icon className="md:-mt-[106px]" />
        <h1 className="mt-6 leading-[51px] text-2xl font-bold ">آدرس اشتباه است!</h1>
        <p className="mt-3 font-light leading-[33px]">آدرسی که به آن مراجعه کردید وجود ندارد.</p>
        <p className="mt-2 leading-6 text-mediumGray">خطای ۴۰۴</p>
        <Link href="/" className="mt-16">
          <Button variant="fill" className="py-1 w-[90px]">
            بازگشت
          </Button>
        </Link>
      </div>
    </HomeLayout>
  );
}

export default NotFoundPage;
