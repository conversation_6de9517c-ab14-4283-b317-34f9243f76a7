"use client";

import HomeLayout from "@/app/HomeLayout";
import { usePathname } from "next/navigation";
import React from "react";

function Template({ children }: { children: React.ReactNode }) {
  const pathName = usePathname();

  const breadCrumbItems = [
    {
      title: "خانه",
      href: "/"
    },
    {
      title: "جزئیات نماد",
      href: pathName
    }
  ];
  return <HomeLayout breadCrumbItems={breadCrumbItems}>{children}</HomeLayout>;
}

export default Template;
