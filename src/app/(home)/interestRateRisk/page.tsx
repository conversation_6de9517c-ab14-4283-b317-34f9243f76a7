"use client";

import InterestsRisk from "@/containers/interestRateRisk/InterestRisks";
import { parseAsBoolean, parseAsString, useQueryStates } from "nuqs";
import { useVarDateFilterStore } from "@/store/varDateFilter";
import { FUND_INTEREST, FUND_TYPES } from "@/containers/interestRateRisk/types";

function InterestRateRiskPage() {
  const [queryStates, setQueryStates] = useQueryStates({
    type: parseAsString.withDefault(FUND_TYPES.EFFICIENCY),
    isShowRetrospect: parseAsBoolean.withDefault(true)
  });

  const { type, isShowRetrospect } = queryStates;

  const { resetFilter } = useVarDateFilterStore();

  const onCardClick = (cid: string) => {
    resetFilter();
    setQueryStates(
      { type: cid },
      {
        history: cid === FUND_TYPES.HISTOGRAM || cid === FUND_INTEREST.WEIGHTED_AVERAGE ? "push" : "replace"
      }
    );
  };

  return (
    <div className="px-4 pb-4 h-full">
      <InterestsRisk type={type} isShowRetrospect={isShowRetrospect} onCardClick={onCardClick} />
    </div>
  );
}

export default InterestRateRiskPage;
