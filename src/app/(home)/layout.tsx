"use client";

import CalculatorForm from "@/containers/home/<USER>/CalculatorForm";
import { useCalculatorStatus } from "@/store/CalendarStore";

export default function RootLayout({ children }: { children: React.ReactNode }) {
  const { isShowCalculator, closeCalculator } = useCalculatorStatus();

  return (
    <>
      {children}
      <CalculatorForm isShow={isShowCalculator} onClose={closeCalculator} />
    </>
  );
}
