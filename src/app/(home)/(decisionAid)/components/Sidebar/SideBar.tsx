import Button from "@/components/atoms/button";
import Checkbox from "@/components/atoms/checkbox/Checkbox";
import Input from "@/components/atoms/input";
import routes from "@/constants/routes";
import { commaSeparator, insertParamsToUrl } from "@/utils/helpers";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect } from "react";
import { Controller, SubmitHandler, useForm } from "react-hook-form";
import { IFormInputs } from "./type";
import { withOrWithOut } from "./utils";

function SideBar() {
  const router = useRouter();
  const searchParams = useSearchParams();

  const MinimumYtm = Number(searchParams.get("MinimumYtm")) || undefined;
  const MinimumMaturityRemainedDays = Number(searchParams.get("MinimumMaturityRemainedDays")) || undefined;
  const MaximumMaturityRemainedDays = Number(searchParams.get("MaximumMaturityRemainedDays")) || undefined;
  const BondType = Number(searchParams.get("BondType")) || undefined;
  const MinimumInvestmentPerBond = Number(searchParams.get("MinimumInvestmentPerBond")) || undefined;
  const MaximumInvestmentBudget = Number(searchParams.get("MaximumInvestmentBudget")) || undefined;

  const { handleSubmit, control, reset, watch } = useForm<IFormInputs>({
    defaultValues: {
      minimumYtm: MinimumYtm !== undefined && MinimumYtm !== null ? Number((MinimumYtm * 100).toFixed(5)) : undefined,
      minimumMaturityRemainedDays: MinimumMaturityRemainedDays,
      maximumMaturityRemainedDays: MaximumMaturityRemainedDays,
      minimumInvestmentPerBond: MinimumInvestmentPerBond,
      maximumInvestmentBudget: MaximumInvestmentBudget,
      withCoupon: BondType === null ? true : BondType === withOrWithOut?.withCoupon,
      withOutCoupon: BondType === null ? true : BondType === withOrWithOut?.withOutCoupon
    }
  });

  const minimumWatch = watch("minimumYtm");
  const isDisable = minimumWatch === undefined || minimumWatch === null;

  useEffect(() => {
    if (isDisable) router.push(routes.decisionAidSingle);
  }, [minimumWatch]);

  const onSubmit: SubmitHandler<IFormInputs> = data => {
    let bondTypeValue;
    if (!data?.withCoupon && !data?.withOutCoupon) {
      bondTypeValue = 0;
    } else if (data?.withCoupon && !data?.withOutCoupon) {
      bondTypeValue = withOrWithOut?.withCoupon;
    } else if (!data?.withCoupon && data?.withOutCoupon) {
      bondTypeValue = withOrWithOut?.withOutCoupon;
    }

    const urlParams = {
      MinimumYtm: Number(data?.minimumYtm) / 100,
      MinimumMaturityRemainedDays: data?.minimumMaturityRemainedDays,
      MaximumMaturityRemainedDays: data?.maximumMaturityRemainedDays,
      BondType: bondTypeValue,
      MinimumInvestmentPerBond: data?.minimumInvestmentPerBond,
      MaximumInvestmentBudget: data?.maximumInvestmentBudget
    };

    const url = insertParamsToUrl(routes.decisionAidAggregate, urlParams);

    router.replace(url);
  };

  const onReset = () => {
    reset(
      {
        minimumYtm: null,
        maximumInvestmentBudget: null,
        maximumMaturityRemainedDays: null,
        minimumInvestmentPerBond: null,
        minimumMaturityRemainedDays: null,
        withCoupon: null,
        withOutCoupon: null
      },
      { keepValues: false }
    );
    router.push(routes.decisionAidSingle);
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="h-full flex flex-col justify-between overflow-auto p-3">
      <div className="flex flex-col gap-2">
        <div className="flex flex-col gap-2">
          <div className="flex">
            <div className="text-mainText text-xs">حداقل YTM مطلوب</div>
            <div className="text-textDescending text-xs">*</div>
          </div>
          <Controller
            name="minimumYtm"
            control={control}
            render={({ field, fieldState: { error } }) => (
              <Input
                type="number"
                data-test="9116f3bc-1145-4073-a942-4060ab20d9ae"
                min={0}
                max={2000}
                onChange={(e: string) => field?.onChange(e.replace(/[^0-9.]/g, "") || null)}
                value={field?.value ? field?.value : ""}
                errorMessage={error?.message}
                title="نرخ مطلوب"
                startAdornment={<div className="text-mainText mt-2">%</div>}
              />
            )}
          />
        </div>
        <div className="flex flex-col gap-2">
          <div className="text-mainText text-xs">روز تا سررسید</div>
          <div className="flex gap-2">
            <Controller
              name="minimumMaturityRemainedDays"
              control={control}
              render={({ field }) => (
                <Input<number>
                  type="number"
                  data-test="2a8bae6a-f307-40bb-9b6e-541fc5ef1e0f"
                  className="text-center"
                  min={0}
                  max={10000}
                  isDisabled={!minimumWatch}
                  onChange={e => field?.onChange(Number(e) || null)}
                  value={field?.value ? field?.value : ""}
                  endAdornment={<div className="text-secondaryText pt-1">از</div>}
                  startAdornment={<div className="text-secondaryText">روز</div>}
                />
              )}
            />
            <Controller
              name="maximumMaturityRemainedDays"
              control={control}
              render={({ field }) => (
                <Input<number>
                  type="number"
                  className="text-center"
                  min={0}
                  max={10000}
                  isDisabled={!minimumWatch}
                  onChange={e => field?.onChange(Number(e) || null)}
                  value={field?.value ? field?.value : ""}
                  data-test="d0b28990-1cfe-4ed4-a262-eb643df2d74c"
                  endAdornment={<div className="text-secondaryText pt-1">تا</div>}
                  startAdornment={<div className="text-secondaryText">روز</div>}
                />
              )}
            />
          </div>
        </div>

        <div className="flex flex-col gap-2">
          <div className="text-mainText text-xs">نوع اوراق</div>
          <div className="flex gap-4">
            <Controller
              name="withCoupon"
              control={control}
              render={({ field }) => (
                <Checkbox
                  size="medium"
                  text="کوپن دار"
                  disabled={!minimumWatch}
                  variant="filledGreen"
                  data-test="c7ede6d3-3f7b-43c0-91c4-c76e381324c3"
                  id="withCoupon"
                  onChange={() => {
                    field?.onChange(!field?.value);
                  }}
                  checked={field?.value ? field?.value : false}
                />
              )}
            />
            <Controller
              name="withOutCoupon"
              control={control}
              render={({ field }) => (
                <Checkbox
                  size="medium"
                  text="بدون کوپن"
                  disabled={!minimumWatch}
                  data-test="1fac6fa1-ecba-4007-95ea-861c8cf16375"
                  id="withOutCoupon"
                  variant="filledLightYellow"
                  onChange={() => {
                    field?.onChange(!field?.value);
                  }}
                  checked={field?.value ? field?.value : false}
                />
              )}
            />
          </div>
        </div>

        <div className="flex mt-6 flex-col gap-2">
          <div className="text-mainText text-xs">حداقل ارزش معامله یک نماد</div>
          <Controller
            name="minimumInvestmentPerBond"
            control={control}
            render={({ field }) => (
              <Input<number>
                type="number"
                isDisabled={!minimumWatch}
                data-test="fc26e732-0917-4281-9f0a-acbae894c2fc"
                onChange={e => field?.onChange(Number(e) || null)}
                value={field?.value ? commaSeparator(field?.value) : ""}
                startAdornment={<div className="text-secondaryText text-xs">ریال</div>}
              />
            )}
          />
        </div>

        <div className="flex flex-col gap-2">
          <div className="text-mainText text-xs">سقف قدرت خرید شما</div>
          <Controller
            name="maximumInvestmentBudget"
            control={control}
            render={({ field }) => (
              <Input<number>
                type="number"
                isDisabled={!minimumWatch}
                data-test="d6e4057f-0706-40d8-b68a-199fb6de1c27"
                onChange={e => field?.onChange(Number(e) || null)}
                value={field?.value ? commaSeparator(field?.value) : ""}
                startAdornment={<div className="text-secondaryText text-xs">ریال</div>}
              />
            )}
          />
        </div>
      </div>

      <div className="flex gap-2">
        <Button
          className="flex-1"
          variant="outLine"
          disabled={isDisable}
          data-test="27ff0f73-cd94-407a-b9b5-07087a4b32f8"
          onClick={() => {
            onReset();
          }}
        >
          پاک کردن
        </Button>
        <Button
          className="flex-1"
          data-test="d36b3505-7fd2-4a21-99f3-bc353b21ade1"
          variant="fill"
          disabled={isDisable}
          type="submit"
        >
          اعمال
        </Button>
      </div>
    </form>
  );
}

export default SideBar;
