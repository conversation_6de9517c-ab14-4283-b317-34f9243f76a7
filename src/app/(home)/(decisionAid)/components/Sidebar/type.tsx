export interface IParams {
  pageSize?: number | null;
  pageNumber?: number | null;
  MinimumYtm?: number | null;
  MinimumMaturityRemainedDays?: number | null;
  MaximumMaturityRemainedDays?: number | null;
  BondType?: number | null;
  MinimumInvestmentPerBond?: number | null;
  MaximumInvestmentBudget?: number | null;
}

export interface ISideBarProps {
  setValues: (collapsible: boolean, params: IParams) => void;
  params: IParams;
}

export interface IFormInputs {
  minimumYtm: number | null;
  withCoupon?: boolean | null;
  withOutCoupon?: boolean | null;
  minimumMaturityRemainedDays?: number | null;
  maximumMaturityRemainedDays?: number | null;
  minimumInvestmentPerBond: number | null;
  maximumInvestmentBudget: number | null;
}
