import React, { useEffect, useState } from "react";

function PassedTimer({ submittedAt: initialSubmittedAt }: { submittedAt: number | undefined }) {
  const [submittedAt, setSubmittedAt] = useState(initialSubmittedAt);
  const [timer, setTimer] = useState(1);

  useEffect(() => {
    setSubmittedAt(initialSubmittedAt);
    setTimer(0);
  }, [initialSubmittedAt]);

  useEffect(() => {
    let myInterval: NodeJS.Timeout | null = null;

    if (submittedAt) {
      setTimer(prev => prev + 1);

      myInterval = setInterval(() => {
        setTimer(prev => prev + 1);
      }, 60000);
    }

    return () => {
      if (myInterval) clearInterval(myInterval);
    };
  }, [submittedAt]);

  return timer === 0 ? 1 : timer;
}

export default React.memo(PassedTimer);
