"use client";

import BestYtmContainer from "@/containers/home/<USER>";
import GeneralStatisticsContainer from "@/containers/home/<USER>";
import FilterBox from "@/components/organisms/filterBox/FilterBox";
import { BondTable } from "@/containers/home/<USER>";
import useToggleCollapse from "../CollapseStore";

export default function Home() {
  const { isCollapsed } = useToggleCollapse();

  return (
    <main className="px-4 pb-4 w-full h-full flex flex-col">
      <GeneralStatisticsContainer isCollapsed={isCollapsed} />
      <BestYtmContainer isCollapsed={isCollapsed} />

      <div className="flex flex-1 h-0 mt-2 gap-2 ">
        <FilterBox />
        <BondTable />
      </div>
    </main>
  );
}
