import { z } from "zod";
import { createUniqueFieldSchema } from "@ts-react/form";
import InputForm from "@/components/atoms/input/Input.form";
import PasswordForm from "@/components/atoms/input/password.form";
import TextareaForm from "@/components/atoms/textarea/Textarea.form";
import InputNumberForm from "@/components/atoms/input/InputNumber.form";
import { MOBILE_NUMBER_REGEX, PASSWORD_REGEX } from "@/constants/regex";

/* -------------------------------------------------------------------------- */
/*                                custom schema                               */
/* -------------------------------------------------------------------------- */

export const TextareaSchema = createUniqueFieldSchema(
  z.string().min(10, "حداقل  باید 10  کاراکتر بنوسید ").optional(),
  "textArea"
);

export const PasswordSchema = (passwordZod?: z.ZodString) =>
  createUniqueFieldSchema<z.ZodString, string>(
    passwordZod ||
      z
        .string()
        .nonempty("رمز عبور اجباری است")
        .regex(
          PASSWORD_REGEX,
          "رمز عبور باید حداقل 8 کاراکتر و ترکیبی از اعداد و حروف و شامل یک کاراکتر بزرگ و کوچک باشد"
        ),
    "Password"
  );

export const mobileNumberSchema = (mobileNumberZod?: any) =>
  createUniqueFieldSchema<z.ZodString, string>(
    mobileNumberZod ||
      z
        .string()
        .nonempty("شماره موبایل اجباری است")
        .length(11, "شماره موبایل باید 11 کاراکتر باشد")
        .regex(MOBILE_NUMBER_REGEX, "شماره موبایل می بایست با فرمت صحیح و اعدادcustomComponent انگلیسی وارد شود"),
    "MobileNumber"
  );

/* -------------------------------------------------------------------------- */
/*                               mapping schema                               */
/* -------------------------------------------------------------------------- */
const mapping = [
  [z.string(), InputForm],
  [PasswordSchema(), PasswordForm],
  [TextareaSchema, TextareaForm],
  [mobileNumberSchema(), InputForm],
  [z.number(), InputNumberForm]
] as const;

export default mapping;
