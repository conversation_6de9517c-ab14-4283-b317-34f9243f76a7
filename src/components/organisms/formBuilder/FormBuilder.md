# document link: https://react-ts-form.com/docs/docs/usage/quick-start

# How to use form builder :

```ts
const ITEMS =[{
        id:"id1",
        name:"name"
      }]

function Test() {
  const FormSchema = z.object({
    text: z.string().describe("Text Field").min(4, "it's too short"),
    richText: RichTextSchema.describe("RichText"),
    select: SelectSchema().describe("select field)
  });

  return (
    <FormBuilder
      schema={FormSchema}
      formProps={{
        buttonText: "confirm"
      }}
      onSubmit={data => console.log("data", data)}
    />
  );
}
```
