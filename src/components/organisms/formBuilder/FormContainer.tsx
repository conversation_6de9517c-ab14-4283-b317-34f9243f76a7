import CardTitle from "@/components/atoms/cardTitle";
import { useMemo } from "react";
import { useFormContext } from "react-hook-form";

import FormActions from "./FormActions";
import { IFormContainerProps } from "./types";

function FormContainer({
  children,

  // classes
  headerTitle,
  containerClass,
  actionsPosition,

  // Functions
  onSubmit,
  id,
  ...restProps
}: IFormContainerProps) {
  const {
    formState: { isDirty }
  } = useFormContext() || { formState: {} };

  const renderFormActions = useMemo(
    () => (
      <FormActions
        {...restProps}
        id={id}
        headerTitle={headerTitle}
        actionsPosition={actionsPosition}
        isDirty={isDirty}
      />
    ),
    [actionsPosition, headerTitle, isDirty, restProps, id]
  );

  return (
    <form id={id} onSubmit={onSubmit}>
      <div className={actionsPosition === "top" ? "flex items-center justify-between" : ""}>
        <CardTitle>{headerTitle}</CardTitle>
        {actionsPosition === "top" && renderFormActions}
      </div>
      <div className={containerClass}>
        {children}
        {actionsPosition === "bottom" && renderFormActions}
      </div>
    </form>
  );
}

FormContainer.defaultProps = {
  isLoading: false,
  actionsPosition: "bottom"
};

export default FormContainer;
