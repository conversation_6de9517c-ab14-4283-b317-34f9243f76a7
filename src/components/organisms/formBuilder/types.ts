import React, { ReactNode } from "react";

export interface IFormContainerProps {
  id?: string;
  /**
   * position of actions
   * @default bottom
   */
  actionsPosition?: "top" | "bottom";

  /**
   * title of header
   */
  headerTitle?: string;

  /**
   * text of button
   */
  buttonText: ReactNode;

  /**
   * loading
   * @default false
   */
  isLoading?: boolean;

  /**
   * disabled submit button
   * @default false
   */
  disabled?: boolean;

  /**
   * custom button classes
   */
  buttonClass?: string;

  /**
   * custom container classes
   */
  containerClass?: string;

  /**
   * children
   */
  children: React.ReactNode;

  /**
   * custom root footer classes
   */
  footerRootClassName?: string;

  /**
   * custom footer classes
   */
  footerClassName?: string;

  /**
   * custom body classes
   */
  bodyClassName?: string;

  /**
   * custom cancel button classes
   */
  cancelClass?: string;

  /**
   * cancel button text
   */
  cancelText?: string;

  /**
   * fire click on cancel button
   */
  onCancel?: () => void;

  /**
   * fire submit button
   */
  onSubmit: () => void;

  /**
   * render custom element
   */
  customActionElement?: React.ReactNode;
}

export interface IFormActionsProps extends Omit<IFormContainerProps, "children" | "containerClass" | "onSubmit"> {
  /**
   * check for is changed or not
   */
  isDirty?: boolean;
}
