import Button from "@/components/atoms/button";
import { twMerge } from "tailwind-merge";
import { IFormActionsProps } from "./types";

function FormActions({
  // booleans
  isDirty,
  disabled,
  isLoading,

  // classes
  cancelClass,
  buttonClass,
  footerClassName,

  // texts
  cancelText,
  buttonText,

  customActionElement,

  // actions
  onCancel,
  id
}: IFormActionsProps) {
  return (
    <div className={twMerge("flex items-center justify-between gap-4 ", footerClassName)}>
      {customActionElement && customActionElement}

      <Button
        type="submit"
        isLoading={isLoading}
        disabled={disabled ?? (disabled || !isDirty)}
        className={buttonClass}
        data-test={`${id ? `${id}-` : ""}submit`}
      >
        {buttonText}
      </Button>

      {!!cancelText && (
        <Button type="button" onClick={onCancel} className={cancelClass}>
          {cancelText}
        </Button>
      )}
    </div>
  );
}

export default FormActions;
