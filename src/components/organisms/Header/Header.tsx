/* eslint-disable no-nested-ternary */

"use client";

import { useGetProfileQuery } from "@/queries/profileAPI";
import { Spinner } from "@/components/atoms/spinner";
import { BreadCrumb } from "@/components/molecules/nextBreadCrumb";
import { IBreadCrumbProps } from "@/components/molecules/nextBreadCrumb/types";
import { HeaderTime, HeaderUser } from "./components";
import MarketStatus from "./components/MarketStatus";
import useNetwork from "./useNetwork";
import Disconnected from "./Disconnected";

function Header({ breadCrumbItems }: IBreadCrumbProps) {
  const { data: profile, isLoading } = useGetProfileQuery();
  const isOnline = useNetwork();

  return (
    <>
      <div className="h-14 w-full bg-bodyBackground text-[#F4F4F4] sticky top-0 z-50 shrink-0">
        <div className="flex h-full w-full items-center justify-between px-1 text-xs sm:px-3 md:px-4 md:text-sm lg:px-4">
          <BreadCrumb breadCrumbItems={breadCrumbItems} />
          <div className="flex gap-2">
            <HeaderTime />
            <div className="flex gap-4 ">
              <MarketStatus />
              <div className="w-[1px] h-6 bg-[#76787A]" />
              {isLoading ? <Spinner /> : <HeaderUser profileName={profile?.data?.userName} />}
            </div>
          </div>
        </div>
      </div>
      {!isOnline && <Disconnected />}
    </>
  );
}

export default Header;
