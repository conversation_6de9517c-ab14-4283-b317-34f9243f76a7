/* eslint-disable import/prefer-default-export */
/* eslint-disable no-nested-ternary */

import Loading from "@/assets/spinner.svg";
import { Tooltip } from "@/components/atoms/tooltip";
import { IsMarketOpen, useServerTime } from "./utils";

function MarketStatus() {
  const { time, isMarketOpen } = useServerTime();
  const isMarketOpenIncludeTime = time && isMarketOpen && IsMarketOpen(time, isMarketOpen);

  return (
    <div className="flex items-center gap-2">
      <div>
        {!time ? (
          <Loading className="w-5 h-5 animate-spin" />
        ) : isMarketOpenIncludeTime ? (
          <Tooltip className="w-fit" content="بازار باز است" variant="green">
            <div className="w-[6px] h-[6px] bg-textAscending rounded-full shadow-ascendingShadow" />
          </Tooltip>
        ) : (
          <Tooltip className="w-fit" content="بازار بسته است" variant="orange">
            <div className="w-[6px] h-[6px] bg-warningText rounded-full shadow-warningShadow" />
          </Tooltip>
        )}
      </div>
    </div>
  );
}
export default MarketStatus;
