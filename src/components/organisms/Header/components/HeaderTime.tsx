import Loading from "@/assets/spinner.svg";
import { dateConverter } from "@/utils/DateHelper";
import persianMonth from "@/utils/date";
import { useServerTime } from "./utils";

function HeaderTime() {
  const { time } = useServerTime();

  return !time ? (
    <Loading className="w-5 h-5 animate-spin" />
  ) : (
    <div className=" flex items-center gap-2 ">
      <span className="text-[12px]  font-semibold">{dateConverter(time)?.format("HH:mm:ss")}</span>
      <span className=" text-[12px] ">
        {dateConverter(time).format("DD")} / {persianMonth[+dateConverter(time).format("MM")]}/{" "}
        {dateConverter(time).format("YYYY")}
      </span>
    </div>
  );
}

export default HeaderTime;
