import { useGetMarketStatusQuery } from "@/queries/marketStatusAPI";
import { toAsiaTehranTimeZone } from "@/utils/DateHelper";
import { useEffect, useState } from "react";

/**
 * true => market is open
 * false => market is close
 */
export function IsMarketOpen(currentDateTime: number, isMarketOffDate: boolean): boolean {
  // check status

  // if open
  if (!isMarketOffDate) return true;

  // if time after 8:30 and before 15
  if (new Date().setHours(15, 0, 0) > currentDateTime && new Date().setHours(8, 30, 0) < currentDateTime) return true;

  return false;
}

export const useServerTime = (): { time: number | undefined; isMarketOpen: boolean | undefined } => {
  const { data: marketStatusData, dataUpdatedAt } = useGetMarketStatusQuery();
  const [timeWhenComponentMount, setTimeWhenComponentMount] = useState(toAsiaTehranTimeZone(new Date()));
  const [statusCurrentDateTime, setStatusCurrentDateTime] = useState(
    marketStatusData && toAsiaTehranTimeZone(new Date(marketStatusData?.data?.currentDateTime)).getTime()
  );

  useEffect(() => {
    setTimeWhenComponentMount(toAsiaTehranTimeZone(new Date()));
  }, []);

  const calculateCurrentDateTime = () => {
    if (marketStatusData && dataUpdatedAt) {
      const timeDifferenceBetweenNowAndApiResponse =
        toAsiaTehranTimeZone(new Date()).getTime() - toAsiaTehranTimeZone(new Date(dataUpdatedAt)).getTime();
      const timeDifferenceBetweenMountAndApiResponse =
        timeWhenComponentMount.getTime() - toAsiaTehranTimeZone(new Date(dataUpdatedAt)).getTime();

      setStatusCurrentDateTime(
        toAsiaTehranTimeZone(
          new Date(
            new Date(marketStatusData?.data?.currentDateTime).getTime() +
              timeDifferenceBetweenNowAndApiResponse +
              timeDifferenceBetweenMountAndApiResponse
          )
        ).getTime()
      );
    }
  };

  useEffect(() => {
    calculateCurrentDateTime();

    const timer = setInterval(() => {
      calculateCurrentDateTime();
    }, 1000);

    return () => clearInterval(timer);
  }, [marketStatusData?.data?.currentDateTime]);

  return { time: statusCurrentDateTime, isMarketOpen: !marketStatusData?.data.isMarketOffDate };
};
