/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import ArrowDown from "@/assets/icons/arrow-down-header.svg";
import Logout from "@/assets/icons/logout-header.svg";
import AdminUser from "@/assets/icons/admin-user.svg";
import { ClickAwayListener } from "@/components/atoms/clickAwayListener";
import { removeCookieAndRedirectToLogin } from "@/utils/logout";
import { useState } from "react";

import { logoutApiCall } from "@/utils/logoutWithApiCall";
import { useRouter } from "next/navigation";
import { IHeaderUserProps } from "../types";

function HeaderUser({ profileName = "کاربر مهمان" }: IHeaderUserProps) {
  const [isOpen, setIsOpen] = useState(false);
  const router = useRouter();

  return (
    <ClickAwayListener onClickAway={() => setIsOpen(false)}>
      <div
        className="flex items-center gap-2 h-full text-light "
        onClick={() => setIsOpen(prev => !prev)}
        data-test="3c5cbf10-c7c1-41dc-8bb8-ca33ff0201f2"
      >
        <span role="presentation" className="w-17 space-x-4 cursor-pointer text-sm  truncate text-dark-text/80">
          {profileName}
        </span>
        <ArrowDown className="w-2 h-2 text-mainText" />
      </div>

      {isOpen && (
        <div className="bg-backgroundCardBackground flex-col  text-[10px] border cursor-pointer absolute left-4 top-9 border-borderBorderAndDivider rounded-[4px] flex items-center ">
          <div
            className="text-[#EFEFEF] flex items-center gap-2 hover:bg-white50 w-full rounded-[4px]  py-[5px] pr-1.5 pl-4"
            onClick={() => {
              router.push(process.env.NEXT_PUBLIC_ADMIN_URL ?? "");
            }}
          >
            <AdminUser />
            <span> پنل مدیریت</span>
          </div>

          <div
            className=" text-warningText flex items-center gap-2 hover:bg-white50 w-full  rounded-[4px]  py-[5px] pr-1.5 pl-4"
            onClick={() => {
              logoutApiCall();
              setTimeout(() => {
                removeCookieAndRedirectToLogin();
              }, 200);
            }}
            data-test="317a7760-27b6-4a05-b037-0080c4a59dfa"
          >
            <Logout />
            <span>خروج از حساب کاربری</span>
          </div>
        </div>
      )}
    </ClickAwayListener>
  );
}

export default HeaderUser;
