import { TDatePickerProps } from "headless-react-datepicker";
import { PopOver } from "@/components/atoms/popper";
import { Ref, forwardRef, useImperativeHandle, useState } from "react";
import DatePicker from "./DatePicker";
import { IDatePickerWrapperProps, IDatePickerWrapperRefProps } from "./types";

function DatePickerWrapper<T extends boolean = boolean>(
  {
    children,
    placement = "bottom-start",
    className = "",
    onCancel,
    onConfirm,
    closeOnConfirm = true,
    ...restProps
  }: IDatePickerWrapperProps<T>,
  ref: Ref<IDatePickerWrapperRefProps> // Ref should be of type DatePickerWrapperRef
) {
  const [isOpen, setIsOpen] = useState(restProps?.isOpen ?? false);

  const toggleOpen = (v: any) => setIsOpen(v);

  const handleCancel = () => {
    onCancel?.();
    setIsOpen(false);
  };

  const handleConfirm = (value?: TDatePickerProps<T>["initialValue"]) => {
    onConfirm?.(value);

    if (closeOnConfirm) {
      setIsOpen(false);
    }
  };

  useImperativeHandle(ref, () => ({
    close() {
      setIsOpen(false);
    },
    open() {
      setIsOpen(true);
    }
  }));

  return (
    <PopOver
      setIsOpen={toggleOpen}
      isOpen={isOpen}
      placement={placement}
      className={className}
      content={<DatePicker setIsOpen={toggleOpen} onCancel={handleCancel} onConfirm={handleConfirm} {...restProps} />}
    >
      {children}
    </PopOver>
  );
}

const ForwardedDatePickerWrapper = forwardRef(DatePickerWrapper) as <T extends boolean = boolean>(
  props: IDatePickerWrapperProps<T> & { ref?: Ref<IDatePickerWrapperRefProps> }
) => React.ReactElement;

export default ForwardedDatePickerWrapper;
