import Input from "@/components/atoms/input";
import { TInputProps } from "@/components/atoms/input/types";
import { PopOver } from "@/components/atoms/popper";
import { dateConverter } from "@/utils/DateHelper";
import { TDatePickerOnChange } from "headless-react-datepicker";
import { forwardRef, ReactNode, useEffect, useImperativeHandle, useRef, useState } from "react";
import { twMerge } from "tailwind-merge";
import DatePicker from "./DatePicker";
import { IDatePickerProps } from "./types";

export interface ITwoInputsRangePickerProps extends Omit<IDatePickerProps<true>, "isRange"> {
  children?: ReactNode;
  onClose?: () => void;
  rootClassName?: string;
  labels?: [string, string];
  input1props?: TInputProps;
  input2props?: TInputProps;
  disabled?: boolean;
  id?: string;
  onChange?: (value: Date[], activeInput?: 1 | 2) => void;
}

const TwoInputsRangePicker = forwardRef(
  (
    {
      id,
      children,
      onCancel,
      onConfirm,
      onClose,
      onChange: onChangeProps,
      value: valueProp,
      rootClassName,
      labels = ["از", "تا"],
      input1props,
      input2props,
      disabled,
      ...restProps
    }: ITwoInputsRangePickerProps,
    ref
  ) => {
    const [isOpen, setIsOpen] = useState(false);
    const [value, setValue] = useState<Date[] | undefined>(valueProp);
    const [activeInput, setActiveInput] = useState<1 | 2 | undefined>(undefined);
    const sortedValue = value?.sort((a, b) => new Date(a).getTime() - new Date(b).getTime());

    const input1Ref = useRef<HTMLInputElement>(null);
    const input2Ref = useRef<HTMLInputElement>(null);

    const handleCancel = () => {
      onCancel?.();
      setIsOpen(false);
    };

    useEffect(() => {
      if (value?.toString() !== valueProp?.toString()) {
        setValue(valueProp);
      }

      if (valueProp === undefined) {
        setValue(undefined);
      }

      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [valueProp]);

    useEffect(() => {
      if (!isOpen) {
        onClose?.();
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isOpen]);

    function reFocusInputs() {
      if (activeInput === 1) {
        input1Ref.current?.focus();
      }

      if (activeInput === 2) {
        input2Ref.current?.focus();
      }
    }

    useEffect(() => {
      reFocusInputs();
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [activeInput, input1Ref, input2Ref]);

    const forceRerender = () => {
      setIsOpen(false);
      setTimeout(() => {
        setIsOpen(true);
      }, 0);
    };

    useEffect(() => {
      if (activeInput === 1 && value?.[0]) {
        setActiveInput(2);
        reFocusInputs();

        // makes re-render to make the date picker's defaultStartDate work
        forceRerender();
      }

      if (activeInput === 2 && value?.[1]) {
        setIsOpen(false);
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [value]);

    const onChange: TDatePickerOnChange<true> = v => {
      // check if input 2 is focused
      if (activeInput === 2 && v[0]) {
        setValue(prev => {
          const finalValue = prev?.[0] ? [prev[0], v[1] || v[0]] : v;

          onChangeProps?.(finalValue, activeInput);
          return finalValue;
        });

        return;
      }

      if (activeInput === 1 && value?.[1]) {
        setValue(prev => {
          const finalValue = prev?.[1] ? [v[0], prev[1]] : v;

          onChangeProps?.(finalValue, activeInput);
          return finalValue;
        });

        return;
      }

      setValue(v);
      onChangeProps?.(v, activeInput);
    };

    useImperativeHandle(ref, () => ({
      close() {
        setIsOpen(false);
      }
    }));
    return (
      <PopOver
        setIsOpen={setIsOpen}
        isOpen={isOpen}
        content={
          <DatePicker
            hasFooter={false}
            {...restProps}
            isRange
            onChange={onChange}
            value={sortedValue}
            onCancel={handleCancel}
            defaultStartDate={activeInput === 1 ? sortedValue?.[0] : sortedValue?.[1]}
          />
        }
      >
        <div className={twMerge("flex items-center w-full gap-4", rootClassName)}>
          <Input
            ref={input1Ref}
            title={labels[0]}
            wrapperClassName="w-1/2"
            value={sortedValue?.[0] ? dateConverter(sortedValue?.[0]).toString() : ""}
            onClick={() => setActiveInput(1)}
            preserveErrorMessage={false}
            isDisabled={disabled}
            readOnly
            data-test={`tirp-${id}-input1`}
            {...input1props}
          />
          <Input
            ref={input2Ref}
            title={labels[1]}
            wrapperClassName="w-1/2"
            value={sortedValue?.[1] ? dateConverter(sortedValue?.[1]).toString() : ""}
            onClick={() => setActiveInput(2)}
            preserveErrorMessage={false}
            isDisabled={disabled}
            readOnly
            data-test={`tirp-${id}-input2`}
            {...input2props}
          />
        </div>
      </PopOver>
    );
  }
);
export default TwoInputsRangePicker;
