import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import TwoInputsRangePicker, {
  ITwoInputsRangePickerProps
} from "@/components/organisms/datePicker/TwoInputsRangePicker";
import DatePicker from "./DatePicker";
import { IDatePickerProps } from "./types";

const meta = {
  title: "Components/Organism/DatePicker",
  component: DatePicker,
  parameters: {
    // More on how to position stories at: https://storybook.js.org/docs/configure/story-layout
    layout: "fullscreen",
    deepControls: { enabled: true }
  },
  // This component will have an automatically generated Autodocs entry: https://storybook.js.org/docs/writing-docs/autodocs
  tags: ["autodocs"],
  argTypes: {
    config: {
      control: "null"
    },
    // @ts-expect-error [values are typed]
    "config.locale": {
      control: "select",
      options: ["fa-IR", "en-US", "ar-EG", "hi-IN"]
    },
    "config.weekStartsOn": {
      control: "select",
      options: ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"]
    },
    "config.weekdayFormat": {
      control: "select",
      options: ["long", "short", "narrow"]
    },
    "config.showOtherDays": {
      control: "boolean"
    },
    "config.otherDaysSelectable": {
      control: "boolean"
    },
    "config.dayFormat": {
      control: "select",
      options: ["numeric", "2-digit"]
    },
    "config.yearRangeFrom": {
      control: "number"
    },
    "config.yearRangeTo": {
      control: "number"
    },
    "config.minDate": {
      control: "date"
    },
    "config.maxDate": {
      control: "date"
    },
    "config.weekends": {
      control: "check",
      options: ["saturday", "sunday", "monday", "tuesday", "wednesday", "thursday", "friday"]
    },
    "config.weekendSelectable": {
      control: "boolean"
    }
  }
} satisfies Meta<typeof DatePicker>;

export default meta;
type Story = StoryObj<typeof meta>;

function RenderDatePicker<T extends boolean>(props: IDatePickerProps<T>) {
  return <DatePicker {...props} />;
}

export const SDPPersian: Story = {
  render: RenderDatePicker,
  args: {
    isRange: true,
    initialValue: [new Date("2024-02-06"), new Date("2024-02-08")],
    calendar: "persian",
    config: {
      locale: "fa-IR",
      weekStartsOn: "saturday",
      showOtherDays: false,
      otherDaysSelectable: false,
      weekdayFormat: "narrow",
      dayFormat: "numeric",
      weekends: ["thursday", "friday"]

      // yearRangeFrom: 1330,
      // yearRangeTo: 1400,
      // minDate: new Date("2024-01-01T00:00:00.000Z"),
      // maxDate: new Date(),
    }
  }
};
function RenderDatePickerControlled(props: ITwoInputsRangePickerProps) {
  return (
    <div className="p-16 max-w-xl mx-auto">
      <TwoInputsRangePicker
        {...props}
        onChange={v => {
          console.log("onChange", v);
        }}
      />
    </div>
  );
}

export const TwoInputs = {
  render: RenderDatePickerControlled,
  args: {
    initialValue: [new Date("2024-02-06T00:00:00"), new Date("2024-02-08T00:00:00")],
    calendar: "persian",
    config: {
      locale: "fa-IR",
      weekStartsOn: "saturday",
      showOtherDays: false,
      otherDaysSelectable: false,
      weekdayFormat: "narrow",
      dayFormat: "numeric",
      weekends: ["thursday", "friday"]

      // yearRangeFrom: 1330,
      // yearRangeTo: 1400,
      // minDate: new Date("2024-01-01T00:00:00.000Z"),
      // maxDate: new Date(),
    }
  }
} satisfies Meta<typeof TwoInputsRangePicker>;
