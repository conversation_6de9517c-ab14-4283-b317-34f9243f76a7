import DatePickerProvider, { DaySlots, TDatePickerProps, WeekDays } from "headless-react-datepicker";
import { useCallback, useState } from "react";
import { twMerge } from "tailwind-merge";

import { IDatePickerProps } from "./types";
import { Footer, Header, Title } from "./utils";

import "headless-react-datepicker/dist/styles.css";
import "./DatePicker.scss";

export default function DatePicker<T extends boolean>({
  onCancel,
  setIsOpen,
  onConfirm,
  hasFooter = true,
  ...restProps
}: IDatePickerProps<T>) {
  const [value, setValue] = useState<TDatePickerProps<T>["initialValue"]>();

  const onChange = useCallback(
    (date: any) => {
      setValue(date);
      restProps?.onChange?.(date);
      if (!restProps?.isRange) onConfirm?.(date);
    },
    [onConfirm, restProps, hasFooter]
  );

  const isRangeButSingleSelected = restProps?.isRange && Array.isArray(value) && value?.length === 1;

  return (
    <div
      style={{
        background: "#343438",
        color: "#F4F4F4",
        border: "0.5px solid #B9B4AB",
        width: "288px",
        borderRadius: "8px",
        margin: "0 auto",
        minHeight: "310px"
      }}
    >
      <DatePickerProvider<T>
        {...restProps}
        config={{
          locale: "fa-IR",
          weekends: ["friday"],

          ...restProps?.config
        }}
        initialValue={restProps?.initialValue || value}
        calendar="persian"
        onChange={onChange}
      >
        <Title
          onClose={() => {
            setIsOpen?.(false);
            onCancel?.();
          }}
        />
        <Header />
        <div className="px-2 ">
          <WeekDays className="text-sm text-[#F4F4F4]  !border-[#545454] border-b-[0.5px] pb-1.5 text-center " />
        </div>
        <DaySlots
          slotClassName={twMerge(
            "dayslot text-[#F4F4F4] w-[28px] cursor-default max-h-[28px] flex items-center justify-center "
          )}
          slotParentClassName="py-0.5 mb-1 flex justify-center !cursor-default"
          selectableClassName="hover:!bg-semanticPrimary2 rounded-[4px]"
          weekendClassName="text-[#FF5E5E]"
          inSelectedRangeParentClassName={twMerge(isRangeButSingleSelected ? "bg-transparent" : "!bg-[#545454]")}
          inHoveredRangeParentClassName="!bg-[#545454]"
          endOfRangeClassName=" bg-semanticPrimary2 !rounded-tl-[4px] rounded-bl-[4px] !px-0 text-textNegative font-bold"
          endOfRangeParentClassName=" w-[35px] justify-end pr-0 pl-0.5"
          startOfRangeParentClassName={isRangeButSingleSelected ? "" : "w-[35px] justify-start pl-0 pr-0.5 mr-auto"}
          startOfRangeClassName={twMerge(
            " bg-semanticPrimary2 text-textNegative font-bold",
            isRangeButSingleSelected ? "border !border-2 border-[#545454]   " : "!rounded-tr-[4px] rounded-br-[4px] "
          )}
          disableClassName="opacity-50"
          todayClassName="border border-[#676767] rounded-[4px]"
          parentClassName=" text-sm py-[1px] date-picker-container"
          selectedClassName="bg-semanticPrimary2 !text-[white] rounded-[4px] hover:!bg-semanticPrimary2 "
        />
        {hasFooter && (
          <Footer
            disabled={Array.isArray(value) ? !value?.[1] : !value}
            onCancel={() => {
              onCancel?.();
              setValue(undefined);
            }}
            onConfirm={() => onConfirm?.(value)}
          />
        )}
      </DatePickerProvider>
    </div>
  );
}
