/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import ArrowLeft from "@/assets/icons/datePicker-arrowLeft.svg";
import ArrowRight from "@/assets/icons/datePicker-arrowRight.svg";
import DatePickerIcon from "@/assets/icons/datePickerIcon.svg";
import Button from "@/components/atoms/button";
import { Select } from "@/components/atoms/select";
import persianMonth from "@/utils/date";
import CloseIcon from "@/assets/icons/close-white.svg";
import { useDatePickerContext } from "headless-react-datepicker";
import "headless-react-datepicker/dist/styles.css";

import selectStyles from "./styles";
import { IFooterProps } from "./types";

interface ITitleProps {
  onClose: () => void;
}

export function Title({ onClose }: ITitleProps) {
  return (
    <div className="flex items-center justify-between border-b border-b-[#545454] pt-[11px] pb-[9px]  mx-3">
      <div className="flex items-center gap-1">
        <DatePickerIcon />
        <span className="text-xs">انتخاب تاریخ</span>
      </div>
      <CloseIcon onClick={onClose} className="cursor-pointer mb-2" />
    </div>
  );
}

export function Footer({ disabled, onCancel, onConfirm }: IFooterProps) {
  return (
    <div className="flex items-center justify-between mt-0 gap-2 mx-3 mb-3">
      <Button size="small" className="w-full" variant="outLineWhite" onClick={onCancel}>
        لغو
      </Button>
      <Button disabled={disabled} size="small" className="w-full" variant="fill" onClick={onConfirm}>
        تایید
      </Button>
    </div>
  );
}

export function Header() {
  const {
    yearInTheCalendar,
    monthInTheCalendar,
    yearsList,
    goToMonth,
    goToYear,
    monthsList,
    goToNextMonth,
    goToPrevMonth
  } = useDatePickerContext();

  return (
    <div className="flex items-center justify-between  mt-2 mb-5 mx-2">
      <div
        className="flex items-center select-none"
        onClick={goToPrevMonth}
        data-testid="c6bfe9a7-aa3d-45d4-a6ec-9e39364832ce"
      >
        <ArrowRight />
        <span className="text-[10px] text-[#BDBDBD] cursor-pointer">ماه قبلی</span>
      </div>
      <div className="flex items-center gap-1 text-[#F4F4F4] text-sm">
        {monthInTheCalendar && (
          <>
            <Select styles={selectStyles} items={monthsList || []} onChange={(val: any) => goToMonth(val.value)}>
              <span className="text-[#F4F4F4] !text-sm">{persianMonth[monthInTheCalendar]}</span>
            </Select>

            <Select
              styles={selectStyles}
              data-testid="date-select=cacc5d1c-e505-479e-9b62-b300931c67ff"
              onChange={(val: any) => goToYear(val.value)}
              items={yearsList?.map(item => ({ label: item.toString(), value: item })) || []}
            >
              <span className="text-[#F4F4F4] !text-sm">{yearInTheCalendar}</span>
            </Select>
          </>
        )}
      </div>
      <div
        className="flex items-center cursor-pointer select-none"
        onClick={goToNextMonth}
        data-testid="date-picker-title-856a9db8-8f73-4cc7-923b-5867f8b6c98e"
      >
        <span className="text-[10px] text-[#BDBDBD]">ماه بعدی</span>
        <ArrowLeft />
      </div>{" "}
    </div>
  );
}
