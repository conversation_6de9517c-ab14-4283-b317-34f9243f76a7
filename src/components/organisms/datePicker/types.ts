import { TDatePickerProps } from "headless-react-datepicker";
import { ReactNode } from "react";

export interface IDatePickerProps<T extends boolean> extends TDatePickerProps<T> {
  onCancel?: () => void;
  setIsOpen?: (val: boolean) => void;
  onConfirm?: (value: TDatePickerProps<T>["initialValue"]) => void;
  hasFooter?: boolean;
  isOpen?: boolean;
}

export interface IFooterProps {
  disabled: boolean;
  onCancel: () => void;
  onConfirm: () => void;
}

export interface IDatePickerWrapperProps<T extends boolean> extends IDatePickerProps<T> {
  children?: ReactNode;
  className?: string;
  placement?: "bottom-start" | "bottom-end";
  closeOnConfirm?: boolean;
}

export interface IDatePickerWrapperRefProps {
  close(): void;
  open(): void;
}
