import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { ColDef, ColGroupDef, GridOptions } from "ag-grid-community";

import Table from "./Table";

const meta: Meta<typeof Table> = {
  component: Table,
  title: "Components/organism/Table"
};

export default meta;
type Story = StoryObj<typeof Table>;

export const columnDefs: (ColDef | ColGroupDef)[] = [
  {
    field: "name",
    headerName: "نام اوراق",
    width: 100,
    unSortIcon: true,
    sortable: true
  },
  {
    field: "buyPrice",
    headerName: " قیمت خرید",
    width: 100,
    unSortIcon: true,
    sortable: true,
    cellRenderer: "renderYtm"
  },
  {
    field: "type",
    headerName: "کوپن",
    width: 100,
    cellClass: "flex justify-center",
    cellRenderer: "renderCoupon"
  },
  {
    field: "dueDate",
    headerName: "سر رسید",
    width: 100,
    cellRenderer: "renderDate"
  },
  {
    field: "transactionTime",
    headerName: "زمان معامله",
    width: 100,
    cellRenderer: "renderTransactionTime"
  }
];

const data = [
  {
    buyPrice: 1000,
    finalPrice: 2000,
    isin: "true",
    lastPrice: 5000,
    name: "data name",
    sellPrice: 6000,
    type: 1,
    dueDate: "2024-04-13T08:30:00+00:00",
    transactionTime: "2024-04-13T08:30:00+00:00"
  },
  {
    buyPrice: 2000,
    finalPrice: 2000,
    isin: "true",
    lastPrice: 5000,
    name: "data name2",
    sellPrice: 6000,
    type: 0,
    dueDate: "2024-04-13T08:30:00+00:00",
    transactionTime: "2024-04-13T08:30:00+00:00"
  }
];

export const gridOptions: GridOptions = {
  enableRtl: true,
  onGridSizeChanged: () => {
    gridOptions.api?.sizeColumnsToFit();
  }
};

export const Default: Story = {
  args: {
    data,
    columnDefs,
    gridOptions,
    className: "pt-2"
  }
};
