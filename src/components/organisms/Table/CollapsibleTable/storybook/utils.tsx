/* eslint-disable no-unsafe-optional-chaining */
/* eslint-disable no-param-reassign */
/* eslint-disable import/prefer-default-export */

import { ColDef } from "@ag-grid-community/core";
import { Tooltip } from "@/components/atoms/tooltip";
import { CustomGroupCellRenderer } from "../utils";

// export const columnDefs = (): ColDef[] => [
export const columnDefs = (): ColDef[] => [
  {
    field: "name",
    headerClass: "headerTooltip",
    tooltipField: "key",
    headerComponent: () => (
      <div className="relative w-full h-full">
        <div className="absolute cursor-pointer top-0 right-0">
          <Tooltip
            content="tooltipContent"
            placement="top-end"
            // eslint-disable-next-line react/no-children-prop
            children={
              <div
                data-test="5a17115e-0fa2-4064-a2c4-b61fb78ac5d3"
                className="border-l-[10px] border-l-transparent border-r-[10px] border-solid border-b-transparent border-b-[10px] border-[#545454]"
              />
            }
          />
        </div>
        <div className="w-full h-full flex justify-center items-center">نام</div>
      </div>
    ),
    cellRenderer: (props: any) => (
      <CustomGroupCellRenderer variant={props?.data?.key % 2 === 0 ? "primaryYellow" : "lightGreen"} {...props} />
    )
  },
  {
    field: "key",
    tooltipField: "key"
  },
  { field: "calls", tooltipField: "calls" },
  { field: "minutes", tooltipField: "calls", valueFormatter: (params: any) => `${params.value.toLocaleString()}m` }
];
