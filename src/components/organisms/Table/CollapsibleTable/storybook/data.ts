/* eslint-disable import/prefer-default-export */
export const collapsibleData = [
  {
    key: 177000,
    callRecords: [{ name: "susan", callId: 555, duration: 72, switchCode: "SW3", direction: "Out" }],
    calls: 24,
    minutes: 25.65,
    name: "<PERSON>"
  },
  {
    key: 177001,
    callRecords: [
      { name: "susan", callId: 555, duration: 72, switchCode: "SW3", direction: "Out" },
      { name: "susan", callId: 556, duration: 61, switchCode: "SW3", direction: "In" },
      { name: "susan", callId: 557, duration: 90, switchCode: "SW5", direction: "In" },
      { name: "susan", callId: 558, duration: 90, switchCode: "SW5", direction: "In" },
      { name: "susan", callId: 559, duration: 90, switchCode: "SW5", direction: "In" },
      { name: "susan", callId: 560, duration: 90, switchCode: "SW5", direction: "In" },
      { name: "susan", callId: 561, duration: 90, switchCode: "SW5", direction: "In" },
      { name: "susan", callId: 562, duration: 90, switchCode: "SW5", direction: "In" }
    ],
    calls: 24,
    minutes: 26.216666666666665,
    name: "Mila Smith"
  },
  {
    key: 177002,
    callRecords: [
      { name: "susan", callId: 555, duration: 72, switchCode: "SW3", direction: "Out" },
      { name: "susan", callId: 556, duration: 61, switchCode: "SW3", direction: "In" },
      { name: "susan", callId: 557, duration: 90, switchCode: "SW5", direction: "In" },
      { name: "susan", callId: 558, duration: 90, switchCode: "SW5", direction: "In" },
      { name: "susan", callId: 559, duration: 90, switchCode: "SW5", direction: "In" },
      { name: "susan", callId: 560, duration: 90, switchCode: "SW5", direction: "In" },
      { name: "susan", callId: 561, duration: 90, switchCode: "SW5", direction: "In" },
      { name: "susan", callId: 562, duration: 90, switchCode: "SW5", direction: "In" }
    ],
    calls: 25,
    minutes: 30.633333333333333,
    name: "Evelyn Taylor"
  },
  {
    key: 177003,
    callRecords: [
      { name: "susan", callId: 555, duration: 72, switchCode: "SW3", direction: "Out" },
      { name: "susan", callId: 556, duration: 61, switchCode: "SW3", direction: "In" },
      { name: "susan", callId: 557, duration: 90, switchCode: "SW5", direction: "In" },
      { name: "susan", callId: 558, duration: 90, switchCode: "SW5", direction: "In" },
      { name: "susan", callId: 559, duration: 90, switchCode: "SW5", direction: "In" },
      { name: "susan", callId: 560, duration: 90, switchCode: "SW5", direction: "In" },
      { name: "susan", callId: 561, duration: 90, switchCode: "SW5", direction: "In" },
      { name: "susan", callId: 562, duration: 90, switchCode: "SW5", direction: "In" }
    ],
    calls: 24,
    minutes: 26.483333333333334,
    name: "Harper Johnson"
  },
  {
    key: 177004,
    callRecords: [
      { name: "susan", callId: 555, duration: 72, switchCode: "SW3", direction: "Out" },
      { name: "susan", callId: 556, duration: 61, switchCode: "SW3", direction: "In" },
      { name: "susan", callId: 557, duration: 90, switchCode: "SW5", direction: "In" },
      { name: "susan", callId: 558, duration: 90, switchCode: "SW5", direction: "In" },
      { name: "susan", callId: 559, duration: 90, switchCode: "SW5", direction: "In" },
      { name: "susan", callId: 560, duration: 90, switchCode: "SW5", direction: "In" },
      { name: "susan", callId: 561, duration: 90, switchCode: "SW5", direction: "In" },
      { name: "susan", callId: 562, duration: 90, switchCode: "SW5", direction: "In" }
    ],
    calls: 23,
    minutes: 24.4,
    name: "Addison Wilson"
  }
];
