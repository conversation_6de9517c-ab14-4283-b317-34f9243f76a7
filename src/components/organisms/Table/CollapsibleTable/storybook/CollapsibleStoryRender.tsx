/* eslint-disable no-unsafe-optional-chaining */
/* eslint-disable @typescript-eslint/no-use-before-define */
/* eslint-disable @typescript-eslint/no-shadow */
/* eslint-disable react/no-unstable-nested-components */
/* eslint-disable react/button-has-type */
/* eslint-disable no-nested-ternary */
import routes from "@/constants/routes";

import { CellDoubleClickedEvent, ColDef, IDetailCellRendererParams } from "@ag-grid-community/core";
import { useMemo, useState } from "react";
import { RowClassParams, RowClassRules } from "@ag-grid-community/core/dist/types/src/entities/gridOptions";
import CustomTooltip from "./components/tableTooltip/TableTooltip";
import CollapsibleTable from "../CollapsibleTable";
import { columnDefs } from "./utils";
import { collapsibleData } from "./data";

export const getRowId = (params: { data: { key: string } }) => params.data.key;

function DetailCellRenderer() {
  return <h1 style={{ padding: "20px", color: "white" }}>Loading...</h1>;
}

export interface ICallRecord {
  name: string;
  callId: number;
  duration: number;
  switchCode: string;
  direction: string;
  number: string;
}

export interface IAccount {
  name: string;
  key: number;
  calls: number;
  minutes: number;
  callRecords: ICallRecord[];
}

function CollapsibleStoryRender() {
  const [hoveredIndex, setHoveredIndex] = useState<null | number>(null);
  const isLoading = false;

  const columnDefsData = useMemo(() => columnDefs(), []);

  const defaultColDef = useMemo<ColDef>(
    () => ({
      flex: 1,
      tooltipComponent: CustomTooltip
    }),
    []
  );

  const rowClassRules = {
    "!bg-[#353538]": (params: RowClassParams) =>
      hoveredIndex && params.rowIndex < hoveredIndex + 1 && params.rowIndex % 2 === 0,
    "!bg-[#3B3B3E]": (params: RowClassParams) =>
      hoveredIndex && params.rowIndex < hoveredIndex + 1 && params.rowIndex % 2 === 1,
    "row-line": (params: any) => params?.data?.key === 177002
  } as RowClassRules;

  const detailCellRendererParams = useMemo(
    () =>
      ({
        detailGridOptions: {
          columnDefs: [
            {
              field: "callId"
            },
            {
              field: "direction"
            },
            { field: "duration", valueFormatter: "x.toLocaleString() + 's'" },
            {
              field: "switchCode",
              minWidth: 150
            }
          ]?.reverse(),
          defaultColDef: {
            flex: 1
          },
          rowHeight: 40
        },
        getDetailRowData: params => {
          params.successCallback(params.data.callRecords);
        }
      }) as IDetailCellRendererParams<IAccount, ICallRecord>,
    []
  );

  const tooltipProps = {
    rowClassRules,
    tooltipShowDelay: 0,
    tooltipHideDelay: 2000,
    onCellMouseOver: (e: { rowIndex: number | null }) => {
      setHoveredIndex(e?.rowIndex);
      // If it doesn't work probably, remove next line and use it in useEffect instead of here.
      // gridRef.current?.api?.redrawRows();
    },
    onCellMouseOut: () => {
      setHoveredIndex(null);
    }
  };

  return (
    <div className="flex flex-1 h-screen mt-2 gap-2 ">
      <div className="grow flex flex-col">
        <CollapsibleTable
          {...tooltipProps}
          getRowId={getRowId as any}
          hiddenGroupArrow={false}
          masterDetail
          columnDefs={columnDefsData}
          detailCellRenderer={isLoading ? DetailCellRenderer : null}
          detailCellRendererParams={detailCellRendererParams}
          defaultColDef={defaultColDef}
          data={collapsibleData?.length ? collapsibleData : null}
          onCellDoubleClicked={(cell: CellDoubleClickedEvent<any>) =>
            window.open(`${routes.detail}/${cell.data?.key}`, "_blank")
          }
        />
      </div>
    </div>
  );
}

export default CollapsibleStoryRender;
