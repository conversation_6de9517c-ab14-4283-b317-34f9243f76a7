.tableContainer {
  font-family: yekan-bakh !important;
  width: 100%;

  :global {
    .custom-header-cell-text {
      color: #efefef !important;
      font-size: 14px !important;
    }
    .ag-header-row > :not(:first-child) {
      .ag-header-cell-comp-wrapper {
        justify-content: center;
      }
    }
  }

  --ag-row-hover-color: transparent !important;
  --ag-header-background-color: #343438 !important;
  --ag-background-color: transparent !important;
  --ag-value-change-value-highlight-background-color: #006fff;
  --ag-selected-row-background-color: transparent !important;
  // --ag-icon-font-code-asc: "\f121";
  // --ag-icon-font-code-desc: "\f121";
  --ag-modal-overlay-background-color: transparent;

  --border-variant: unset;

  :global {
    .ag-body-vertical-scroll {
      margin-right: 8px;
    }

    .ag-cell-label-container {
      padding-top: 0px !important;
      margin-bottom: 6px !important;
    }

    .ag-watermark {
      display: none !important;
    }

    .ag-center-cols-container .ag-row-group-contracted.row-line {
      z-index: 2;
      &::before {
        content: " ";
        position: absolute;
        display: block;
        width: 100%;
        height: 2px;
        background-color: #006fff !important;
        bottom: 0 !important;
        // left: 0;
        z-index: 99999;
        right: 10px !important;
        transform: translateY(50%);
      }

      &::after {
        content: "قدرت خرید شما";
        position: absolute;
        display: block;
        font-size: 10px;
        font-weight: 400;
        padding: 2px 10px;
        background-color: #006fff;
        color: white;
        border-radius: 20px;
        bottom: 0;
        right: 5px;
        transform: translateY(50%);
        z-index: 99999;
      }
    }

    /* ----------------------- this is used inside of group ----------------------- */
    .ag-full-width-container[role="rowgroup"] .row-line {
      z-index: 2;
      position: relative;

      &::before {
        content: "";
        position: absolute;
        width: 100%;
        height: 2px;
        background-color: #006fff;
        bottom: 8px;
        left: 0;
        right: 10px;
        z-index: 99999;
      }

      &::after {
        content: "قدرت خرید شما";
        position: absolute;
        font-size: 10px;
        font-weight: 400;
        padding: 2px 10px;
        background-color: #006fff;
        color: white;
        border-radius: 20px;
        bottom: 0px;
        right: 0px;
        z-index: 999999999;
      }
    }

    .ag-row-hover:not(.ag-full-width-row)::before,
    .ag-row-hover.ag-full-width-row.ag-row-group::before,
    .ag-row-hover:not(.ag-full-width-row)::before,
    .ag-row-hover.ag-full-width-row.ag-row-group::before {
      top: unset;
    }

    // .ag-row-focus {
    //   border: 1px solid transparent !important;
    // }

    .ag-cell {
      border-top: unset !important;
      border-bottom: unset !important;
    }

    .ag-body {
      direction: rtl;
    }

    .ag-icon {
      &::after {
        color: white !important;
      }
      &::before {
        color: white !important;
      }
    }

    .ag-body-vertical-scroll {
      min-width: 4px !important;
      width: 4px !important;
      max-width: 4px !important;
      margin-right: 0 !important;
      position: absolute !important;
      left: 0;
      z-index: 9999;
      display: block !important;
    }

    .ag-body-vertical-scroll-viewport {
      min-width: 4px !important;
      width: 4px !important;
      max-width: 4px !important;
    }

    .ag-body-vertical-scroll-container {
      min-width: 4px !important;
      width: 4px !important;
      max-width: 4px !important;
    }

    .ag-body-horizontal-scroll {
      min-height: 4px !important;
      height: 4px !important;
      max-height: 4px !important;
      margin-top: 0 !important;
      position: absolute !important;

      bottom: 0;
      /* .ag-header-cell {
        padding-right: 0 !important;
      } */
    }

    .ag-theme-quartz .ag-layout-auto-height .ag-center-cols-viewport,
    .ag-theme-quartz .ag-layout-auto-height .ag-center-cols-container,
    .ag-theme-quartz .ag-layout-print .ag-center-cols-viewport,
    .ag-theme-quartz .ag-layout-print .ag-center-cols-container,
    .ag-theme-quartz-dark .ag-layout-auto-height .ag-center-cols-viewport,
    .ag-theme-quartz-dark .ag-layout-auto-height .ag-center-cols-container,
    .ag-theme-quartz-dark .ag-layout-print .ag-center-cols-viewport,
    .ag-theme-quartz-dark .ag-layout-print .ag-center-cols-container,
    .ag-theme-quartz-auto-dark .ag-layout-auto-height .ag-center-cols-viewport,
    .ag-theme-quartz-auto-dark .ag-layout-auto-height .ag-center-cols-container,
    .ag-theme-quartz-auto-dark .ag-layout-print .ag-center-cols-viewport,
    .ag-theme-quartz-auto-dark .ag-layout-print .ag-center-cols-container .ag-details-row {
      min-height: 50px !important;
    }

    .ag-body-horizontal-scroll-viewport {
      min-height: 4px !important;
      height: 4px !important;
      max-height: 4px !important;
    }

    .ag-body-horizontal-scroll-container {
      min-height: 4px !important;
      height: 4px !important;
      max-height: 4px !important;
    }

    .ag-body-horizontal-scroll-viewport {
      opacity: 0;
      transition: 0.3s opacity;
    }

    .ag-root:hover .ag-body-horizontal-scroll-viewport {
      opacity: 1;
    }

    /* width */
    .ag-root ::-webkit-scrollbar {
      width: 1px;
      height: 1px;
    }

    .ag-root:hover ::-webkit-scrollbar {
      width: 4px;
      height: 4px;
    }

    *:hover::-webkit-scrollbar-track {
      background-color: rgba(4, 12, 21, 0.8) !important;
    }

    .ag-row-pinned {
      background-color: #343438;
      color: #f4f4f4;
      border: 1px solid transparent;
    }

    .ag-floating-top-container {
      &:last-child {
        border-bottom: 1px solid #545454 !important;
      }
    }

    .ag-header-viewport {
      border-bottom: 1px solid #545454 !important;
      direction: rtl;
    }

    .ag-floating-top {
      border-bottom: 1px solid #545454;
    }

    /* Track */
    .ag-root ::-webkit-scrollbar-track {
      border-radius: 2px;
      background-color: transparent;
    }

    /* Handle */
    .ag-root ::-webkit-scrollbar-thumb {
      border-radius: 2px;
      background-color: #f4f4f4;
    }

    .ag-react-container {
      //width: 100%;
      padding-top: 9px;
      margin-top: 28px;
      //height: calc(100% - 40px);
    }

    .ag-sort-indicator-container {
      & span {
        color: #106fe5 !important;
        font-size: 17px !important;
      }
      & .ag-sort-order {
        display: none;
      }
    }

    .ag-header-cell-label {
      & .ag-header-cell-text {
        color: #efefef !important;
        font-size: 14px;
      }
    }

    .ag-header-cell-centered {
      & .ag-header-cell-text {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        text-align: center !important;
      }
    }
    .ag-header-cell-right {
      padding-right: 10px;
    }
    .ag-header-cell-left {
      & .ag-header-cell-text {
        width: 100%;
        display: flex;
        justify-content: end;
        text-align: left !important;
      }
    }
    /* 
    .ag-header-cell {
      padding-right: 0;
    } */

    .ag-header-cell {
      height: 100% !important;
      padding-left: 4px !important;
      padding-right: 4px !important;
    }

    .ag-details-row {
      padding: 0 !important;
    }

    .ag-details-row {
      & .ag-header {
        min-height: 32px !important;
        height: 32px !important;
      }

      & .ag-header-row {
        min-height: 32px !important;
        height: 32px !important;
      }
    }

    .ag-unselectable {
      // background-color: #343438;
      font-family: yekan-bakh;
      padding: 0 8px 8px 16px;
      .ag-header {
        background-color: #343438;
      }

      .ag-layout-auto-height {
        background-color: transparent !important;
        max-height: 200px;
        overflow-y: auto;
      }

      .ag-row {
        border-bottom: none;
      }

      .ag-row-even {
        background-color: #252529;
      }

      .ag-row-odd {
        background-color: #1f1f22;
      }
    }

    .ag-center-cols-viewport {
      min-height: 50px;
      overflow: visible;
    }

    .ag-details-row {
      .ag-unselectable {
        .ag-header {
          border-bottom: 1px solid #b9b4ab;
        }

        .ag-center-cols-container {
          & > .ag-row {
            height: 40px !important;
            border-bottom: 1px solid #545454;
          }
        }
      }
    }

    .ag-theme-alpine {
      min-width: 100% !important;
      position: relative;
      background-color: transparent !important;
    }

    .ag-header-viewport {
      background-color: transparent !important;
    }

    .ag-overlay-loading-wrapper {
      background-color: rgb(0 0 15) !important;
    }

    .ag-root-wrapper {
      border: none !important;
    }

    .ag-theme-alpine {
      --ag-grid-size: 10px !important;
      --ag-list-item-height: 120px !important;
    }

    .ag-header {
      border-bottom: 1px solid #545454;
      border-top-right-radius: 4px;
      border-top-left-radius: 4px;
      // background-color: transparent !important;
      min-height: 41px !important;
      height: 41px !important;

      & span {
        font-size: 12px;
        font-weight: 400;
      }
    }

    .ag-header-row {
      background-color: #343438;

      & * {
        color: #efefef;
      }

      height: 48px !important;
      min-height: 48px !important;
      & .ag-header-cell:first-child {
        padding-right: 12px !important;
      }
    }

    .ag-header-row-column {
      width: 100% !important;
      z-index: 2;
    }

    .ag-root-wrapper-body,
    .ag-layout-normal {
      // background-color: #1f1f22 !important;
      background-color: transparent !important;
    }

    .ag-sticky-top {
      & .ag-cell-value {
        color: white;
      }

      margin-top: -1px;
    }

    .ag-center-cols-container {
      gap: 10px !important;

      & > .ag-row {
        margin-bottom: 8px !important;
        cursor: pointer;
        border-color: transparent;
        border-right: 2px solid var(--border-variant);
        border-bottom: none;
        color: #f4f4f4;

        &:nth-child(odd) {
          background-color: #1f1f22;
          .ag-root-wrapper-body,
          .ag-layout-normal {
            background-color: #1f1f22 !important;
          }
        }

        &:nth-child(even) {
          background-color: #252529;
          .ag-root-wrapper-body,
          .ag-layout-normal {
            background-color: #1f1f22 !important;
          }
        }
      }

      .ag-row-selected {
        border: 2px solid #0a84ff !important;

        .ag-selected-icon {
          @apply border border-primary;
        }

        .ag-selected-bullet {
          @apply h-[7px] min-w-[7px] rounded-[2.5px] bg-primary;
        }
      }
    }

    .ag-cell {
      display: flex;
      align-items: center;
      border-top: unset;
      border-bottom: unset;
    }

    .ag-cell-focus,
    .ag-cell-no-focus {
      border-color: transparent !important;
    }

    .no-border.ag-cell:focus {
      border-color: transparent !important;
      outline: none;
    }
  }
}

.disableHeader {
  :global {
    .ag-header-cell {
      pointer-events: none !important;
    }

    .ag-sort-indicator-container span {
      color: #676767 !important;
    }

    .sort-selected-icon {
      filter: grayscale(1);
    }
  }
}

.hiddenGroupArrow {
  :global {
    .ag-group-contracted {
      display: none !important;
    }
  }
}

.headerContainer {
  :global {
    .ag-header-row > :not(:first-child) {
      .ag-header-cell-label {
        justify-content: center;
      }
    }
  }
}
