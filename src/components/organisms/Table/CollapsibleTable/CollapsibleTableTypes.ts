import { IMenuItem, IPosition } from "@/components/molecules/menu";
import { CellContextMenuEvent } from "@ag-grid-community/core";
import { AgGridReact, AgGridReactProps } from "@ag-grid-community/react";
import { LegacyRef } from "react";

export const colors = {
  primaryYellow: "#F1C21B",
  lightGreen: "#71DB88",
  primaryGreen: "#00A216",
  lightRed: "#FF5E5E",
  primaryRed: "#C11414"
};

export interface ITable<T> extends AgGridReactProps<T> {
  data: T[] | null;
  menuItems?: IMenuItem<T>[];
  hiddenGroupArrow?: boolean;
  gridRef?: LegacyRef<AgGridReact<T>> | undefined;
  headerCenter?: boolean;
  sumValues?: boolean;
}

export type TOnCellContextEvent<T> = CellContextMenuEvent<T>["event"] & { x: number; y: number };

export interface IMenuOption<T> {
  isOpen: boolean;
  position: IPosition;
  rowData?: T;
}

export interface ICustomGroupCellRendererProps {
  variant: keyof typeof colors;
  [x: string]: any;
}
