"use client";

/* eslint-disable import/no-extraneous-dependencies */
import "@ag-grid-community/styles/ag-grid.css";
import "@ag-grid-community/styles/ag-theme-quartz.css";

import { AgGridReact } from "@ag-grid-community/react";
import { twMerge } from "tailwind-merge";
import { forwardRef, Ref, startTransition, useEffect, useImperativeHandle, useRef, useState } from "react";
import {
  renderCoupon,
  renderYtm,
  renderYtmPrice,
  renderDate,
  renderTransactionTime,
  getRowId,
  renderFloatNumber
} from "@/components/organisms/Table/utils";
import { Menu } from "@/components/molecules/menu";
import { CellContextMenuEvent, ModuleRegistry } from "@ag-grid-community/core";
import { ClientSideRowModelModule } from "@ag-grid-community/client-side-row-model";
import { ColumnsToolPanelModule } from "@ag-grid-enterprise/column-tool-panel";
import { MasterDetailModule } from "@ag-grid-enterprise/master-detail";

import { RowClassParams, RowClassRules } from "@ag-grid-community/core/dist/types/src/entities/gridOptions";
import { throttle } from "lodash";
import { IMenuOption, ITable, TOnCellContextEvent } from "./CollapsibleTableTypes";
import styles from "./CollapsibleTable.module.scss";

ModuleRegistry.registerModules([ClientSideRowModelModule, ColumnsToolPanelModule, MasterDetailModule]);

export interface ICollapsibleTableRefProps {
  closeAll(): void;
}

function CollapsibleTable<T>(
  {
    data,
    gridOptions,
    columnDefs,
    rowHeight,
    hiddenGroupArrow = true,
    className,
    menuItems,
    headerCenter,
    sumValues = true,
    ...restProps
  }: ITable<T>,
  ref: Ref<ICollapsibleTableRefProps>
) {
  const [menuOption, setMenuOption] = useState<IMenuOption<T>>({
    isOpen: false,
    rowData: undefined,
    position: { x: 0, y: 0 }
  });
  const indexRef = useRef<number>(0);
  const gridRef = useRef<AgGridReact | null>(null);

  /* ------------------- set position of right click popover ------------------ */
  const onCellContextMenu = (e: CellContextMenuEvent<T>) => {
    const event = e.event as TOnCellContextEvent<T>;

    setMenuOption(prev => ({
      ...prev,
      isOpen: true,
      rowData: e.data,
      position: { x: event?.x, y: event?.y }
    }));
  };

  function onHover(e: { rowIndex: number }) {
    if (indexRef.current !== e?.rowIndex && sumValues) {
      startTransition(() => {
        gridRef.current?.api?.updateGridOptions({
          rowClassRules: {
            "!bg-[#353538]": (params: RowClassParams) =>
              e?.rowIndex && params.rowIndex < Number(e?.rowIndex) + 1 && params.rowIndex % 2 === 0,
            "!bg-[#3B3B3E]": (params: RowClassParams) =>
              e?.rowIndex && params.rowIndex < Number(e?.rowIndex) + 1 && params.rowIndex % 2 === 1,
            ...restProps?.rowClassRules
          } as RowClassRules
        });
      });
    }
    indexRef.current = e?.rowIndex;
  }

  function onMouseOut() {
    gridRef.current?.api?.updateGridOptions({
      rowClassRules: restProps?.rowClassRules as RowClassRules
    });
  }

  const tooltipProps = {
    tooltipShowDelay: 0,
    tooltipHideDelay: 12000,
    gridRef,
    onCellMouseOver: throttle(e => {
      onHover(e);
    }, 30)
  };

  useEffect(() => {
    const handleContextMenu = (event: MouseEvent) => {
      if ((event.target as HTMLElement).closest(".ag-row-pinned")) {
        event.preventDefault();
      }
    };

    document.addEventListener("contextmenu", handleContextMenu);

    return () => {
      document.removeEventListener("contextmenu", handleContextMenu);
    };
  }, []);

  useImperativeHandle(ref, () => ({
    closeAll() {
      gridRef.current!.api?.forEachNode(node => {
        // eslint-disable-next-line no-param-reassign
        node.expanded = false;
      });

      gridRef.current!.api?.onGroupExpandedOrCollapsed();
    }
  }));

  const [expandedRowId, setExpandedRowId] = useState<string | null>(null);

  /* ------- This function is triggered when a row is expanded/collapsed ------ */
  const onRowGroupOpened = (event: any) => {
    const clickedRowId = event.node?.data?.key; // Unique ID for the row

    /* ----------- If the clicked row is already expanded, do nothing ----------- */
    if (expandedRowId === clickedRowId) {
      return;
    }

    setExpandedRowId(clickedRowId);

    /* ------------- Collapse all other rows except the one clicked ------------- */
    event.api.forEachNode((node: any) => {
      if (node.data?.key !== clickedRowId) {
        // eslint-disable-next-line no-param-reassign
        node.expanded = false;
      } else {
        // eslint-disable-next-line no-param-reassign
        node.expanded = true;
      }
    });
  };

  return (
    <div onPointerLeave={onMouseOut} className="w-full h-full">
      <AgGridReact<T>
        {...tooltipProps}
        animateRows
        enableRtl
        icons={{
          sortAscending: `<img src="/sort-selected.svg" class="sort-selected-icon" alt="svg"/>`,
          sortDescending: `<img src="/sort-selected.svg" class="sort-selected-icon" alt="svg"/>`
        }}
        onRowGroupOpened={onRowGroupOpened}
        detailRowAutoHeight
        preventDefaultOnContextMenu
        onCellContextMenu={onCellContextMenu}
        // enableCellChangeFlash
        suppressColumnVirtualisation
        rowHeight={rowHeight || 64}
        getRowId={restProps?.getRowId || (getRowId as any)}
        rowData={data}
        gridOptions={gridOptions}
        columnDefs={columnDefs}
        components={{
          renderYtm,
          renderYtmPrice,
          renderCoupon,
          renderDate,
          renderTransactionTime,
          renderFloatNumber
        }}
        ref={gridRef}
        className={twMerge(
          "ag-theme-quartz",
          styles.tableContainer,
          !data?.length && styles.disableHeader,
          hiddenGroupArrow && styles.hiddenGroupArrow,
          headerCenter && styles.headerContainer,
          className
        )}
        {...restProps}
      />
      {!!menuItems?.length && (
        <Menu
          {...menuOption}
          items={menuItems}
          onClose={() =>
            setMenuOption(prev => ({
              ...prev,
              isOpen: false
            }))
          }
        />
      )}
    </div>
  );
}

const ForwardedCollapsibleTable = forwardRef(CollapsibleTable) as <T>(
  props: ITable<T> & { ref?: Ref<ICollapsibleTableRefProps> }
) => React.ReactElement;

export default ForwardedCollapsibleTable;
