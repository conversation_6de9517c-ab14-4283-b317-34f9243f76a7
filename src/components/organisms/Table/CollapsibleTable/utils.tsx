/* eslint-disable no-unsafe-optional-chaining */
/* eslint-disable no-param-reassign */
/* eslint-disable import/prefer-default-export */
import { twMerge } from "tailwind-merge";

import { ICustomGroupCellRendererProps, colors } from "./CollapsibleTableTypes";

export function CustomGroupCellRenderer({ variant = "primaryRed", ...restProps }: ICustomGroupCellRendererProps) {
  const AgGroupCellRenderer = restProps?.api?.frameworkOverrides?.frameworkComponents?.agGroupCellRenderer;
  const backgroundColor = colors[variant];

  const id = restProps?.data?.key ? restProps?.data?.key : restProps?.data?.callRecords?.[0]?.key;

  return (
    <>
      <div
        className={twMerge("h-[70px] w-1   absolute z-50 -top-1 right-0 -mb-2.5")}
        style={{
          background: backgroundColor
        }}
      />
      <style>
        {`
        div[row-id="detail_${id}"] {
          border-right: 4px solid ${backgroundColor};
          margin-right:1px;
        }
        `}
      </style>

      <AgGroupCellRenderer {...restProps} />
    </>
  );
}
