.tableContainer {
  font-family: "yekan-bakh" !important;
  width: 100%;

  --ag-row-hover-color: transparent !important;
  --ag-header-background-color: #35353a !important;
  --ag-background-color: transparent !important;
  --ag-value-change-value-highlight-background-color: #006fff !important;
  --ag-selected-row-background-color: transparent !important;
  --ag-icon-font-code-asc: "\f121";
  --ag-icon-font-code-desc: "\f121";
  --ag-modal-overlay-background-color: transparent;

  --border-variant: unset;

  :global {
    .ag-body-vertical-scroll {
      margin-right: 8px;
    }

    .ag-body-vertical-scroll {
      min-width: 4px !important;
      width: 4px !important;
      max-width: 4px !important;
      margin-right: 0 !important;
      position: absolute !important;
      left: 0;
    }

    .ag-body-vertical-scroll-viewport {
      min-width: 4px !important;
      width: 4px !important;
      max-width: 4px !important;
    }

    .ag-body-vertical-scroll-container {
      min-width: 4px !important;
      width: 4px !important;
      max-width: 4px !important;
    }

    .ag-body-horizontal-scroll {
      min-height: 4px !important;
      height: 4px !important;
      max-height: 4px !important;
      margin-top: 0 !important;
      position: absolute !important;
      bottom: 0;
      /* .ag-header-cell {
        padding-right: 0 !important;
      } */
    }

    .ag-body-horizontal-scroll-viewport {
      min-height: 4px !important;
      height: 4px !important;
      max-height: 4px !important;
    }

    .ag-body-horizontal-scroll-container {
      min-height: 4px !important;
      height: 4px !important;
      max-height: 4px !important;
    }

    .ag-body-horizontal-scroll-viewport {
      opacity: 0;
      transition: 0.3s opacity;
    }

    .ag-root:hover .ag-body-horizontal-scroll-viewport {
      opacity: 1;
    }

    /* width */
    .ag-root ::-webkit-scrollbar {
      width: 1px;
      height: 1px;
    }

    .ag-root:hover ::-webkit-scrollbar {
      width: 4px;
      height: 4px;
    }

    *:hover::-webkit-scrollbar-track {
      background-color: rgba(4, 12, 21, 0.8) !important;
    }

    .ag-row-pinned {
      background-color: #343438;
      color: #f4f4f4;
      border: 1px solid transparent;
    }

    .ag-floating-top-container {
      &:last-child {
        border-bottom: 1px solid #545454 !important;
      }
    }

    .ag-header-viewport {
      border-bottom: 1px solid #545454 !important;
    }

    .ag-floating-top {
      border-bottom: 1px solid #545454;
    }

    /* Track */
    .ag-root ::-webkit-scrollbar-track {
      border-radius: 2px;
      background-color: transparent;
    }

    /* Handle */
    .ag-root ::-webkit-scrollbar-thumb {
      border-radius: 2px;
      background-color: #f4f4f4;
    }

    .ag-react-container {
      width: 100%;
      padding-top: 9px;
      margin-top: 28px;
      height: calc(100% - 40px);
    }

    .ag-sort-indicator-container {
      & span {
        color: #106fe5 !important;
        font-size: 17px !important;
      }
      & .ag-sort-order {
        display: none;
      }

      & .ag-sort-indicator-icon {
        padding-right: 1px;
        margin-bottom: 8px !important;
        width: 12px;
        height: 12px;
      }
    }

    .ag-header-cell-label {
      height: 24px !important;
      & .ag-header-cell-text {
        color: #efefef !important;
        font-size: 14px;
      }
    }

    .ag-cell-label-container {
      padding-top: 0px !important;
      margin-bottom: 6px !important;
      height: 24px;
    }

    .ag-header-cell-centered {
      & .ag-header-cell-text {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        text-align: center !important;
      }
    }
    .ag-header-cell-right {
      padding-right: 10px;
    }
    .ag-header-cell-left {
      & .ag-header-cell-text {
        width: 100%;
        display: flex;
        justify-content: end;
        text-align: left !important;
      }
    }

    .headerTooltip {
      padding-right: 0;
    }

    .ag-theme-alpine {
      min-width: 100% !important;
      position: relative;
      background-color: transparent !important;
    }

    .ag-header-viewport {
      background-color: transparent !important;
    }

    .ag-overlay-loading-wrapper {
      background-color: rgb(0 0 15) !important;
    }

    .ag-root-wrapper {
      border: none !important;
    }

    .ag-theme-alpine {
      --ag-grid-size: 10px !important;
      --ag-list-item-height: 120px !important;
    }

    .ag-header {
      border-bottom: 1px solid #35353a;
      border-top-right-radius: 4px;
      border-top-left-radius: 4px;
      // background-color: transparent !important;
      min-height: 41px !important;
      height: 41px !important;

      & span {
        font-size: 12px;
        font-weight: 400;
      }
    }

    .ag-header-row {
      background-color: #35353a;

      & * {
        color: #efefef;
      }
      & img.sort-selected-icon {
        max-width: initial;
        position: relative;
        top: 4px;
        right: 3px;
        transform: scale(1.2);
      }

      height: 48px !important;
      min-height: 48px !important;

      & .ag-header-cell:not(:first-child) {
        padding-right: 22px !important;
      }
      & .ag-header-cell:first-child {
        padding-right: 15px !important;
      }

      & .ag-header-cell {
        padding-left: 0px !important;
      }
    }

    .ag-header-row-column {
      width: 100% !important;
      z-index: 2;
      background-color: var(--header-color) !important;
    }

    .ag-center-cols-container {
      gap: 10px !important;

      & > .ag-row {
        margin-bottom: 8px !important;
        cursor: pointer;
        border-color: transparent;
        border-right: 2px solid var(--border-variant);
        border-bottom: none;
        color: #f4f4f4;

        &:nth-child(odd) {
          background-color: #1f1f22 !important;
          &:hover {
            background-color: #152137 !important;
          }
        }

        &:nth-child(even) {
          background-color: #252529 !important;
          &:hover {
            background-color: #152137 !important;
          }
        }
      }

      .ag-row-selected {
        border: 2px solid #0a84ff !important;

        .ag-selected-icon {
          @apply border border-primary;
        }

        .ag-selected-bullet {
          @apply h-[7px] min-w-[7px] rounded-[2.5px] bg-primary;
        }
      }
    }

    .ag-cell {
      display: flex;
      align-items: center;
    }

    .ag-cell-focus,
    .ag-cell-no-focus {
      border-color: transparent !important;
    }

    .no-border.ag-cell:focus {
      border-color: transparent !important;
      outline: none;
    }
  }
}

.disableHeader {
  :global {
    .ag-header-cell {
      pointer-events: none !important;
    }

    .ag-sort-indicator-container span {
      color: #676767 !important;
    }

    .sort-selected-icon {
      filter: grayscale(1);
    }
  }
}

.tableLoading {
  :global {
    .ag-root ::-webkit-scrollbar-thumb {
      border-radius: 2px;
      background-color: transparent;
      display: none;
    }

    .ag-root ::-webkit-scrollbar-track {
      display: none;
    }
  }
}
