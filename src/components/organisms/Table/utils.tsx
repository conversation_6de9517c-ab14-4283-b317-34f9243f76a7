import Tick from "@/assets/icons/Check.svg";
import Close from "@/assets/icons/Close.svg";
import { numberWithCommas } from "@/utils/helpers";
import { dateConverter } from "@/utils/DateHelper";

import { Spinner } from "@/components/atoms/spinner";

interface ICustomNoRowsProps {
  noRowsTitle: string;
}

interface ICustomNetworkErrorProps {
  errorTitle: string;
}

export const getRowId = (params: { data: { isin: string } }) => params.data.isin;
export const getRowIndex = (params: { data: { index: number } }) => params.data.index?.toString();

export const renderYtm = ({ value }: { value: number | string }) => (
  <span>
    {value ? (
      <span>
        {Number(value) < 0
          ? `${Math.abs(Number(Number(value) * 100))?.toFixed(3)}-`
          : (Number(value) * 100)?.toFixed(3)}
        <span className="text-.9xs text-dark-text/70"> ٪ </span>
      </span>
    ) : (
      `-`
    )}
  </span>
);

export const renderYtmPrice = ({ value }: { value: number }) => (
  <span>{value ? <span className="mr-2">{numberWithCommas(value)}</span> : <span className="mr-2">---</span>}</span>
);

export const renderFloatNumber = ({ value }: { value: number }) => (
  <span>{value ? <span className="mr-2">{value?.toFixed(3)}</span> : <span className="mr-2">---</span>}</span>
);

export const renderCoupon = ({ value }: { value: number }) => (
  <div data-test="b05d415e-78c3-4446-9012-20152dcbf3f6">
    {value && value === 100 ? (
      <Tick className=" w-6 h-6 text-semanticWithCoupon" />
    ) : (
      <Close className=" w-6 h-6 text-semanticWithoutCoupon" />
    )}
    {!value && <span className="mr-2">---</span>}
  </div>
);

export const renderDate = ({ value }: { value: string }) => (
  <span className="mr-2">{value ? dateConverter(value).format("YYYY/MM/DD") : ""}</span>
);

export const renderTransactionTime = ({ value }: { value: string }) => (
  <div className=" text-base mt-1 mr-2">
    {value ? dateConverter(value).format("HH:MM:ss") : ""} <br />
    <p className=" text-[10px]">{value ? dateConverter(value).format("YYYY/MM/DD") : ""}</p>
  </div>
);

export function CustomNoRows({ noRowsTitle }: ICustomNoRowsProps) {
  return <div className="flex items-center justify-center text-white h-full">{noRowsTitle}</div>;
}

export function CustomNetworkError({ errorTitle }: ICustomNetworkErrorProps) {
  return <div className="flex items-center justify-center text-white h-full"> {errorTitle}</div>;
}

export function CustomLoadingOverlay() {
  return (
    <div className="flex items-center justify-center h-full   ">
      <Spinner svgClassName="text-white" />
    </div>
  );
}
