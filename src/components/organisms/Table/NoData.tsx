import { GridOptions } from "ag-grid-community";
import React from "react";
import { CustomNetworkError, CustomNoRows } from "./utils";
import Table from "./Table";

interface INoDataProps {
  columnDefs: any;
  isError?: boolean;
  noRowsTitle: string;
  errorTitle: string;
  headerColor?: string;
}

function NoData({ columnDefs, isError, noRowsTitle, errorTitle, headerColor }: INoDataProps) {
  const gridOptions: GridOptions = {
    enableRtl: true,
    sortingOrder: ["asc", "desc"]
  };

  return (
    <Table
      headerColor={headerColor}
      noRowsOverlayComponent={() => (isError ? CustomNetworkError({ errorTitle }) : CustomNoRows({ noRowsTitle }))}
      columnDefs={columnDefs}
      gridOptions={gridOptions}
      data={[]}
    />
  );
}

export default NoData;
