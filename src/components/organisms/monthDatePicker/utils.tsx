/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import CloseIcon from "@/assets/icons/close-white.svg";
import ArrowLeft from "@/assets/icons/datePicker-arrowLeft.svg";
import ArrowRight from "@/assets/icons/datePicker-arrowRight.svg";
import DatePickerIcon from "@/assets/icons/datePickerIcon.svg";
import Button from "@/components/atoms/button";
import { Select } from "@/components/atoms/select";
import { dateConverter } from "@/utils/DateHelper";
import "headless-react-datepicker/dist/styles.css";

import { lightSelectStyles, selectStyles } from "./styles";
import { IFooterProps, IMonthPickerProps, ITheme } from "./types";

interface ITitleProps {
  onClose: () => void;
  theme: ITheme;
}

export function Title({ onClose, theme }: ITitleProps) {
  return (
    <div className="flex items-center justify-between border-b border-b-[#545454] pt-[11px] pb-[9px]  mx-3">
      <div className="flex items-center gap-1">
        <DatePickerIcon className="text-[#F4F4F4] h-5 w-5" style={{ color: theme.color }} />
        <span className="text-xs">انتخاب تاریخ</span>
      </div>
      <CloseIcon onClick={onClose} className="cursor-pointer mb-2" />
    </div>
  );
}

export function Footer({ disabled, onCancel, onConfirm }: IFooterProps) {
  return (
    <div className="flex items-center justify-between mt-0 gap-2 mx-3 mb-3">
      <Button size="small" className="w-full " variant="outLineWhite" onClick={onCancel}>
        پاک کردن
      </Button>
      <Button size="small" disabled={disabled} className="w-full" variant="fill" onClick={onConfirm}>
        تایید
      </Button>
    </div>
  );
}

interface IHeaderProps {
  yearValue?: number;
  onChangeYear?: (v: number) => void;
  theme: ITheme;
  variant: IMonthPickerProps["variant"];
}

export function Header({ yearValue, onChangeYear, theme, variant }: IHeaderProps) {
  const jalaliDate = dateConverter(new Date()?.toISOString());
  const currentYear = (jalaliDate ? +jalaliDate.format("YYYY") : new Date().getFullYear()) + 5;
  const prevTwentyYear = currentYear - 20;

  const yearsList = Array.from({ length: currentYear - prevTwentyYear + 1 }, (_, i) => prevTwentyYear + i);
  const yearInTheCalendar = yearValue || currentYear;

  const goToNextYear = () => {
    const currentYearIndex = yearsList?.findIndex(item => item === yearInTheCalendar);

    if (!currentYearIndex || currentYearIndex === -1) return;

    const nextYearIndex = yearsList?.[currentYearIndex + 1];
    if (nextYearIndex) onChangeYear?.(nextYearIndex);
  };

  const goToPrevYear = () => {
    const currentYearIndex = yearsList?.findIndex(item => item === yearInTheCalendar);
    if (!currentYearIndex || currentYearIndex === -1) return;

    const nextYearIndex = yearsList?.[currentYearIndex - 1];
    if (nextYearIndex) onChangeYear?.(nextYearIndex);
  };

  return (
    <div className="flex items-center justify-between  mt-2 mb-4 mx-2">
      <div
        className="flex items-center select-none"
        onClick={goToPrevYear}
        data-testid="c6bfe9a7-aa3d-45d4-a6ec-9e39364832ce"
      >
        <ArrowRight style={{ color: theme.color }} />
        <span className="text-[10px] text-[#BDBDBD] cursor-pointer" style={{ color: theme.color }}>
          سال قبلی
        </span>
      </div>
      <div className="flex items-center gap-1 text-sm" style={{ color: theme.color }}>
        <Select
          className="mt-0.5 block h-auto w-full"
          // inputWrapperProps={{
          //   // Todo:
          //   // fix className in select component
          //   className: "!mb-0 [&>div]:!border [&>div]:!border-secondaryBorder [&>div]:!rounded-lg"
          // }}
          styles={variant === "dark" ? selectStyles : lightSelectStyles}
          onChange={(val: any) => onChangeYear?.(val.value)}
          items={yearsList?.map(item => ({ label: item.toString(), value: item })) || []}
        >
          <span className=" !text-sm" style={{ color: theme.color }}>
            {yearInTheCalendar}
          </span>
        </Select>
      </div>
      <div
        className="flex items-center cursor-pointer select-none"
        onClick={goToNextYear}
        data-testid="date-picker-title-856a9db8-8f73-4cc7-923b-5867f8b6c98e"
      >
        <span className="text-[10px] text-[#BDBDBD]" style={{ color: theme.color }}>
          سال بعدی
        </span>
        <ArrowLeft style={{ color: theme.color }} />
      </div>{" "}
    </div>
  );
}

export const addLeadingZero = (num: number) => (num < 10 ? `0${num}` : num.toString());
