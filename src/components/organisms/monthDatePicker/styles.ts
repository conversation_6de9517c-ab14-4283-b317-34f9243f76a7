/* eslint-disable no-nested-ternary */
import { ISelectItems } from "@/components/atoms/select";
import { StylesConfig } from "react-select";

export const selectStyles: StylesConfig<ISelectItems<any>, boolean> = {
  control: styles => ({
    ...styles,
    width: "100%",
    minHeight: "32px",
    borderRadius: "8px",
    border: "1px solid #F4F4F4",
    boxShadow: "none",
    backgroundColor: "transparent",
    color: "#3f3c3c",
    fontSize: "14px",
    cursor: "pointer"
  }),
  menu: styles => ({
    ...styles,
    zIndex: 20,
    minWidth: "82px",
    fontSize: "10px",
    marginTop: "0",
    background: "#343438",
    borderRadius: "4px",
    border: "1px solid #545454",
    boxShadow: "0px 0px 20px 2px rgba(0, 0, 0, 0.25)"
  }),
  menuList: styles => ({
    ...styles,
    padding: "0 6px"
  }),
  container: styles => ({
    ...styles,
    "& *": {
      color: "white !important"
    }
  }),
  input: styles => ({
    ...styles,
    color: "#3f3c3c"
  }),
  option: (styles, state) => ({
    ...styles,
    fontWeight: 400,
    fontSize: "10px",
    textAlign: "right",
    padding: "11px 6px 0px 6px",
    color: "#BDBDBD !important",
    borderBottom: "1px solid #545454",
    background: state?.isSelected ? "transparent" : "unset",
    "&:hover": {
      cursor: "pointer",
      background: "transparent"
    },
    "&:active": {
      background: "transparent"
    },
    "&:focus": {
      background: "transparent"
    }
  })
};

export const lightSelectStyles: StylesConfig<ISelectItems<any>, boolean> = {
  control: styles => ({
    ...styles,
    width: "100%",
    minHeight: "32px",
    borderRadius: "8px",
    border: "1px solid #F4F4F4",
    boxShadow: "none",
    backgroundColor: "transparent",
    color: "#3f3c3c",
    fontSize: "14px",
    cursor: "pointer"
  }),
  menu: styles => ({
    ...styles,
    zIndex: 20,
    minWidth: "82px",
    fontSize: "10px",
    marginTop: "0",
    background: "#EFEFEF",
    borderRadius: "4px",
    border: "1px solid #BDBDBD",
    boxShadow: "0px 0px 20px 2px rgba(0, 0, 0, 0.25)"
  }),
  menuList: styles => ({
    ...styles,
    padding: "0 6px"
  }),
  container: styles => ({
    ...styles,
    "& *": {
      color: "#3f3c3c !important"
    }
  }),
  input: styles => ({
    ...styles,
    color: "#3f3c3c"
  }),
  option: (styles, state) => ({
    ...styles,
    fontWeight: 400,
    fontSize: "10px",
    textAlign: "right",
    padding: "11px 6px 0px 6px",
    color: "#3f3c3c !important",
    borderBottom: "1px solid #BDBDBD",
    background: state?.isSelected ? "transparent" : "unset",
    "&:hover": {
      cursor: "pointer",
      background: "transparent"
    },
    "&:active": {
      background: "transparent"
    },
    "&:focus": {
      background: "transparent"
    }
  })
};
