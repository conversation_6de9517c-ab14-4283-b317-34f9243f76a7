import { PopOver } from "@/components/atoms/popper";
import RangeMonthPicker from "@/components/organisms/monthDatePicker/RangeMonthPicker";
import { Ref, forwardRef, useImperativeHandle, useState } from "react";
import { IRangeDatePickerWrapperProps, IDatePickerWrapperRefProps } from "./types";

function RangeMonthPickerWrapper(
  { children, placement = "bottom-start", ...restProps }: IRangeDatePickerWrapperProps,
  ref: Ref<IDatePickerWrapperRefProps> // Ref should be of type DatePickerWrapperRef
) {
  const [isOpen, setIsOpen] = useState(false);

  const toggleOpen = (v: any) => setIsOpen(v);

  useImperativeHandle(ref, () => ({
    close() {
      setIsOpen(false);
    },
    open() {
      setIsOpen(true);
    }
  }));

  return (
    <PopOver
      setIsOpen={toggleOpen}
      isOpen={isOpen}
      placement={placement}
      content={<RangeMonthPicker setIsOpen={toggleOpen} {...restProps} />}
    >
      {children}
    </PopOver>
  );
}

const ForwardedDatePickerWrapper = forwardRef(RangeMonthPickerWrapper) as (
  props: IRangeDatePickerWrapperProps & { ref?: Ref<IDatePickerWrapperRefProps> }
) => React.ReactElement;

export default ForwardedDatePickerWrapper;
