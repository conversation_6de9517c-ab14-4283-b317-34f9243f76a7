import persianMonth from "@/utils/date";
import { dateConverter } from "@/utils/DateHelper";
import { twMerge } from "tailwind-merge";
import { ITheme } from "./types";
import { addLeadingZero } from "./utils";

type MonthValue = string;

interface IMonthsProps {
  monthValue: MonthValue;
  onchangeMonth: (v: MonthValue) => void;
  theme: ITheme;
}

export function Months({ monthValue, onchangeMonth, theme }: IMonthsProps) {
  const monthsList = Object.keys(persianMonth)?.map(key => ({
    label: persianMonth?.[+key],
    value: addLeadingZero(+key)
  }));
  const currentMonth = dateConverter(new Date()?.toISOString()).locale("en")?.format("MM");

  return (
    <div className="grid grid-cols-3 justify-between w-full gap-2 px-1">
      {monthsList?.map(item => (
        <div
          key={item?.value}
          onClick={() => onchangeMonth?.(item?.value)}
          style={{
            background: monthValue === item?.value ? theme.activeBgColor : "",
            color: monthValue === item?.value ? theme.activeColor : "",
            borderColor: currentMonth === item?.value ? theme.borderColor : "transparent"
          }}
          className={twMerge(
            "px-2 py-1.5 text-center w-full cursor-pointer rounded border border-transparent text-sm"
            // monthValue === item?.value ? "bg-[#106FE5] border-transparent" : "",
            // currentMonth === item?.value ? "border-[#EFEFEF]" : ""
          )}
        >
          {item?.label}
        </div>
      ))}
    </div>
  );
}
