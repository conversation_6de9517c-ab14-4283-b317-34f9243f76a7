import { dateConverter } from "@/utils/DateHelper";
import { useEffect, useState } from "react";

import { IMonthPickerProps } from "./types";
import { Header, Title } from "./utils";

import "headless-react-datepicker/dist/styles.css";
import "./DatePicker.scss";
import { Months } from "./Months";

export default function MonthPicker({ initialValue, setIsOpen, variant = "dark", onchange }: IMonthPickerProps) {
  // eslint-disable-next-line no-unsafe-optional-chaining
  const currentYear = +dateConverter(new Date()?.toISOString()).locale("en")?.format("YYYY");

  const [value, setValue] = useState(initialValue);

  useEffect(() => {
    setValue(initialValue);
  }, [initialValue]);

  const year = value?.toString()?.slice(0, 4) ?? currentYear;
  const month = value?.toString()?.slice(4, 6) ?? "";

  const lightTheme = {
    bgColor: "#EFEFEF",
    color: "#3f3c3c",
    borderColor: "#BDBDBD",
    activeBgColor: "#1477FF",
    activeColor: "#EFEFEF"
  };

  const darkTheme = {
    bgColor: "#343438",
    color: "#F4F4F4",
    borderColor: "#B9B4AB",
    activeBgColor: "#1477FF",
    activeColor: "#EFEFEF"
  };

  const theme = variant === "dark" ? darkTheme : lightTheme;

  return (
    <div
      style={{
        background: theme.bgColor,
        color: theme.color,
        border: `0.5px solid ${theme.borderColor}`,
        width: "238px",
        borderRadius: "8px",
        margin: "0 auto",
        minHeight: "254px"
      }}
    >
      <Title
        theme={theme}
        onClose={() => {
          setIsOpen?.(false);
        }}
      />
      <Header
        theme={theme}
        yearValue={+year}
        onChangeYear={v => {
          setValue(v + month);

          onchange?.(v + month);
        }}
        variant={variant}
      />
      <Months
        theme={theme}
        monthValue={month}
        onchangeMonth={v => {
          setValue(year + v);

          onchange?.(year + v);
          setIsOpen?.(false);
        }}
      />
    </div>
  );
}
