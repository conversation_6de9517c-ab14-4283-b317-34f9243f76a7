import Date from "@/assets/icons/calendar2.svg";
import { useDescription, useTsController } from "@ts-react/form";
import { MonthPickerWrapper } from "@/components/organisms/monthDatePicker";
import Input from "@/components/atoms/input";
import { TInputProps } from "@/components/atoms/input/types";
import { IDatePickerWrapperProps } from "./types";

interface IMonthPickerFormProps extends IDatePickerWrapperProps {
  inputProps?: TInputProps;
}

export default function MonthPickerForm({ inputProps, ...restProps }: IMonthPickerFormProps) {
  const {
    field: { onChange, value },
    error
  } = useTsController<any>();
  const { label } = useDescription();

  const inputValue =
    value?.toString()?.length > 4 ? `${value?.toString()?.slice(0, 4)}/${value?.toString()?.slice(4, 6)}` : "";

  return (
    <MonthPickerWrapper variant="light" initialValue={value?.toString()} {...restProps} onchange={onChange}>
      <Input
        {...inputProps}
        title={label}
        readOnly
        value={inputValue}
        errorMessage={error?.errorMessage}
        startAdornment={<Date className="text-[#676767] w-4 h-4 " />}
      />
    </MonthPickerWrapper>
  );
}
