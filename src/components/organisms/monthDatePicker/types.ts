import { ReactNode } from "react";

export type TRangeValue = [string, string] | [string] | null | undefined;

export interface IMonthPickerProps {
  setIsOpen?: (val: boolean) => void;
  isAdmin?: boolean;
  initialValue?: string;
  variant?: "dark" | "light";
  onchange?: (v: string) => void;
}

export interface IRangeMonthPickerProps {
  setIsOpen?: (val: boolean) => void;
  isAdmin?: boolean;
  initialValue?: TRangeValue;
  variant?: "dark" | "light";
  onchange?: (v: string) => void;
  onDateSelect?: (value: TRangeValue) => void;
}

export interface IFooterProps {
  disabled: boolean;
  onCancel: () => void;
  onConfirm: () => void;
}

export interface IRangeDatePickerWrapperProps extends IRangeMonthPickerProps {
  children?: ReactNode;
  className?: string;
  placement?: "bottom-start" | "bottom-end";
  closeOnConfirm?: boolean;
}

export interface IDatePickerWrapperProps extends IMonthPickerProps {
  children?: ReactNode;
  className?: string;
  placement?: "bottom-start" | "bottom-end";
  closeOnConfirm?: boolean;
}

export interface IDatePickerWrapperRefProps {
  close(): void;
  open(): void;
}

export interface ITheme {
  bgColor: string;
  color: string;
  borderColor: string;
  activeBgColor: string;
  activeColor: string;
}
