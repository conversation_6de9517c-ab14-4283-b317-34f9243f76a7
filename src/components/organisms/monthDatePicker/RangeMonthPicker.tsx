import { dateConverter } from "@/utils/DateHelper";
import { useEffect, useState } from "react";

import { IRangeMonthPickerProps } from "./types";

import "headless-react-datepicker/dist/styles.css";
import "./DatePicker.scss";
import { RangeMonths } from "./RangeMonths";
import { Header, Title } from "./utils";

export default function RangeMonthPicker({
  initialValue,
  setIsOpen,
  variant = "dark",
  onchange,
  onDateSelect
}: IRangeMonthPickerProps) {
  // eslint-disable-next-line no-unsafe-optional-chaining
  const currentYear = +dateConverter(new Date()?.toISOString()).locale("en")?.format("YYYY");

  const [value, setValue] = useState(initialValue);

  useEffect(() => {
    setValue(initialValue);
  }, [initialValue]);

  useEffect(() => {
    if (value?.[1] && onDateSelect) onDateSelect(value);
  }, [value]);

  const year = value?.[0]?.toString()?.slice(0, 4) ?? currentYear;
  const month = value?.[0]?.toString()?.slice(4, 6) ?? "";
  const endMonth = value?.[1]?.toString()?.slice(4, 6) ?? "";

  const lightTheme = {
    bgColor: "#EFEFEF",
    color: "#3f3c3c",
    borderColor: "#BDBDBD",
    activeBgColor: "#1477FF",
    activeColor: "#EFEFEF"
  };

  const darkTheme = {
    bgColor: "#343438",
    color: "#F4F4F4",
    borderColor: "#B9B4AB",
    activeBgColor: "#1477FF",
    activeColor: "#EFEFEF"
  };

  const theme = variant === "dark" ? darkTheme : lightTheme;

  return (
    <div
      style={{
        background: theme.bgColor,
        color: theme.color,
        border: `0.5px solid ${theme.borderColor}`,
        width: "238px",
        borderRadius: "8px",
        margin: "0 auto",
        minHeight: "254px"
      }}
    >
      <Title
        theme={theme}
        onClose={() => {
          setIsOpen?.(false);
        }}
      />
      <Header
        theme={theme}
        yearValue={+year}
        onChangeYear={v => {
          setValue([v + month]);

          onchange?.(v + month);
        }}
        variant={variant}
      />
      <RangeMonths
        theme={theme}
        monthValue={[month, endMonth]}
        onchangeMonth={v => {
          if (value?.[0] && v) {
            setValue([value?.[0] as string, year + v]);
            setIsOpen?.(false);
            onchange?.(year + v);
          } else if (v) setValue([year + v]);
        }}
      />
    </div>
  );
}
