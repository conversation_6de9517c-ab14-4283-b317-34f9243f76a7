/* eslint-disable import/prefer-default-export */
// import { persianMonth } from "@/utils/date";
import persianMonth from "@/utils/date";
import { dateConverter } from "@/utils/DateHelper";
import { useState } from "react";
import { twMerge } from "tailwind-merge";
import { ITheme, TRangeValue } from "./types";
import { addLeadingZero } from "./utils";

type MonthValue = string | null;

interface IMonthsProps {
  monthValue: TRangeValue;
  onchangeMonth: (v: MonthValue) => void;
  theme: ITheme;
}

export function RangeMonths({ monthValue, onchangeMonth, theme }: IMonthsProps) {
  const monthsList = Object.keys(persianMonth)?.map(key => ({
    label: persianMonth?.[+key],
    value: addLeadingZero(+key)
  }));
  const currentMonth = dateConverter(new Date()?.toISOString()).locale("en")?.format("MM");
  const [hoveredId, setHoveredId] = useState<string | undefined | null>(null);

  const handleMouseOver = (value?: string) => {
    setHoveredId(value);
  };

  const checkIsSelected = (item: { value: string }) =>
    monthValue?.[0] === item?.value || monthValue?.[1] === item?.value;

  const checkHighlight = (id: string) => {
    if (
      monthValue?.[0] &&
      Number(monthValue?.[0]) <= Number(hoveredId) &&
      Number(id) <= Number(hoveredId) &&
      Number(id) >= Number(monthValue?.[0])
    )
      return "bg-white/10 rounded-none";
    return "";
  };

  return (
    <div className="grid grid-cols-3 justify-between gap-y-2 w-full">
      {monthsList?.map(item => (
        <div
          onFocus={() => {}}
          key={item?.value}
          onClick={() => onchangeMonth?.(item?.value)}
          onMouseOver={() => handleMouseOver(item?.value)}
          style={{
            background: checkIsSelected(item) ? theme.activeBgColor : "",
            color: checkIsSelected(item) ? theme.activeColor : "",
            borderColor: currentMonth === item?.value ? theme.borderColor : "transparent"
          }}
          className={twMerge(
            "px-4 py-1.5 text-center w-full cursor-pointer rounded border border-transparent text-sm",
            checkHighlight(item?.value),
            checkIsSelected(item) && "rounded-r",
            Number(item?.value) === Number(hoveredId) && "rounded-l"
          )}
        >
          {item?.label}
        </div>
      ))}
    </div>
  );
}
