import { PopOver } from "@/components/atoms/popper";
import { Ref, forwardRef, useImperativeHandle, useState } from "react";

import MonthPicker from "./MonthPicker";
import { IDatePickerWrapperProps, IDatePickerWrapperRefProps } from "./types";

function MonthPickerWrapper(
  { children, placement = "bottom-end", ...restProps }: IDatePickerWrapperProps,
  ref: Ref<IDatePickerWrapperRefProps> // Ref should be of type DatePickerWrapperRef
) {
  const [isOpen, setIsOpen] = useState(false);

  const toggleOpen = (v: any) => setIsOpen(v);

  useImperativeHandle(ref, () => ({
    close() {
      setIsOpen(false);
    },
    open() {
      setIsOpen(true);
    }
  }));

  return (
    <PopOver
      setIsOpen={toggleOpen}
      isOpen={isOpen}
      placement={placement}
      content={<MonthPicker setIsOpen={toggleOpen} {...restProps} />}
    >
      {children}
    </PopOver>
  );
}

const ForwardedDatePickerWrapper = forwardRef(MonthPickerWrapper) as (
  props: IDatePickerWrapperProps & { ref?: Ref<IDatePickerWrapperRefProps> }
) => React.ReactElement;

export default ForwardedDatePickerWrapper;
