import DecisionAidBold from "@/assets/icons/Decision Aid-bold.svg";
import DecisionAid from "@/assets/icons/Decision Aid.svg";
import ElementFill from "@/assets/icons/element-fill.svg";
import Element from "@/assets/icons/element.svg";
import Graph from "@/assets/icons/graph-linear.svg";
import GraphFilled from "@/assets/icons/graph.svg";
import HomeFill from "@/assets/icons/home-fill.svg";
import Home from "@/assets/icons/home.svg";
import InterestRiskIcon from "@/assets/icons/interestRisk-1.svg";
import InterestRiskFillIcon from "@/assets/icons/interestRisk.svg";
import TransactionsIcon from "@/assets/icons/TransactionsIcon-1.svg";
import TransactionsFillIcon from "@/assets/icons/TransactionsIcon.svg";
import WatcherFill from "@/assets/icons/watcher-fill.svg";
import Watcher from "@/assets/icons/watcher.svg";

const SidebarItems = ({ isAdmin }: { isAdmin?: boolean }) => [
  {
    id: 1,
    icon: <Home className="w-6 h-6 text-mainText" />,
    fillWhiteIcon: <HomeFill className="text-white w-6 h-6" />,
    text: "خانه",
    href: "/"
  },
  {
    id: 2,
    icon: <Watcher className="w-6 h-6 text-mainText" />,
    fillWhiteIcon: <WatcherFill className="text-white w-6 h-6" />,
    text: "دیدبان",
    href: "/watchlist"
  },
  {
    id: 3,
    icon: <DecisionAid className="w-6 h-6 text-mainText" />,
    fillWhiteIcon: <DecisionAidBold className="text-white w-6 h-6" />,
    text: "پیشنهاد سرمایه گذاری",
    href: "/decisionAidSingle"
  },
  {
    id: 4,
    icon: <Element className="w-6 h-6 text-mainText" />,
    fillWhiteIcon: <ElementFill className="text-white w-6 h-6" />,
    text: "نقشه بازار",
    href: "/marketmap"
  },
  ...(isAdmin
    ? [
        {
          id: 5,
          icon: <InterestRiskIcon className="w-6 h-6 text-mainText" />,
          fillWhiteIcon: <InterestRiskFillIcon className="text-white w-6 h-6" />,
          text: "ریسک نرخ بهره",
          href: "/interestRateRisk?type=YTM"
        }
      ]
    : []),

  ...(isAdmin
    ? [
        {
          id: 6,
          icon: <TransactionsIcon className="w-6 h-6 text-mainText" />,
          fillWhiteIcon: <TransactionsFillIcon className="text-white w-6 h-6" />,
          text: "معاملات",
          href: "/transactions"
        }
      ]
    : []),
  ...(isAdmin
    ? [
        {
          id: 7,
          icon: <Graph className="w-6 h-6 text-mainText" />,
          fillWhiteIcon: <GraphFilled className="text-white w-6 h-6" />,
          text: "پرتفوی کاربری",
          href: "/portfolio"
        }
      ]
    : [])
];

export default SidebarItems;
