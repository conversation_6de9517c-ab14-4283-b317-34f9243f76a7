"use client";

import CalcIcon from "@/assets/icons/calculator-bold.svg";
import Logo from "@/assets/logo/logo.svg";
import { Tooltip } from "@/components/atoms/tooltip";
import { useGetProfileQuery } from "@/queries/profileAPI";
import { useCalculatorStatus } from "@/store/CalendarStore";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useMemo } from "react";
import { twMerge } from "tailwind-merge";
import SidebarItems from "./utils";

function Sidebar() {
  const { isShowCalculator, showCalculator, closeCalculator } = useCalculatorStatus();
  const { data: profile } = useGetProfileQuery();

  const pathName = usePathname();
  const sidebar = useMemo(
    () => SidebarItems({ isAdmin: profile?.data?.roleName === "administrator" }),
    [JSON.stringify(profile)]
  );

  const isActive = (href: string) => {
    if (pathName === "/" && href !== pathName) return false;

    if (href.includes(pathName)) return true;
    return false;
  };

  return (
    <div className="bg-newHeaderAndSidebar h-full flex flex-col pb-3 pt-4 items-center overflow-y-auto overflow-x-hidden">
      <div className="px-4 pb-4 flex flex-col gap-5 ">
        <Link href="/">
          <Logo />
        </Link>
      </div>

      <div className="w-full">
        <div className="h-[1px] bg-secondaryCardBackground  rounded-md mx-2 " />
      </div>

      <div className="flex flex-col justify-between items-center w-full px-2 flex-1">
        <div className="flex-1">
          <div className="flex flex-col items-center pt-[23px] gap-2 overflow-x-hidden">
            {sidebar &&
              sidebar.map(item => (
                <Tooltip className="w-fit" content={item.text} placement="left">
                  <Link
                    href={item.href}
                    className={twMerge(
                      "flex flex-col items-center w-full p-2 rounded-md  cursor-pointer",
                      isActive(item.href)
                        ? "bg-semanticPrimary relative"
                        : "group hover:bg-cardDarkBlue hover:text-white"
                    )}
                    data-test={`sidebar-item-${item.id}`}
                  >
                    {isActive(item.href) ? (
                      <div className="group-hover:block">{item.fillWhiteIcon}</div>
                    ) : (
                      <div>
                        <div className="text-backgroundCardBackground group-hover:hidden ">{item.icon}</div>
                        <div className="hidden group-hover:block">{item.fillWhiteIcon}</div>
                      </div>
                    )}
                  </Link>
                </Tooltip>
              ))}
          </div>
        </div>
        <div
          className={twMerge(
            "w-8 h-8 flex justify-center items-center cursor-pointer rounded-full mt-auto mb-3",
            !isShowCalculator ? "bg-mainText" : "bg-mainBlue"
          )}
        >
          {!isShowCalculator ? (
            <CalcIcon
              onClick={showCalculator}
              className="w-6 h-6 text-newHeaderAndSidebar"
              data-test="7815b8f2-6729-4795-861e-21c9cbb2c624"
            />
          ) : (
            <CalcIcon
              onClick={closeCalculator}
              className="w-6 h-6 text-smokeWhite"
              data-test="50ce8f8d-3ae1-4db8-b4fa-f29a75f911fc"
            />
          )}
        </div>
      </div>
    </div>
  );
}

export default Sidebar;
