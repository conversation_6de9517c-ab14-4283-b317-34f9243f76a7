import SearchFilterIcon from "@/assets/icons/search.svg";
import Button from "@/components/atoms/button";
import Input from "@/components/atoms/input";
import YTMRange from "@/components/organisms/filter/components/YTMRange/YTMRange";
import CouponStatus from "@/components/organisms/filter/components/couponStatus/CouponStatus";
import FilterDivider from "@/components/organisms/filter/components/filterDivider/FilterDivider";
import MarketSizeRange from "@/components/organisms/filter/components/marketSizeRange/MarketSizeRange";
import NominalInterest from "@/components/organisms/filter/components/nominalInterest/NominalInterest";
import useFilterStore, { filterDefaultValues } from "@/store/FilterStore";
import { errorToast } from "@/utils/toast";
import _ from "lodash";
import { twMerge } from "tailwind-merge";
import styles from "./Filter.module.scss";
import MaturityDate from "./components/maturityDate/MaturityDate";

function Filter() {
  const {
    BondName,
    setBondName,
    resetAll,
    setIsConfirmFilterCount,
    setDisableForm,
    MaturityDateRange,
    BestBuyYtm,
    CouponBond,
    ZeroCouponBond,
    TotalTradedVolume,
    LastTradePriceYtm,
    BestSellYtm,
    ClosePriceYtm,
    NominalInterestRate,
    isConfirmFilterCount,
    expandAll,
    disableForm,
    lastTradePriceYtmCollapse,
    bestBuyYtmCollapse,
    bestSellYtmCollapse,
    closePriceYtmCollapse,
    setExpandAll
  } = useFilterStore();

  const isFormDisabled = _.isEqual(filterDefaultValues, {
    BestBuyYtm,
    BondName,
    CouponBond,
    ZeroCouponBond,
    TotalTradedVolume,
    LastTradePriceYtm,
    BestSellYtm,
    ClosePriceYtm,
    NominalInterestRate,
    isConfirmFilterCount,
    expandAll,
    MaturityDateRange,
    disableForm,
    lastTradePriceYtmCollapse,
    bestBuyYtmCollapse,
    bestSellYtmCollapse,
    closePriceYtmCollapse
  });

  const handleRemoveFilterButtonClick = () => {
    resetAll();
    setExpandAll(true);
  };

  return (
    <div className="z-50 pt-2 pb-1 h-full w-full">
      <div className="px-2 relative h-8 w-full">
        <Input
          onKeyUp={e => {
            if (e.keyCode === 13) {
              setIsConfirmFilterCount();
            }
          }}
          onChange={(e: any) => {
            setDisableForm(false);
            setBondName(e);
          }}
          title="جستجو"
          inputSize="small"
          placeHolderEndAdornment={<div className="text-secondaryText">(نام نماد)</div>}
          startAdornment={<SearchFilterIcon className="w-4 h-4" />}
          className="h-8"
          value={BondName}
          type="text"
          data-test="bd800cfa-a840-40c1-b698-a011dda3243a"
        />
      </div>

      <div
        className={twMerge(
          " h-[calc(100%-40px)]  overflow-x-hidden mt-2 flex flex-col justify-between",
          styles?.customScrollBar
        )}
      >
        <div className="overflow-y-auto ml-3px px-2">
          <CouponStatus />
          <FilterDivider />

          <MaturityDate />
          <FilterDivider />

          <YTMRange />
          <FilterDivider />

          <MarketSizeRange />
          <FilterDivider />

          <NominalInterest />
        </div>

        <div className="flex justify-center gap-2 pb-1 pt-1.5 mt-auto shrink-0 ">
          <Button
            className="text-xs"
            variant="outLine"
            type="button"
            size="small"
            onClick={() => {
              handleRemoveFilterButtonClick();
            }}
            disabled={isFormDisabled}
            data-test="dcd4c127-8e35-4f8d-812c-5577cab374b2"
          >
            حذف فیلتر
          </Button>

          <Button
            className="text-xs"
            type="submit"
            size="small"
            onClick={() => {
              if (MaturityDateRange?.[0] === "-1" && MaturityDateRange?.[1] === "-1") {
                errorToast({
                  title: "بازه سر رسید را انتخاب کنید"
                });
              } else {
                setIsConfirmFilterCount();
              }
            }}
            disabled={isFormDisabled}
            data-test="84c715b6-7741-4837-8d67-7a517fc1ca78"
          >
            اعمال فیلتر
          </Button>
        </div>
      </div>
    </div>
  );
}

export default Filter;
