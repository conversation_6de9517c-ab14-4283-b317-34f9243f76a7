import FilterCollapse from "@/components/atoms/collapse/FilterCollapse";
import Input from "@/components/atoms/input";
import Slider from "@/components/atoms/slider/Slider";
import useFilterStore, { filterDefaultValues } from "@/store/FilterStore";
import React, { useState } from "react";
import _ from "lodash";

function NominalInterest() {
  const { NominalInterestRate, setNominalInterestRange } = useFilterStore();

  const [sliderError, setSliderError] = useState([false, false]);

  const minChange = (e: number) => {
    if (e < 0 || e > 100) return;
    if (!!NominalInterestRate?.[1] && e > NominalInterestRate[1]) {
      setSliderError([true, sliderError[1]]);
    } else {
      setSliderError([false, sliderError[1]]);
      setNominalInterestRange([!Number.isNaN(e) ? e : undefined, NominalInterestRate?.[1]]);
    }
  };
  const maxChange = (e: number) => {
    if (e < 0 || e > 100) return;
    if (NominalInterestRate?.[0] && e < NominalInterestRate[0]) {
      setSliderError([sliderError[0], true]);
    } else {
      setSliderError([sliderError[0], false]);
    }
    setNominalInterestRange([NominalInterestRate?.[0], !Number.isNaN(e) ? e : undefined]);
  };

  const onChangeSlider = (e: any) => {
    setSliderError([false, false]);
    setNominalInterestRange(e);
  };

  return (
    <div>
      <FilterCollapse
        title="محدوده سود اسمی"
        className=" pr-2"
        id="NominalInterest"
        onClearClick={() => setNominalInterestRange(filterDefaultValues.NominalInterestRate)}
        showClear={!_.isEqual(NominalInterestRate, filterDefaultValues.NominalInterestRate)}
      >
        <div className="flex flex-col gap-4">
          <div className="flex justify-between gap-2 h-10">
            <Input<number>
              isError={sliderError[0]}
              endAdornment={<div>از</div>}
              className="text-center ltr h-10"
              onChange={minChange}
              startAdornment={<div className="text-[12px] mt-[2px]">%</div>}
              value={NominalInterestRate?.[0]}
              type="number"
              placeHolder="0"
              data-test="30106d00-5d94-4982-855c-52f25f013edd"
            />
            <Input<number>
              isError={sliderError[1]}
              endAdornment={<div>تا</div>}
              onChange={maxChange}
              className="text-center ltr h-10"
              startAdornment={<div className="text-[12px] mt-[2px]">%</div>}
              value={NominalInterestRate?.[1]}
              type="number"
              placeHolder="100"
              data-test="8502f054-5d9f-48ac-a3e0-a83d789fcac7"
            />
          </div>
          <Slider
            range
            min={0}
            max={100}
            onChange={onChangeSlider}
            reverse
            defaultValue={NominalInterestRate ?? [0, 100]}
            value={NominalInterestRate ?? [0, 100]}
          />
          <div className="flex justify-between">
            <div className="h-4 text-secondaryText text-[10px]">کمترین</div>
            <div className="h-4 text-secondaryText text-[10px]">بیشترین</div>
          </div>
        </div>
      </FilterCollapse>
    </div>
  );
}

export default NominalInterest;
