/* eslint-disable no-extra-boolean-cast */
/* eslint-disable no-param-reassign */
import FilterCollapse from "@/components/atoms/collapse/FilterCollapse";
import TwoInputsRangePicker from "@/components/organisms/datePicker/TwoInputsRangePicker";
import { IDatePickerWrapperRefProps } from "@/components/organisms/datePicker/types";
import useFilterStore, { filterDefaultValues } from "@/store/FilterStore";
import { dateConverter } from "@/utils/DateHelper";
import _ from "lodash";
import { useEffect, useRef, useState } from "react";
import RadioGroup from "../../../../molecules/radioGroup/RadioGroup";
import maturityDateItems, { DefaultValue } from "./utils";

function MaturityDate() {
  const { setMaturityDateRange, MaturityDateRange } = useFilterStore();

  const [maturityItems, setMaturityDateItems] = useState(maturityDateItems);
  const [disableDatePicker, setDisableDatePicker] = useState(true);

  useEffect(() => {
    const clonedArray = JSON.parse(JSON.stringify(maturityItems));
    if (!MaturityDateRange || (MaturityDateRange?.[0] === "" && MaturityDateRange?.[1] === "")) {
      clonedArray?.forEach((item: any) => {
        if (item?.id === "none") item.checked = true;
        else item.checked = false;
      });
      setMaturityDateItems(clonedArray);
    }
  }, [MaturityDateRange]);

  const datePickerRef = useRef<IDatePickerWrapperRefProps>(null);

  const dateValue = (): Date[] | undefined => {
    if (
      MaturityDateRange?.[0] &&
      MaturityDateRange?.[0] &&
      MaturityDateRange?.[0] !== "-1" &&
      MaturityDateRange?.[1] !== "-1"
    ) {
      return [new Date(MaturityDateRange[0]), new Date(MaturityDateRange[1])];
    }
    return MaturityDateRange?.[0] ? [new Date(MaturityDateRange[0])] : undefined;
  };

  const value =
    !disableDatePicker &&
    (MaturityDateRange?.[0] || MaturityDateRange?.[1]) &&
    (MaturityDateRange?.[0] !== "-1" || MaturityDateRange?.[1] !== "-1")
      ? dateValue()
      : undefined;

  return (
    <FilterCollapse
      title=" سررسید"
      className=" pr-2"
      id="MaturityDateRange"
      onClearClick={() => {
        setDisableDatePicker(true);
        setMaturityDateRange(filterDefaultValues.MaturityDateRange);
      }}
      showClear={!_.isEqual(MaturityDateRange, filterDefaultValues.MaturityDateRange)}
    >
      <div className="flex flex-col gap-2">
        <RadioGroup
          size="medium"
          items={maturityItems}
          className="flex flex-col gap-2"
          onSwitch={e => {
            if (e?.value === DefaultValue.custom) {
              setDisableDatePicker(false);
              setMaturityDateRange(["-1", "-1"]);
            } else {
              setDisableDatePicker(true);
              setMaturityDateRange(e?.value);
            }
          }}
          id="MaturityDate"
        />
        <TwoInputsRangePicker
          ref={datePickerRef}
          id="MaturityDateRange"
          rootClassName="flex justify-between gap-2 h-6"
          config={{
            yearRangeFrom: 1395,
            yearRangeTo:
              parseInt(dateConverter(new Date()).calendar("persian").locale("en-US").format("YYYY"), 10) + 10,
            // today - 1 min
            minDate: new Date(new Date().setDate(new Date().getDate() - 1))
          }}
          value={value}
          onChange={(v, activeInput) => {
            if (!v[1]) {
              if (activeInput === 2) {
                setMaturityDateRange([new Date().toISOString(), new Date(v[0]).toISOString()]);
              } else {
                setMaturityDateRange([new Date(v[0]).toISOString(), MaturityDateRange?.[1] as string]);
              }
            } else {
              setMaturityDateRange([new Date(v[0]).toISOString(), new Date(v[1]).toISOString()]);
            }
          }}
          labels={["از تاریخ", "تا تاریخ"]}
          input1props={{
            className: "ltr text-[12px] -mt-[2px] h-6",
            inputWrapperProps: { labelClass: "text-mediumBlue" },
            inputSize: "small"
          }}
          input2props={{
            className: "ltr text-[12px] -mt-[2px] h-6",
            inputWrapperProps: { labelClass: "text-mediumBlue" },
            inputSize: "small"
          }}
          disabled={disableDatePicker || (!MaturityDateRange?.[0] && !MaturityDateRange?.[1])}
        />
      </div>
    </FilterCollapse>
  );
}

export default MaturityDate;
