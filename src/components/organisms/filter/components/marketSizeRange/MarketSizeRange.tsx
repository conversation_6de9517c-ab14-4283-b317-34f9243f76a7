import FilterCollapse from "@/components/atoms/collapse/FilterCollapse";
import Input from "@/components/atoms/input";
import useFilterStore, { filterDefaultValues } from "@/store/FilterStore";
import React, { useState } from "react";
import _ from "lodash";

function MarketSizeRange() {
  const [sliderError, setSliderError] = useState([false, false]);
  const { TotalTradedVolume, setMarketSizeRange } = useFilterStore();

  const minChange = (e: number) => {
    if (e < 0 || e > 600000000) return;
    if (!!TotalTradedVolume?.[1] && e > TotalTradedVolume?.[1]) {
      setSliderError([true, sliderError[1]]);
    } else {
      setSliderError([false, sliderError[1]]);
      setMarketSizeRange([!Number.isNaN(e) ? e : undefined, TotalTradedVolume?.[1]]);
    }
  };
  const maxChange = (e: number) => {
    if (e < 0 || e > 600000000) return;
    if (!!TotalTradedVolume?.[0] && e < TotalTradedVolume?.[0]) {
      setSliderError([sliderError[0], true]);
    } else {
      setSliderError([sliderError[0], false]);
    }
    setMarketSizeRange([TotalTradedVolume?.[0], !Number.isNaN(e) ? e : undefined]);
  };

  return (
    <div>
      <FilterCollapse
        title="محدوده حجم معاملات"
        className="  pr-2 pl-[2px]"
        id="MarketSizeRange"
        onClearClick={() => setMarketSizeRange(filterDefaultValues?.TotalTradedVolume)}
        showClear={!_.isEqual(TotalTradedVolume, filterDefaultValues.TotalTradedVolume)}
      >
        <div className="flex flex-col gap-1">
          <div className="flex flex-col justify-between gap-1 h-22">
            <Input<number>
              isError={sliderError[0]}
              endAdornment={<div>از</div>}
              className="text-left ltr h-10"
              onChange={minChange}
              value={TotalTradedVolume?.[0]}
              max={600000000}
              type="number"
              placeHolder="0"
              data-test="MarketSizeRangeFrom"
            />
            <Input<number>
              isError={sliderError[1]}
              endAdornment={<div>تا</div>}
              onChange={maxChange}
              className="text-left ltr h-10"
              value={TotalTradedVolume?.[1]}
              max={600000000}
              type="number"
              placeHolder="600,000,000"
              data-test="MarketSizeRangeTo"
            />
          </div>
        </div>
      </FilterCollapse>
    </div>
  );
}

export default MarketSizeRange;
