/* eslint-disable @typescript-eslint/no-unused-expressions */
import Checkbox from "@/components/atoms/checkbox/Checkbox";
import FilterCollapse from "@/components/atoms/collapse/FilterCollapse";
import useFilterStore, { filterDefaultValues } from "@/store/FilterStore";
import YTMClosePrice from "../YTMClosePrice/YTMClosePrice";
import YTMLastPrice from "../YTMLastPrice/YTMLastPrice";
import YTMPurchaseQuote from "../YTMPurchaseQuote/YTMPurchaseQuote";
import YTMSalesQuote from "../YTMSalesQuote/YTMSalesQuote";

function YTMRange() {
  const {
    setYTMLastPriceRange,
    setYTMPurchaseQuote,
    setYTMSalesQuote,
    setYTMClosePrice,
    setLastTradePriceYtmCollapse,
    setBestBuyYtmCollapse,
    setBestSellYtmCollapse,
    setClosePriceYtmCollapse,
    lastTradePriceYtmCollapse,
    bestBuyYtmCollapse,
    bestSellYtmCollapse,
    closePriceYtmCollapse
  } = useFilterStore();

  const clearYtmFilter = () => {
    setYTMLastPriceRange(filterDefaultValues?.LastTradePriceYtm);
    setYTMPurchaseQuote(filterDefaultValues?.BestBuyYtm);
    setYTMSalesQuote(filterDefaultValues?.BestSellYtm);
    setYTMClosePrice(filterDefaultValues?.ClosePriceYtm);

    setLastTradePriceYtmCollapse(false);
    setBestBuyYtmCollapse(false);
    setBestSellYtmCollapse(false);
    setClosePriceYtmCollapse(false);
  };

  return (
    <div>
      <FilterCollapse
        title="محدوده YTM"
        className="pr-2"
        id="YTMRange"
        onClearClick={() => clearYtmFilter()}
        showClear={lastTradePriceYtmCollapse || bestBuyYtmCollapse || bestSellYtmCollapse || closePriceYtmCollapse}
      >
        <div className="flex flex-col gap-4">
          <div className="flex flex-col gap-4">
            <Checkbox
              id="lastPrice"
              text="آخرین قیمت"
              checked={lastTradePriceYtmCollapse}
              variant="filledBlue"
              size="small"
              onChange={() => {
                setLastTradePriceYtmCollapse(!lastTradePriceYtmCollapse);
              }}
            />
            {lastTradePriceYtmCollapse && <YTMLastPrice />}
          </div>

          <div className="flex flex-col gap-4">
            <Checkbox
              id="purchaseQuote"
              text="بهترین مظنه خرید"
              checked={bestBuyYtmCollapse}
              variant="filledBlue"
              size="small"
              onChange={() => {
                setBestBuyYtmCollapse(!bestBuyYtmCollapse);
              }}
            />
            {bestBuyYtmCollapse && <YTMPurchaseQuote />}
          </div>

          <div className="flex flex-col gap-4">
            <Checkbox
              id="salesQuote"
              text="بهترین مظنه فروش"
              variant="filledBlue"
              size="small"
              checked={bestSellYtmCollapse}
              onChange={() => {
                setBestSellYtmCollapse(!bestSellYtmCollapse);
              }}
            />
            {bestSellYtmCollapse && <YTMSalesQuote />}
          </div>

          <div className="flex flex-col gap-4">
            <Checkbox
              id="closePrice"
              text="قیمت پایانی"
              variant="filledBlue"
              size="small"
              checked={closePriceYtmCollapse}
              onChange={() => {
                setClosePriceYtmCollapse(!closePriceYtmCollapse);
              }}
            />
            {closePriceYtmCollapse && <YTMClosePrice />}
          </div>
        </div>
      </FilterCollapse>
    </div>
  );
}

export default YTMRange;
