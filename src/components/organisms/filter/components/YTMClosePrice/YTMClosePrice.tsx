import Input from "@/components/atoms/input";
import Slider from "@/components/atoms/slider/Slider";
import useFilterStore from "@/store/FilterStore";
import React, { useState } from "react";

function YTMClosePrice() {
  const { ClosePriceYtm, setYTMClosePrice } = useFilterStore();

  const [sliderError, setSliderError] = useState([false, false]);

  const minChange = (e: number) => {
    if (e < -2000 || e > 2000) return;
    if (!!ClosePriceYtm?.[1] && e > ClosePriceYtm[1]) {
      setSliderError([true, sliderError[1]]);
    } else {
      setSliderError([false, sliderError[1]]);
      setYTMClosePrice([!Number.isNaN(e) ? e : undefined, ClosePriceYtm?.[1]]);
    }
  };
  const maxChange = (e: number) => {
    if (e < -2000 || e > 2000) return;
    if (ClosePriceYtm?.[0] && e < ClosePriceYtm[0]) {
      setSliderError([sliderError[0], true]);
    } else {
      setSliderError([sliderError[0], false]);
    }
    setYTMClosePrice([ClosePriceYtm?.[0], !Number.isNaN(e) ? e : undefined]);
  };

  const onChangeSlider = (e: any) => {
    setSliderError([false, false]);
    setYTMClosePrice(e);
  };

  return (
    <>
      <div className="flex justify-between gap-2 h-10">
        <Input<number>
          isError={sliderError[0]}
          endAdornment={<div>از</div>}
          className="text-center ltr h-10"
          onChange={minChange}
          placeHolder="-2,000"
          startAdornment={<div className="text-[12px] mt-[2px]">%</div>}
          value={ClosePriceYtm?.[0]}
          type="number"
          data-test="YTMClosePrice-min"
        />
        <Input<number>
          isError={sliderError[1]}
          endAdornment={<div>تا</div>}
          onChange={maxChange}
          startAdornment={<div className="text-[12px] mt-[2px]">%</div>}
          className="text-center ltr h-10"
          placeHolder="2,000"
          value={ClosePriceYtm?.[1]}
          type="number"
          data-test="YTMClosePrice-max"
        />
      </div>
      <Slider
        range
        min={-2000}
        max={2000}
        onChange={onChangeSlider}
        reverse
        defaultValue={ClosePriceYtm ?? [-2000, 2000]}
        value={ClosePriceYtm}
      />
      <div className="flex justify-between">
        <div className="h-4 text-secondaryText text-[10px]">کمترین</div>
        <div className="h-4 text-secondaryText text-[10px]">بیشترین</div>
      </div>
    </>
  );
}

export default YTMClosePrice;
