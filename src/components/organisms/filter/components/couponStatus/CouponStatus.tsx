import Checkbox from "@/components/atoms/checkbox/Checkbox";
import useFilterStore from "@/store/FilterStore";
import React, { useEffect } from "react";

function CouponStatus() {
  const { CouponBond, ZeroCouponBond, BondName, toggleWithCoupons, toggleWithOutCoupons, setDisableForm } =
    useFilterStore();

  useEffect(() => {
    if (!CouponBond && !ZeroCouponBond && !BondName) {
      setDisableForm(true);
    }
  }, [CouponBond, ZeroCouponBond]);

  return (
    <div className="flex flex-col gap-2 pb-4 pr-1">
      <div className=" flex items-center  text-white200 text-xs">وضعیت کوپن</div>
      <div className="flex gap-6 ">
        <Checkbox
          text="کوپن دار"
          id="withCoupon"
          variant="filledGreen"
          size="small"
          checked={CouponBond}
          onChange={() => toggleWithCoupons(!CouponBond)}
        />

        <Checkbox
          text="بدون کوپن"
          id="withOutCoupon"
          variant="filledLightYellow"
          size="small"
          checked={ZeroCouponBond}
          onChange={() => toggleWithOutCoupons(!ZeroCouponBond)}
        />
      </div>
    </div>
  );
}

export default CouponStatus;
