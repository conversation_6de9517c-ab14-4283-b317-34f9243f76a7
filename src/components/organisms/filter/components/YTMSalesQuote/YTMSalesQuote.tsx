import Input from "@/components/atoms/input";
import Slider from "@/components/atoms/slider/Slider";
import useFilterStore from "@/store/FilterStore";
import React, { useState } from "react";

function YTMSalesQuote() {
  const { BestSellYtm, setYTMSalesQuote } = useFilterStore();

  const [sliderError, setSliderError] = useState([false, false]);

  const minChange = (e: number) => {
    if (e < -2000 || e > 2000) return;
    if (!!BestSellYtm?.[1] && e > BestSellYtm[1]) {
      setSliderError([true, sliderError[1]]);
    } else {
      setSliderError([false, sliderError[1]]);
      setYTMSalesQuote([!Number.isNaN(e) ? e : undefined, BestSellYtm?.[1]]);
    }
  };
  const maxChange = (e: number) => {
    if (e < -2000 || e > 2000) return;
    if (BestSellYtm?.[0] && e < BestSellYtm[0]) {
      setSliderError([sliderError[0], true]);
    } else {
      setSliderError([sliderError[0], false]);
    }
    setYTMSalesQuote([BestSellYtm?.[0], !Number.isNaN(e) ? e : undefined]);
  };

  const onChangeSlider = (e: any) => {
    setSliderError([false, false]);
    setYTMSalesQuote(e);
  };

  return (
    <>
      <div className="flex justify-between gap-2 h-10">
        <Input<number>
          isError={sliderError[0]}
          endAdornment={<div>از</div>}
          className="text-center ltr h-10"
          onChange={minChange}
          startAdornment={<div className="text-[12px] mt-[2px]">%</div>}
          value={BestSellYtm?.[0]}
          type="number"
          placeHolder="-2,000"
          data-test="YTMSalesQuote-min"
        />
        <Input<number>
          isError={sliderError[1]}
          endAdornment={<div>تا</div>}
          onChange={maxChange}
          className="text-center ltr h-10"
          startAdornment={<div className="text-[12px] mt-[2px]">%</div>}
          value={BestSellYtm?.[1]}
          type="number"
          placeHolder="2,000"
          data-test="YTMSalesQuote-max"
        />
      </div>
      <Slider
        range
        min={-2000}
        max={2000}
        onChange={onChangeSlider}
        reverse
        defaultValue={BestSellYtm ?? [-2000, 2000]}
        value={BestSellYtm}
      />
      <div className="flex justify-between">
        <div className="h-4 text-secondaryText text-[10px]">کمترین</div>
        <div className="h-4 text-secondaryText text-[10px]">بیشترین</div>
      </div>
    </>
  );
}

export default YTMSalesQuote;
