"use client";

/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import { memo, useState } from "react";
import ReactPaginate from "react-paginate";
import ArrowLeftPagination from "@/assets/icons/arrow-left.svg";
import RightArrowJumpPaginate from "@/assets/icons/EndRight.svg";
import LeftArrowJumpPaginate from "@/assets/icons/EndLeft.svg";
import { Select } from "@/components/atoms/select";
import { IPaginationProps } from "./type";
import defaultItemsPerPage from "./utils";

function Pagination(props: IPaginationProps) {
  const {
    totalCount,
    showItemsCounter = false,
    itemsPerPage = defaultItemsPerPage,
    pageNumber,
    setPageNumber,
    rowPerPage = 15,
    onChangeRow
  } = props;

  const [rowPerPageState, setRowPerPageState] = useState(rowPerPage);

  const pageCount = Math.ceil(totalCount / rowPerPageState);

  const handlePageClick = (event: { selected: React.SetStateAction<number> }) => {
    setPageNumber?.(event.selected);
  };

  const rowPerPageValue = itemsPerPage.find(item => item.value === rowPerPage);

  return (
    <div className="flex items-center justify-center  w-full gap-2">
      <div className="flex items-center justify-center gap-1">
        <div className="cursor-pointer" onClick={() => setPageNumber?.(0)}>
          <RightArrowJumpPaginate className="w-4 h-4" />
        </div>
        <ReactPaginate
          className="flex gap-1 h-10 items-center justify-center flex-nowrap ltr"
          pageClassName="w-6 h-6 flex rounded-[6px] items-center justify-center p-[11px] gap-[11px] text-mainText"
          pageLinkClassName="items-center gap-1 justify-center p-[11px]"
          nextLabel={<ArrowLeftPagination className="w-4 h-4 rotate-180" />}
          nextClassName="flex justify-center items-center  gap-[11px]"
          nextLinkClassName=""
          previousClassName="flex justify-center items-center gap-[11px]"
          previousLinkClassName=""
          onPageChange={handlePageClick}
          forcePage={pageNumber}
          pageRangeDisplayed={2}
          marginPagesDisplayed={1}
          pageCount={pageCount}
          previousLabel={<ArrowLeftPagination className=" h-4 w-4" />}
          breakLabel={<div className="text-mainText px-1.5">...</div>}
          activeClassName="border border-solid border-semanticPrimary"
          renderOnZeroPageCount={null}
        />
        <div className="cursor-pointer" onClick={() => setPageNumber?.(pageCount - 1)}>
          <LeftArrowJumpPaginate className="w-4 h-4" />
        </div>
      </div>
      {showItemsCounter && (
        <Select
          variant="dropdown"
          items={itemsPerPage}
          value={rowPerPageValue}
          className="border rounded py-2 h-6 pl-1 pr-[6px] min-w-[38px]"
          menuPosition="fixed"
          onChange={(e: any) => {
            setRowPerPageState(e?.value ?? 15);
            onChangeRow?.(e?.value);
          }}
        />
      )}
    </div>
  );
}

export default memo(Pagination);
