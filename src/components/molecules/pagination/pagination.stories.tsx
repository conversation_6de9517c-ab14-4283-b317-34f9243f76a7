import type { Meta, StoryObj } from "@storybook/react";
import Pagination from "./Pagination";

const meta: Meta<typeof Pagination> = {
  component: Pagination,
  title: "Components/Molecules/Pagination"
};

export default meta;
type Story = StoryObj<typeof Pagination>;

export const Default: Story = {
  args: {
    totalCount: 100,
    showItemsCounter: true,
    pageNumber: 1,
    setPageNumber: () => {}
  }
};
