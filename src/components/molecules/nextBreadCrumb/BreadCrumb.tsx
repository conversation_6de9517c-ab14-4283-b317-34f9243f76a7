import Link from "next/link";
import { usePathname } from "next/navigation";
import React from "react";
import { twMerge } from "tailwind-merge";
import ArrowLeft from "@/assets/icons/arrow-left.svg";
import { IBreadCrumbProps } from "./types";

function BreadCrumb({ breadCrumbItems }: IBreadCrumbProps) {
  const paths = usePathname();

  return (
    <div className="flex items-center gap-1">
      {breadCrumbItems.map((item, index) => (
        <div className="flex items-center gap-1 pr-px">
          <Link href={item.href}>
            <div
              className={twMerge(
                item.href === paths ? "text-sm leading-6 text-mainText font-bold" : "text-xs text-mainText"
              )}
            >
              {item.title}
            </div>
          </Link>

          <div>
            {breadCrumbItems.length > 1 && index < breadCrumbItems.length - 1 && (
              <ArrowLeft className="w-4 h-4 text-semanticPrimary" />
            )}
          </div>
        </div>
      ))}
    </div>
  );
}

export default BreadCrumb;
