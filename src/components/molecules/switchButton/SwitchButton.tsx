import Button from "@/components/atoms/button";
import React from "react";
import { twMerge } from "tailwind-merge";
import { ISwitchButtonProps } from "./types";

function SwitchButton({
  switchItems,
  isDisabled = false,
  selectedItemId,
  onSelectedItemChange,
  dataTest
}: ISwitchButtonProps) {
  return (
    <div
      data-test={dataTest}
      className={twMerge(
        "flex gap-1 justify-center align-middle  rounded-[4px] p-0.5",
        isDisabled ? "bg-disable" : "bg-bodyBackground"
      )}
    >
      {switchItems &&
        switchItems.map(item => (
          <div>
            <Button
              variant={selectedItemId === item.id ? "fill" : "noFrame"}
              data-test={item?.testId}
              className={twMerge(
                "!text-mainText !shadow-none !font-normal py-1.5",
                selectedItemId === item.id
                  ? "hover:bg-semanticPrimary"
                  : "hover:!text-secondaryText active:bg-radiance800",
                isDisabled && (selectedItemId === item.id ? "!bg-secondaryText !text-mainText" : "!text-secondaryText")
              )}
              size="small"
              onClick={() => onSelectedItemChange?.(item.id)}
              disabled={isDisabled}
            >
              <div className="flex items-center gap-2">
                {item?.icon}
                {item.title}
              </div>
            </Button>
          </div>
        ))}
    </div>
  );
}

export default SwitchButton;
