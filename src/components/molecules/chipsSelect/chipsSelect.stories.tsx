import type { Meta, StoryObj } from "@storybook/react";
import ChipsSelect from "./ChipsSelect";

const meta: Meta<typeof ChipsSelect> = {
  component: ChipsSelect,
  title: "Components/Molecules/ChipsSelect"
};

export default meta;
type Story = StoryObj<typeof ChipsSelect>;

export const Default: Story = {
  args: {
    items: [
      { id: 1, title: "1روز" },
      { id: 2, title: "1هفته" },
      { id: 3, title: "1ماه" },
      { id: 4, title: "3ماه" },
      { id: 5, title: "6ماه" },
      { id: 6, title: "1سال" }
    ]
  }
};
