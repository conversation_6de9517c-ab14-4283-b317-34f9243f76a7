"use client";

/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable import/no-extraneous-dependencies */
import { twMerge } from "tailwind-merge";
import TransitionEffect from "@/containers/tradeHistory/TransitionEffect";
import { IChipsSelectItem, IChipsSelectProps } from "./type";

function ChipsSelect(props: IChipsSelectProps) {
  const { items, activeChip, onSwitch } = props;

  const switchChip = (item: IChipsSelectItem) => {
    onSwitch?.(item);
  };

  if (!items?.length) return null;

  return (
    <>
      <div className={twMerge("flex", props?.className)}>
        {items
          ?.filter(i => i.title)
          .map(item => (
            <div
              id={`chips-item-${item?.id}`}
              key={item?.id}
              className={twMerge(
                "z-10 h-7 w-[52px] m-[2px] text-center flex justify-center items-center rounded text-xs leading-4 text-white cursor-pointer",
                activeChip?.id === item?.id && "active"
              )}
              onClick={() => switchChip(item)}
              data-test={`chips-item-${item?.id}`}
            >
              {item?.title}
            </div>
          ))}
      </div>
      {activeChip && <TransitionEffect active={activeChip} padding={8} />}
    </>
  );
}

export default ChipsSelect;
