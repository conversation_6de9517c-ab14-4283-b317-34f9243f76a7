"use client";

import Close from "@/assets/icons/Cancel.svg";
import useUserModalStore from "@/store/userModalStore/UserModalStore";
import Dialog from "rc-dialog";
import { twMerge } from "tailwind-merge";
import Styles from "./UserModalProvider.module.scss";

type MousePosition = { x: number; y: number } | null;

let mousePosition: MousePosition;

// ref: https://github.com/ant-design/ant-design/issues/15795
const getClickPosition = (e: MouseEvent) => {
  mousePosition = {
    x: e.pageX,
    y: e.pageY
  };
  setTimeout(() => {
    mousePosition = null;
  }, 100);
};

if (typeof document !== "undefined") {
  document.documentElement.addEventListener("click", getClickPosition, true);
}

function UserModalProvider() {
  const { isOpen, closeUserModal, userModalContent, setUserModalContent, userModalProps, setUserModalProps } =
    useUserModalStore();

  if (!userModalContent) return null;

  return (
    <Dialog
      {...userModalProps}
      animation="zoom"
      maskAnimation="fade"
      destroyOnClose
      focusTriggerAfterClose={false}
      closable={false}
      /* --- these props must below {...modalProps} and will override modalProps -- */
      visible={isOpen}
      rootClassName={twMerge(userModalProps?.rootClassName, Styles.rootModal, "bg-red-500")}
      mousePosition={mousePosition}
      wrapClassName={twMerge(
        userModalProps?.center ? "flex items-center justify-center " : "backdrop-blur-sm",
        userModalProps?.wrapClassName
      )}
      onClose={e => {
        closeUserModal();
        userModalProps?.onClose?.(e);
      }}
      afterClose={() => {
        setUserModalContent(null);
        setUserModalProps(undefined);
        userModalProps?.afterClose?.();
      }}
    >
      <div className="flex flex-col p-3 bg-backgroundCardBackground h-full">
        {!!userModalProps?.headerTitle && (
          <div className="flex items-center justify-between pb-3">
            <span className={twMerge("text-[#F4F4F4] font-bold ", userModalProps.titleClassName)}>
              {userModalProps?.headerTitle}
            </span>
            <Close
              width={14}
              height={14}
              className="cursor-pointer w-6 h-6"
              onClick={closeUserModal}
              aria-label="modal-close"
            />
          </div>
        )}
        {userModalContent}
      </div>
    </Dialog>
  );
}

export default UserModalProvider;
