import { dateConverter } from "@/utils/DateHelper";

export interface IRenderRecordParams {
  dateTime: string;
  value: number;
  index: number;
  count: number;
  color?: string | null;
}

export const convertDate = (date: string) => {
  const d = new Date(date);
  const utc = Date.UTC(d.getFullYear(), d.getMonth(), d.getDate());
  return utc;
};

export const renderRecord = (params: IRenderRecordParams) => {
  const { dateTime, value, index, count, color = null } = params;

  if (index < count - 1) {
    return {
      x: convertDate(dateTime),
      y: value,
      marker: { fillColor: "#F4F4F4", states: { hover: { fillColor: color } } }
    };
  }

  return { x: convertDate(dateTime), y: value };
};

// Helper function to get the first day of each Jalali month in the data range
export function getJalaliMonthBoundaries(data: any[]) {
  if (!data || !data.length) return [];

  // Extract all dates from the data series
  const allDates: number[] = [];
  data.forEach(series => {
    if (series.data && Array.isArray(series.data)) {
      series.data.forEach((point: any) => {
        if (point && typeof point.x === "number") {
          allDates.push(point.x);
        }
      });
    }
  });

  if (allDates.length === 0) return [];

  // Sort dates and remove duplicates
  const sortedDates = Array.from(new Set(allDates)).sort((a, b) => a - b);

  // Find the first day of each Jalali month in the range
  const monthBoundaries: number[] = [];
  let currentMonth = "";

  sortedDates.forEach(timestamp => {
    const date = new Date(timestamp);
    // Convert to Jalali and get year/month
    const jalaliDate = dateConverter(date.toString()).locale("fa");
    const yearMonth = jalaliDate.format("YYYY/MM");

    // const jalaliDate = gregorianDateToJalaliFa(date.toString()).format("MM/DD");
    // const yearMonth = jalaliDate;

    // If this is a new month, add it to boundaries
    if (yearMonth !== currentMonth) {
      currentMonth = yearMonth;
      monthBoundaries.push(timestamp);
    }
  });

  return monthBoundaries;
}

export function findInterval(showMonthlyPeriod?: boolean) {
  return showMonthlyPeriod ? 24 * 3600 * 1000 * 30 : null;
}
