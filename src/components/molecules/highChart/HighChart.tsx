"use client";

import { getJalaliMonthBoundaries } from "@/components/molecules/highChart/utils";
import { getNow } from "@/containers/tradeHistory/util";
/* eslint-disable import/no-extraneous-dependencies */
import { useVarDateFilterStore } from "@/store/varDateFilter";
import { calculateDaysBetweenDates, dateConverter } from "@/utils/DateHelper";
import HighchartsReact from "highcharts-react-official";
import Highcharts, { Options } from "highcharts/highstock";
import { memo, PropsWithoutRef, RefObject, startTransition, useLayoutEffect, useRef, useState } from "react";

if (Highcharts && Highcharts.setOptions) {
  Highcharts.setOptions({
    lang: {
      resetZoom: "لغو بزرگ نمایی"
    }
  });
}

function HighChart(props: PropsWithoutRef<HighchartsReact.Props>) {
  const chartRef = useRef<{ chart: Highcharts.Chart; container: RefObject<Highcharts.Chart> }>(null);
  const depthRef = useRef(0);
  const [key, setKey] = useState(0);
  const { period } = useVarDateFilterStore();
  const showMonthlyPeriod = calculateDaysBetweenDates(period?.fromDate, period?.toDate || getNow()) > 31;
  const isLine = props?.options?.chart?.type !== "pie" && props?.options?.chart?.type !== "column";
  const showResetZoomButton = (props?.showResetZoom || props?.options?.chart?.zooming?.enabled) ?? isLine;

  const data = props?.options?.series;

  const options = {
    chart: {
      marginTop: 60,
      ...props?.options.chart
    },
    xAxis: {
      crosshair: isLine
        ? {
            width: 2,
            color: "#858585",
            dashStyle: "shortdot"
          }
        : false,
      tickPositioner() {
        // Get the month boundaries from our data
        const monthBoundaries = getJalaliMonthBoundaries(data);

        if (props?.options?.xAxis?.tickPositioner) {
          return props?.options?.xAxis?.tickPositioner;
        }
        return showMonthlyPeriod ? monthBoundaries : undefined;
      },

      // tickPositioner: props?.options?.xAxis?.tickPositioner || (() => undefined),
      labels: {
        ...(props?.options?.xAxis?.labels || {}),
        formatter() {
          const d = new Date((this as any)?.value);
          const format = showMonthlyPeriod ? "MMMM" : "MM/DD";
          return dateConverter(d.toString()).locale("fa").format(format);
        },
        y: 34,
        style: {
          ...(props?.options?.xAxis?.labels?.style || {}),
          color: "#F4F4F4",
          fontFamily: "yekan-bakh",
          fontSize: showMonthlyPeriod ? "16px" : "12px",
          fontWeight: showMonthlyPeriod ? "700" : "400"
        }
      },
      ...(props?.options?.xAxis || {})
    },
    ...props?.options
  };

  function updateWidth() {
    startTransition(() => setKey(prev => prev + 1));
  }

  useLayoutEffect(() => {
    const resizeObserver = new ResizeObserver(updateWidth);
    if (document?.body) {
      resizeObserver.observe(document?.body);
    }
    return () => resizeObserver.disconnect();
  }, []);

  function onWheel(e: { deltaY: number }) {
    if (!showResetZoomButton) return;
    if (e?.deltaY > 0 && depthRef.current > 0) {
      depthRef.current -= 1;
    } else {
      depthRef.current += 1;
    }
    if (depthRef.current > 1) {
      chartRef?.current?.chart?.showResetZoom();
    }
    if (depthRef.current < 2) {
      chartRef?.current?.chart?.update(showMonthlyPeriod && props?.showTunedPeriod ? options : props?.options);
    }
  }

  return (
    <div className="chart-wrapper h-full" key={key} onWheel={onWheel}>
      <HighchartsReact
        immutable
        containerProps={{ className: "flex items-center justify-center h-full w-full" }}
        highcharts={Highcharts}
        ref={props?.refChart ?? chartRef}
        {...props}
        options={options}
      />
    </div>
  );
}

export default memo(HighChart);
export type HighChartsOptions = Options;
