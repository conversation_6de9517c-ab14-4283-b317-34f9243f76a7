import React from "react";

export interface IMenuItem<T> {
  id: string | number;
  title: (value?: T) => string;
  icon?: (value?: T) => React.ReactNode | JSX.Element;
  action: (value?: T) => void;
}

export interface IPosition {
  x: number;
  y: number;
}

export interface IClasses {
  container?: string;
  itemClass?: string;
}

export interface IMenuProps<T> {
  items: IMenuItem<T>[];
  rowData?: T;
  isOpen?: boolean;
  classes?: IClasses;
  onClose?: () => void;
  position?: IPosition;
}
