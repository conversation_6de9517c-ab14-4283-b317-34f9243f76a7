/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import { twMerge } from "tailwind-merge";
import { ClickAwayListener } from "@/components/atoms/clickAwayListener";

import { IMenuProps } from "./types";

function Menu<T>({ items, onClose, classes, isOpen, position, rowData }: IMenuProps<T>) {
  const onItemClick = (action: (value?: T) => void) => {
    action(rowData);
    setTimeout(() => {
      onClose?.();
    }, 0);
  };

  if (!isOpen) return null;

  return (
    <ClickAwayListener onClickAway={() => onClose?.()}>
      <ul
        className={twMerge(
          classes?.container,
          "fixed bg-backgroundCardBackground border border-borderBorderAndDivider flex min-w-[110px] flex-col gap-3 rounded-[4px]  px-[11px] py-[7px] text-xs text-smokeWhite shadow-menu"
        )}
        style={{
          top: position?.y || 0,
          left: position?.x || 0
        }}
      >
        {items.map(item => (
          <li
            key={item.id}
            className={twMerge(
              classes?.itemClass,
              "flex text-[10px]  w-full cursor-pointer items-center gap-2  rounded-md  hover:bg-lightBgBlue hover:text-primary"
            )}
            onClick={() => {
              onItemClick(item.action);
            }}
          >
            {item.icon && item.icon(rowData)}
            <span>{item.title(rowData)}</span>
          </li>
        ))}
      </ul>
    </ClickAwayListener>
  );
}

export default Menu;
