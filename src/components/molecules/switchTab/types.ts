import React from "react";
import { switchVariant } from "./utils";

export interface ISwitchTabProps {
  items: ISwitchTabItem[];
  initialSelect?: ISwitchTabItem;
  activeTab?: string | number;
  className?: string;
  itemClassName?: string;
  onSwitch?: ((v?: any) => void) | null;
  variant?: keyof typeof switchVariant;
}

export interface ISwitchTabItem {
  id: string | number;
  icon?: React.ReactNode;
  title?: string | JSX.Element;
  gap?: number;
}
