/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect, useRef } from "react";
import { twMerge } from "tailwind-merge";
import { switchVariant } from "./utils";

function TransitionEffect({
  active,
  padding,
  variant
}: {
  active: string | number;
  padding: number;
  variant: keyof typeof switchVariant;
}) {
  const glider = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!glider.current) {
      return;
    }

    const bg: HTMLDivElement = glider.current;
    const id = `chips-item-${active.toString()}`;
    const el = document.getElementById(id) as HTMLElement;
    const pos = el?.offsetLeft ? `${el.offsetLeft - padding}px` : "";

    bg.style.width = `${el?.clientWidth}px`;
    bg.style.transform = `translateX(${pos})`;
  }, [active]);

  return (
    <span
      ref={glider}
      className={twMerge(
        "glider transition ease-out duration-200 w-[64px] h-7 absolute left-2 z-0 rounded ",
        switchVariant[variant]
      )}
    />
  );
}

export default TransitionEffect;
