"use client";

/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable import/no-extraneous-dependencies */
import { twMerge } from "tailwind-merge";
import TransitionEffect from "./TransitionEffect";
import { ISwitchTabItem, ISwitchTabProps } from "./types";
import { switchVariantNotSelectedColor, switchVariantSelectedColor } from "./utils";

function SwitchTab(props: ISwitchTabProps) {
  const { items, activeTab, onSwitch, itemClassName, variant = "bordered" } = props;

  const onClickTab = (item: ISwitchTabItem) => {
    onSwitch?.(item);
  };

  if (!items?.length) return null;

  return (
    <>
      <div className={twMerge("flex py-0.5", props?.className)}>
        {items
          // ?.filter(i => i.title)
          .map(item => (
            <div
              id={`chips-item-${item?.id}`}
              key={item?.id}
              className={twMerge(
                "z-10 h-[26px] text-center flex justify-center items-center rounded-sm text-sm leading-4  cursor-pointer",
                switchVariantNotSelectedColor[variant],
                itemClassName,
                activeTab === item?.id && switchVariantSelectedColor[variant]
              )}
              onClick={() => onClickTab(item)}
              data-test={`chips-item-${item?.id}`}
            >
              {item?.title}
              {item?.icon || ""}
            </div>
          ))}
      </div>
      {activeTab && <TransitionEffect active={activeTab} padding={8} variant={variant} />}
    </>
  );
}

export default SwitchTab;
