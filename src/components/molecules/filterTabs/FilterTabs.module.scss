@mixin triangle($position-property) {
  height: 8px;
  width: 8px;
  left: 0;
  position: absolute;
  #{$position-property}: 100%;
  overflow: hidden;
}
@mixin beforeTriangle($border-radius, $shadow-color) {
  content: "";
  display: block;
  height: 100%;
  border-radius: $border-radius;
  box-shadow: 0 0 0 8px $shadow-color;
}

.topTriangleActive {
  @include triangle("bottom");
}

.topTriangleActive::before {
  @include beforeTriangle(0 0 0 50%, #343438);
}

.bottomTriangleActive {
  @include triangle("top");
}

.bottomTriangleActive::before {
  @include beforeTriangle(50% 0 0 0, #343438);
}

.topTriangleDeActive {
  @include triangle("bottom");
}

.topTriangleDeActive::before {
  @include beforeTriangle(0 0 0 50%, #3d3d3d);
}

.bottomTriangleDeActive {
  @include triangle("top");
}

.bottomTriangleDeActive::before {
  @include beforeTriangle(50% 0 0 0, #3d3d3d);
}
