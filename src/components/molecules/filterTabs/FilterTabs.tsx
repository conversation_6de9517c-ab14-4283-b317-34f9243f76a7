"use client";

/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable react-hooks/exhaustive-deps */

import { useEffect, useState } from "react";
import { twMerge } from "tailwind-merge";
import style from "./FilterTabs.module.scss";
import { ITabsItem, ITabsProps } from "./type";

function FilterTabs(props: ITabsProps) {
  const { items, selectedTab, onSwitchTab } = props;
  const [activeTab, setActiveTab] = useState(items[0]);

  const switchTab = (item: ITabsItem) => {
    setActiveTab(item);
    onSwitchTab?.(item);
  };

  useEffect(() => {
    const s = items.find(i => i.id === selectedTab);
    setActiveTab(s || items[0]);
  }, [selectedTab]);

  return (
    <div>
      {items?.map(item => (
        <div
          className={twMerge(
            "relative flex items-center justify-center py-3 rounded-r w-10 h-[40px] cursor-pointer",
            activeTab?.id === item?.id ? "bg-cardBackground z-50" : "bg-secondaryCardBackground z-40"
          )}
          onClick={() => switchTab(item)}
          data-test={`${item?.id}-tab`}
        >
          <div className={activeTab?.id === item?.id ? style.topTriangleActive : style.topTriangleDeActive} />
          <div className={twMerge(activeTab?.id === item?.id ? "text-mainText" : "text-secondaryText")}>
            {activeTab?.id === item?.id ? item?.activeIcon : item?.deActiveIcon}
          </div>
          <div className={activeTab?.id === item?.id ? style.bottomTriangleActive : style.bottomTriangleDeActive} />
        </div>
      ))}
    </div>
  );
}

export default FilterTabs;
