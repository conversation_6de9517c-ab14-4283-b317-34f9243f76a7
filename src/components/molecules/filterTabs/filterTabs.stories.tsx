import type { <PERSON>a, StoryObj } from "@storybook/react";
import Filter from "@/assets/icons/filterIconSecond.svg";
import DeActiveFilter from "@/assets/icons/deActiveFilterIcon.svg";
import Calculator from "@/assets/icons/calculator-bold.svg";
import DeActiveCalculator from "@/assets/icons/DeActiveCalculator.svg";
import { MENUITEMS } from "@/components/organisms/filterBox/util";
import FilterTabs from "./FilterTabs";

const meta: Meta<typeof FilterTabs> = {
  component: FilterTabs,
  title: "Components/Molecules/FilterTabs"
};

export default meta;
type Story = StoryObj<typeof FilterTabs>;

export const Default: Story = {
  args: {
    items: [
      {
        id: MENUITEMS.FILTER,
        activeIcon: <Filter className="w-[18px] h-[19px]" />,
        deActiveIcon: <DeActiveFilter className="w-[18px] h-[19px]" />
      },
      {
        id: MENUITEMS.SEARCH,
        activeIcon: <Calculator className="w-[18px] h-[18px]" />,
        deActiveIcon: <DeActiveCalculator className="w-[18px] h-[19px]" />
      }
    ]
  }
};
