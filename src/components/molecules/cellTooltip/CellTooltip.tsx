import HoverTooltip from "@/components/atoms/hoverTooltip/HoverTooltip";

interface ICellTooltipProps {
  title?: string;
  className?: string;
  titleLength: number;
  offset?: [number, number];
  tooltipClassName?: string;
}

function CellTooltip({ title, className, titleLength, tooltipClassName, offset }: ICellTooltipProps) {
  if (title && title?.length > titleLength) {
    return (
      <HoverTooltip content={title} tooltipClassName={tooltipClassName} offset={offset}>
        <div className={className}>{title}</div>
      </HoverTooltip>
    );
  }

  return <div className={className}>{title}</div>;
}

export default CellTooltip;
