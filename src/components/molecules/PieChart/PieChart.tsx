/* eslint-disable no-bitwise */
/* eslint-disable import/no-extraneous-dependencies */

"use client";

import { forwardRef, useImperativeHandle, useRef } from "react";
import { Tooltip } from "react-tooltip";
import { toFixed, setMinChartRange } from "@/utils/helpers";
import HighChart from "@/components/molecules/highChart/HighChart";
import { IPieChartProps } from "./types";
import { getOptions } from "./util";

const PieChart = forwardRef((props: IPieChartProps, ref) => {
  const { id, data = [], width, height, hasTooltipPercent, isTooltipEnabled = true } = props;
  const colors = ["#4895EF", "#4361EE", "#3F37C9", "#7209B7", "#B5179E", "#4CC9F0"];
  const tooltipRef = useRef<any>(null); // Assume Tooltip has a close() method

  useImperativeHandle(ref, () => ({
    onClose: () => {
      tooltipRef.current?.close?.();
    }
  }));

  if (data && data.length > colors.length) {
    data.forEach(() => {
      colors.push(`#${(((1 << 24) * Math.random()) | 0).toString(16).padStart(6, "0")}`);
    });
  }

  const tooltipData = data.map((i, k) => ({
    ...i,
    color: i.color ? i.color : colors[k]
  }));

  const chartData = data.map(i => ({ ...i, y: setMinChartRange(i.y) }));

  return (
    <>
      <div data-tooltip-id={id}>
        <HighChart width={width} height={height} options={getOptions({ data: chartData, colors, width, height })} />
      </div>

      {isTooltipEnabled && (
        <Tooltip
          id={id}
          ref={tooltipRef}
          place="right-start"
          float
          className="!p-3 !border !border-dark_black8 !bg-dark_black9 !rounded-lg min-w-[144px] !mx-2 z-[9999999]"
          classNameArrow="mt-2"
        >
          <div className="flex flex-col gap-2 leading-4 z-20">
            {tooltipData.map(i => (
              <div key={i?.name} className="flex gap-2 text-xs items-center">
                <span className="w-3 h-3 rounded-sm" style={{ background: i.color }} />
                <span>
                  {hasTooltipPercent && "%"}
                  {toFixed(i.y)} {i.name}
                </span>
              </div>
            ))}
          </div>
        </Tooltip>
      )}
    </>
  );
});

PieChart.displayName = "PieChart";

export default PieChart;
