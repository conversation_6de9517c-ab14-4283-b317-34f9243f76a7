/* eslint-disable import/prefer-default-export */

import { TChartOption } from "./types";

export const getOptions = (props: TChartOption) => {
  const { data = [], colors = [], width, height } = props;

  return {
    chart: {
      type: "pie",
      animation: false,
      backgroundColor: null,
      margin: 0,
      spacingTop: 0,
      spacingBottom: 0,
      spacingLeft: 0,
      spacingRight: 0,
      width,
      height
    },
    title: {
      text: ""
    },
    credits: {
      enabled: false
    },

    plotOptions: {
      pie: {
        dataLabels: {
          enabled: false
        },
        showInLegend: false,
        borderWidth: 0,
        animation: false
      }
    },
    colors,
    tooltip: {
      enabled: false
    },
    series: [
      {
        name: "",
        lineWidth: 0,
        marker: {
          enabled: false
        },
        border: 0,
        borderRadius: 0,
        groupPadding: 0,
        states: {
          hover: {
            enabled: false
          },
          inactive: {
            opacity: 1
          }
        },
        data
      }
    ]
  };
};
