import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import <PERSON><PERSON><PERSON> from "./PieC<PERSON>";

const meta: Meta<typeof PieChart> = {
  component: Pie<PERSON><PERSON>,
  title: "Components/Molecules/PieChart"
};

const data = [
  { name: "اوراق دولتی", y: 30, color: "#4361EE" },
  { name: "اوراق غیردولتی", y: 33 },
  { name: "سایر", y: 24 },
  { name: "سهام", y: 25 },
  { name: "صندوق", y: 28 },
  { name: "سپرده بانکی", y: 27 }
];

export default meta;
type Story = StoryObj<typeof PieChart>;

export const Default: Story = {
  args: {
    id: "chart1",
    width: 128,
    height: 128,
    data
  }
};
