export const getColumnChartOption = ({
  series,
  categories,
  tooltipEnabled = true,
  groupPadding,
  pointWidth,
  stacking = true,
  x = 0
}: {
  series: any;
  categories: string[];
  tooltipEnabled?: boolean;
  groupPadding?: number;
  pointWidth?: number;
  x?: number;
  stacking?: boolean;
}) => ({
  chart: {
    type: "column",
    backgroundColor: "transparent",
    animation: false,
    marginRight: 12,
    marginLeft: 29,
    marginBottom: 17,
    zooming: {
      type: "x",
      resetButton: {
        position: {
          x: 0,
          y: 3
        },
        theme: {
          fill: "#F4F4F4",
          stroke: "transparent",
          class: "zoom-button",
          border: 0,
          r: 4,
          style: {
            color: "#161616",
            fontSize: "14px",
            fontWeight: "400"
          }
        }
      }
    }
  },
  title: {
    text: ""
  },
  subtitle: {
    text: ""
  },
  xAxis: {
    categories,
    crosshair: true,
    labels: {
      useHTML: true,
      rotation: 0,
      x,
      y: 11,
      /* @ts-ignore */
      style: {
        color: "#F4F4F4",
        fontSize: "10px",
        fontWeight: "400",
        textOverflow: "none"
      },
      /* @ts-ignore */
      formatter() {
        /* @ts-ignore */
        const { value } = this;
        return `<span>${value}</span>`;
      }
    },
    lineColor: "#545454",
    lineWidth: 2
  },
  yAxis: {
    min: 0,
    title: false,
    lineColor: "#545454",
    lineWidth: 1,
    gridLineWidth: 0,
    endOnTick: false,
    max: 100,
    offset: 5,
    tickInterval: 25,
    labels: {
      useHTML: true,
      x: -5,
      /* @ts-ignore */
      // eslint-disable-next-line
      formatter: function () {
        /* @ts-ignore */
        const { value } = this;
        return `${value}%`;
      },
      style: {
        color: "#F4F4F4",
        fontFamily: "var(--font-yekan)",
        fontSize: "10px",
        width: 46
      }
    }
  },
  plotOptions: {
    column: {
      ...(stacking && { stacking: "normal" }),
      borderRadius: 0,
      borderWidth: 0,
      pointPadding: 0,
      ...(groupPadding !== undefined && { groupPadding }),
      dataLabels: {
        enabled: true,
        color: "#F4F4F4",
        verticalAlign: "top",
        y: -25,
        overflow: "none",
        inside: true,
        style: {
          fontSize: "10px",
          textShadow: false,
          textOutline: "none",
          fontFamily: "var(--font-yekan)"
        },
        formatter() {
          return this.y > 0 ? `${this.y}` : "";
        }
      },
      animation: false
    },
    series: {
      ...(pointWidth && { pointWidth })
    }
  },
  series,
  credits: {
    enabled: false
  },
  legend: {
    enabled: false
  },

  tooltip: {
    enabled: tooltipEnabled,
    backgroundColor: "#28282C",
    style: {
      color: "#F4F4F4"
    },
    /* @ts-ignore */
    formatter() {
      let s;
      /* @ts-ignore */
      if (this.point.name) {
        /* @ts-ignore */
        s = `${this.point.name} ${this.y}`;
      } else {
        /* @ts-ignore */
        s = `${this.x}: ${this.y}`;
      }
      return s;
    }
  }
});

export const setMinChartRange = (n: number) => (n > 0 && n < 1 ? 1 : n);

export const ORGANIZATION_STATUS_LABELS = {
  CRITICAL: "بحرانی ",
  HIGH: "بالا ",
  MODERATE: "متوسط ",
  LOW: "کم "
};
