import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { columnChartColorsDark } from "@/containers/cards/util";
import Column<PERSON>hart from "./ColumnChart";
import { getColumnChartOption, ORGANIZATION_STATUS_LABELS } from "./util";

const meta: Meta<typeof ColumnChart> = {
  component: ColumnChart,
  title: "Components/Molecules/ColumnChart"
};

const categories = [
  ORGANIZATION_STATUS_LABELS.LOW,
  ORGANIZATION_STATUS_LABELS.MODERATE,
  ORGANIZATION_STATUS_LABELS.HIGH,
  ORGANIZATION_STATUS_LABELS.CRITICAL
];

const series = [
  {
    data: [
      { y: 1, color: columnChartColorsDark[0] },
      { y: 2, color: columnChartColorsDark[1] },
      { y: 3, color: columnChartColorsDark[2] },
      { y: 4, color: columnChartColorsDark[3] }
    ]
  }
];

const options = getColumnChartOption({ series, categories });

export default meta;
type Story = StoryObj<typeof ColumnChart>;

export const Default: Story = {
  args: {
    options,
    className: "bg-black m-2 p-2 w-[500px]"
  }
};
