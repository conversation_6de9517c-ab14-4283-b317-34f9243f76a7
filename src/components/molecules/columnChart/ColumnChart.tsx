import { twMerge } from "tailwind-merge";
import Styles from "./columnChart.module.scss";
import { IColumnChartProps } from "./type";
import HighChart from "../highChart/HighChart";

function ColumnChart(props: IColumnChartProps) {
  const { options, className } = props;

  return (
    <div className={twMerge(Styles.columnChartWrapper, "items-center w-full", className)}>
      <HighChart showResetZoom={props?.showResetZoom} options={options} />
    </div>
  );
}

export default ColumnChart;
