.columnChartWrapper {
  :global {
    * {
      font-family: var(--font-yekan);
    }
    .highcharts-crosshair-category {
      display: none !important;
      visibility: hidden !important;
    }

    .tooltip {
      display: flex;
      flex-direction: column;
      justify-content: center;
      gap: 3px;
      min-width: 153px;
      padding: 12px;
      font-size: 10px;
      border-radius: 6px;
      color: #efefef;
      line-height: 24px;
      border: 0.5px solid #545454;
      background: #28282c;
      font-size: 12px;

      & > span.date {
        text-align: center;
        display: block;
        font-size: 10px;
      }

      & > div {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }

    .tooltip {
      span {
        display: flex;
        align-items: center;
        i {
          display: inline-block;
          width: 12px;
          height: 12px;
          margin-left: 8px;
          border-radius: 2px;
        }
      }
      b {
        line-height: 22px;
        font-size: 14px;
        max-width: 50px;
        overflow: hidden;
      }
    }
  }
}
