import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import RadioGroup from "./RadioGroup";

const meta: Meta<typeof RadioGroup> = {
  component: RadioGroup,
  title: "Components/Molecules/RadioGroup"
};

export default meta;
type Story = StoryObj<typeof RadioGroup>;

export const Default: Story = {
  args: {
    items: [
      { id: "1", label: "first", checked: false },
      { id: "2", label: "second", checked: true }
    ],
    className: "flex flex-col gap-4"
  }
};
