/* eslint-disable import/prefer-default-export */
export const bindBgVariants = {
  filledBlue: "bg-radiance600 group-hover:bg-azureRadiance800"
};

export const bindBorderVariants = {
  filledBlue: "border-radiance500 group-hover:border-azureRadiance800"
};

export const bindSizeVariants = {
  small: "w-4 h-4 rounded-sm border-[0.88px]",
  medium: "w-[18px] h-[18px] border-[1px]",
  large: "w-5 h-5 border-[1.11px]"
};

export const bindTickVariants = {
  small: "w-[10.66px] h-[10.66px]",
  medium: "w-3 h-3",
  large: "w-[13.3px] h-[13.3px]"
};

export const bindLabelVariants = {
  small: "text-[10px]",
  medium: "text-xs",
  large: "text-xs"
};
