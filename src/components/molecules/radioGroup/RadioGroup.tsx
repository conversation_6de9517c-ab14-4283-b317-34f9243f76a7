/* eslint-disable no-param-reassign */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable react/jsx-no-useless-fragment */
import { useMemo, useState } from "react";
import { twMerge } from "tailwind-merge";
import { IRadioButtonItem, IRadioButtonProps } from "./type";
import { bindBgVariants, bindBorderVariants, bindLabelVariants, bindSizeVariants, bindTickVariants } from "./utils";

function RadioGroup(props: IRadioButtonProps) {
  const { items, onSwitch, disabled, className, variant = "filledBlue", size = "large", id } = props;

  const [radioItems, setRadioItems] = useState(items);

  useMemo(() => {
    setRadioItems(items);
  }, [items]);

  const editIc = (editId: string) => {
    const clonedArray: IRadioButtonItem[] = JSON.parse(JSON.stringify(radioItems));
    const foundIndex = clonedArray?.findIndex(item => item?.id === editId);

    if (foundIndex !== -1) {
      clonedArray.forEach(item => {
        if (item?.checked) item.checked = false;
      });
      clonedArray[foundIndex] = { ...clonedArray[foundIndex], checked: true };
    }

    setRadioItems(clonedArray);
  };

  const switchButton = (item: IRadioButtonItem) => {
    editIc(item?.id);
    onSwitch?.(item);
  };

  return (
    <div className={twMerge(className)}>
      {radioItems?.map(item => (
        <>
          <input id={item?.id} type="radio" hidden onClick={() => !disabled && switchButton(item)} />
          <label htmlFor={item?.id} data-test={`radio-${id || ""}-item-${item?.id}`}>
            <div className="flex gap-1 group items-center">
              <div
                className={twMerge(
                  bindBorderVariants[variant],
                  bindSizeVariants[size],
                  "flex items-center justify-center border-solid rounded-full shrink-0",
                  disabled && "border-textDisabled group-hover:border-textDisabled",
                  !disabled && "cursor-pointer"
                )}
              >
                <div
                  className={twMerge(
                    bindTickVariants[size],
                    "rounded-full",
                    item?.checked && bindBgVariants[variant],
                    disabled && "bg-textDisabled group-hover:bg-textDisabled",
                    disabled && !item?.checked && "hidden"
                  )}
                />
              </div>
              <div
                className={twMerge(
                  bindLabelVariants[size],
                  "leading-4 text-[#EFEFEF] flex items-center whitespace-nowrap text-xs ",
                  disabled && "text-textDisabled",
                  !disabled && "cursor-pointer"
                )}
              >
                {item?.label}
              </div>
            </div>
          </label>
        </>
      ))}
    </div>
  );
}

export default RadioGroup;
