import { bindBgVariants, bindBorderVariants, bindSizeVariants } from "./utils";

export interface IRadioButtonProps {
  items: IRadioButtonItem[];
  onSwitch?: ((v?: any) => void) | null;
  disabled?: boolean;
  className?: string;
  variant?: keyof typeof bindBgVariants | keyof typeof bindBorderVariants;
  size?: keyof typeof bindSizeVariants;
  id?: string;
}

export interface IRadioButtonItem {
  id: string;
  label: string;
  checked?: boolean;
  value?: any;
}
