import Calendar from "@/assets/icons/calendar-filter.svg";
import { DatePickerWrapper } from "@/components/organisms/datePicker";
import { IDatePickerWrapperRefProps } from "@/components/organisms/datePicker/types";
import RangeMonthPickerWrapper from "@/components/organisms/monthDatePicker/RangeMonthPickerWrapper";
import { TRangeValue } from "@/components/organisms/monthDatePicker/types";
import { getNow } from "@/containers/tradeHistory/util";
import { useVarDateFilterStore } from "@/store/varDateFilter";
import { TActiveTab } from "@/store/varDateFilter/useVarDateFilterStore";
import { dateConverter } from "@/utils/DateHelper";
import { memo, useCallback, useEffect, useMemo, useRef, useState } from "react";
import SwitchTab from "../switchTab/SwitchTab";
import { IFilterTimeTab } from "./types";
import { FILTER_TIME, getPrevDate } from "./utils";

function DateFilter({
  defaultTimeKey,
  showDatePickerMonthly
}: {
  defaultTimeKey?: { id: string; title: string };
  showDatePickerMonthly?: boolean;
}) {
  const datePickerRef = useRef<IDatePickerWrapperRefProps>(null);
  const [initialDate, setInitialDate] = useState<Date[] | undefined>();
  const { setPeriod, setActiveTab, activeTab } = useVarDateFilterStore();

  const onSwitch = useCallback(
    (v: IFilterTimeTab, firstRender?: boolean) => {
      // using if to stop repetitive user click

      if (v.id !== activeTab?.id || firstRender) {
        // convert enum to date
        const date = getPrevDate(v.id);
        const now = getNow();
        setActiveTab(v as TActiveTab);

        if (v.id !== FILTER_TIME.DateRange) {
          // todo fix next line
          setPeriod({ fromDate: date, toDate: now });
          setInitialDate(undefined);
        }
      }
    },
    [activeTab?.id]
  );

  useEffect(() => {
    if (defaultTimeKey) {
      setActiveTab(defaultTimeKey);
      const date = getPrevDate(defaultTimeKey?.id);
      setPeriod({ fromDate: date });
    }
  }, []);

  const onDateChange = (e: TRangeValue) => {
    setPeriod({ fromDate: e?.[0], toDate: e?.[1] });
  };

  const filterTimeItems = useMemo(
    () => [
      {
        id: FILTER_TIME.DateRange,
        icon: !showDatePickerMonthly ? (
          <DatePickerWrapper
            isRange
            ref={datePickerRef}
            closeOnConfirm={false}
            hasFooter
            initialValue={initialDate}
            config={{
              locale: "fa-IR",
              weekends: ["friday"],
              maxDate: new Date()
            }}
            className="-top-[6px]"
            onConfirm={(date?: Date[]) => {
              if (date && date[0] && date[1] && date !== initialDate) {
                setInitialDate(date);
                setPeriod({
                  fromDate: dateConverter(date[0]).format("YYYY-MM-DD"),
                  toDate: dateConverter(date[1]).format("YYYY-MM-DD")
                });

                datePickerRef.current?.close();

                const f = filterTimeItems.find(i => i.id === FILTER_TIME.DateRange);

                if (f) {
                  onSwitch(f);
                }
              }
            }}
            onCancel={() => {
              setInitialDate(undefined);
              onSwitch(filterTimeItems[1]);
            }}
          >
            <Calendar />
          </DatePickerWrapper>
        ) : (
          <RangeMonthPickerWrapper onDateSelect={onDateChange} initialValue={undefined}>
            <Calendar />
          </RangeMonthPickerWrapper>
        )
      },
      { id: FILTER_TIME.Month1, title: "1 ماه" },
      { id: FILTER_TIME.Month3, title: "3 ماه" },
      { id: FILTER_TIME.Month6, title: "6 ماه" },
      { id: FILTER_TIME.Yearly, title: "1 سال" }
    ],
    [initialDate, onSwitch]
  );
  return (
    <div className="relative px-0.5 py-px flex items-center bg-[#28282C] rounded w-fit z-500">
      <SwitchTab
        items={filterTimeItems}
        activeTab={activeTab?.id as string}
        onSwitch={v => {
          onSwitch(v);
          if (v.id === FILTER_TIME.DateRange) datePickerRef.current?.open();
        }}
        itemClassName="w-[56px] [&:nth-child(1)]:w-[28px] !rounded-[2px]"
      />
    </div>
  );
}

export default memo(DateFilter);
