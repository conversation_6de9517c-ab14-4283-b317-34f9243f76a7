import dayjs from "dayjs";

// eslint-disable-next-line @typescript-eslint/naming-convention
export enum FILTER_TIME {
  Yearly = "1year",
  Month6 = "6month",
  Month3 = "3month",
  Month1 = "1month",
  DateRange = "dateRange"
}

export const getPrevDate = (time: string | number) => {
  const f = "YYYY-MM-DD";

  switch (time) {
    case FILTER_TIME.Month1:
      return dayjs().subtract(1, "month").format(f);
    case FILTER_TIME.Month3:
      return dayjs().subtract(3, "month").format(f);
    case FILTER_TIME.Month6:
      return dayjs().subtract(6, "month").format(f);
    case FILTER_TIME.Yearly:
      return dayjs().subtract(1, "year").format(f);
    default:
      return dayjs().format(f);
  }
};
