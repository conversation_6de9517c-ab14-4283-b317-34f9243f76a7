import { numberWithCommas } from "@/utils/helpers";
import { IPriceYtmProps } from "./type";

function PriceYtm({ price, ytm }: IPriceYtmProps) {
  return (
    <div className=" text-base mt-1 mr-2">
      {price ? (
        <>
          {numberWithCommas(price)} <br />
          <p className="text-xs">٪{ytm?.toFixed(3)}</p>
        </>
      ) : (
        "---"
      )}
    </div>
  );
}

export default PriceYtm;
