import Spinner from "@/assets/spinner.svg";
import { twJoin, twMerge } from "tailwind-merge";

import { bindShadowVariants, bindSizeVariant, bindSpinnerSize, bindVariants } from "@/components/atoms/button/utils";
import IButtonProps from "./types";

export default function Button(props: IButtonProps) {
  const {
    className,
    children,
    isLoading,
    variant = "fill",
    size = "medium",
    disabled = false,
    startAdornment,
    endAdornment
  } = props;

  return (
    <button
      type="button"
      {...props}
      disabled={disabled}
      className={
        twMerge(
          bindSizeVariant[size],
          bindVariants[variant],
          "flex items-center justify-center disabled:cursor-not-allowed",
          isLoading ? "px-2" : "",
          className,
          startAdornment ? "pl-6 pr-4" : "",
          endAdornment ? "pl-4 pr-6" : "",
          typeof children === "string" ? "" : "px-4"
        ) + twJoin(bindShadowVariants[variant])
      }
    >
      {isLoading && (
        <div className="flex items-center justify-center">
          <Spinner className={twMerge("animate-spin p-0.5 ", bindSpinnerSize[size])} />
        </div>
      )}
      {!isLoading && typeof children === "string" ? (
        <div className="flex items-center gap-2">
          {startAdornment && <div>{startAdornment}</div>}
          <div
            className={twMerge(
              !startAdornment && !endAdornment && "min-w-[66px]",
              (startAdornment || endAdornment) && size === "small" && "min-w-[29px]",
              (startAdornment || endAdornment) && size !== "small" && "min-w-[33px]"
            )}
          >
            {children}
          </div>
          {endAdornment && <div>{endAdornment}</div>}
        </div>
      ) : (
        <div>{children}</div>
      )}
    </button>
  );
}
