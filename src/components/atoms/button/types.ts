import { DetailedHTMLProps, ButtonHTMLAttributes, ReactNode } from "react";
import { bindSizeVariant, bindVariants } from "./utils";

interface IButtonProps extends DetailedHTMLProps<ButtonHTMLAttributes<HTMLButtonElement>, HTMLButtonElement> {
  isLoading?: boolean;
  disabled?: boolean;
  variant?: keyof typeof bindVariants;
  startAdornment?: ReactNode;
  endAdornment?: ReactNode;
  size?: keyof typeof bindSizeVariant;
}

export default IButtonProps;
