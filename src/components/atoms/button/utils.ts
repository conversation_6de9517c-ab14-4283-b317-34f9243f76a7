/* eslint-disable import/prefer-default-export */

export const bindVariants = {
  fill: "text-mainText bg-semanticPrimary  hover:text-mainText hover:bg-radiance800 active:text-mainText active:bg-radiance800 focus:text-mainText focus:bg-semanticPrimary disabled:bg-textDisabled disabled:text-secondaryText curser-pointer",
  outLine:
    "text-mainText bg-transparent hover:text-mainText  hover:bg-radiance800  active:text-mainText  active:bg-radiance800 focus:text-mainText focus:bg-transparent disabled:text-secondaryText disabled:bg-transparent curser-pointer",
  noFrame:
    " text-mainText bg-transparent hover:text-mainText hover:bg-radiance800 active:text-mainText active:bg-radiance800 focus:text-mainText disabled:text-secondaryText curser-pointer disabled:bg-transparent ",
  outLineWhite:
    " text-mainText bg-transparent hover:text-mainText hover:bg-radiance800 active:text-mainText active:bg-radiance800 focus:text-mainText focus:bg-transparent disabled:text-secondaryText disabled:bg-transparent curser-pointer"
};

export const bindSizeVariant = {
  large: "rounded-[4px] text-base px-3 py-3",
  medium: "rounded-[4px] text-base py-2 px-3",
  small: "rounded-[4px] text-[13px] leading-4 px-3 py-2"
};

export const bindSpinnerSize = {
  large: "h-6 w-6",
  medium: "h-6 w-6",
  small: "h-[17px] w-[17px]"
};

type ButtonVariants = { [k in keyof typeof bindVariants]: string };

export const bindShadowVariants = {
  fill: " active:outline active:outline-2 active:outline-radiance900 disabled:outline-none",
  outLine:
    " shadow-shadow1px shadow-radiance600 hover:shadow-shadow1px hover:shadow-radiance600 active:shadow-none active:outline active:outline-2 active:outline-radiance900 focus:shadow-shadow1px focus:shadow-radiance600 disabled:shadow-shadow1px disabled:outline-none disabled:shadow-textDisabled",
  noFrame: " active:outline active:outline-2 active:outline-radiance900 disabled:outline-none",
  outLineWhite:
    " shadow-shadow1px shadow-mainText hover:shadow-shadow1px hover:shadow-radiance600 active:shadow-none active:outline active:outline-2 active:outline-radiance900 focus:shadow-shadow1px focus:shadow-mainText disabled:shadow-shadow1px disabled:outline-none disabled:shadow-textDisabled"
} as ButtonVariants;
