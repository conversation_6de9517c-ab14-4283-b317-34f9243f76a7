import { useDescription, useTsController } from "@ts-react/form";

import { TInputProps } from "./types";
import Input from "./Input";

export default function InputNumberForm(props: TInputProps) {
  const {
    field: { onChange, value },
    error
  } = useTsController<number>();
  const { label } = useDescription();

  return (
    <Input<number>
      {...props}
      type="number"
      title={label}
      value={value}
      onClear={() => onChange(undefined)}
      onChange={e => onChange(e)}
      errorMessage={error?.errorMessage}
      isClearable={false}
    />
  );
}
