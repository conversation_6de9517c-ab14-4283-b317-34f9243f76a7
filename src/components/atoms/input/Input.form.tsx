import { useDescription, useTsController } from "@ts-react/form";
import Input from "./Input";
import { TInputProps } from "./types";

export default function InputForm(props: TInputProps) {
  const {
    field: { onChange, value },
    error
  } = useTsController<string>();
  const { label } = useDescription();

  return (
    <Input
      {...props}
      title={label}
      value={value}
      onClear={() => onChange("")}
      onChange={(e: string) => onChange(e)}
      errorMessage={error?.errorMessage || props?.errorMessage}
    />
  );
}
