/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
import { useDescription, useTsController } from "@ts-react/form";

import { TInputProps } from "./types";
import Input from "./Input";

export default function PasswordForm(props: TInputProps) {
  const {
    field: { onChange, value },
    error
  } = useTsController<string>();

  const { label } = useDescription();

  return (
    <div className="relative">
      <Input
        {...props}
        type="password"
        title={label}
        value={value || ""}
        onClear={() => onChange("")}
        clearIconClassName="left-12"
        onChange={(e: any) => onChange(e)}
        errorMessage={error?.errorMessage || props?.errorMessage}
      />
    </div>
  );
}
