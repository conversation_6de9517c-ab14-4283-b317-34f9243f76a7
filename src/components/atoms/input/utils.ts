/* eslint-disable import/prefer-default-export */

export const bindSizeInputVariants = {
  small: "py-2 text-xs leading-4 ",
  medium: "py-2 text-[14px] leading-6",
  large: "text-[14px] leading-6 py-3"
};

export const bindSizeInputWrapperVariants = {
  small: "px-2 text-xs",
  medium: "pl-2 pr-3 text-[14px]",
  large: "pl-2 pr-3 text-[14px]"
};

export const bindSizeInputHintVariants = {
  small: "text-[8px] ps-2",
  medium: "text-.8xs ps-3",
  large: "text-.8xs ps-3"
};
