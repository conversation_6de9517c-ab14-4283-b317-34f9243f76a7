import type { <PERSON>a, StoryObj } from "@storybook/react";
import Input from "./Input";
import { bindSizeInputVariants } from "./utils";

const meta: Meta<typeof Input> = {
  component: Input,
  title: "Components/atoms/Input"
};

export default meta;
type Story = StoryObj<typeof Input>;

export const Default: Story = {
  args: {
    title: "test title",
    wrapperClassName: bindSizeInputVariants.large
  }
};
