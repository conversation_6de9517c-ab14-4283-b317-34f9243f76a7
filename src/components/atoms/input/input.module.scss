.input_wrapper {
  align-items: center;
  width: 100%;
  height: auto;
  position: relative;
  display: inline-block;
  border-radius: 4px;
  /* border: 1px solid #f1f4f4; */

  * {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
  }

  fieldset {
    text-align: right;
    position: absolute;
    inset: -5px 0px 0px;
    margin: 0px;
    padding: 0px 8px;
    pointer-events: none;
    overflow: hidden;
    min-width: 0%;

    legend {
      float: unset;
      width: auto;
      overflow: hidden;
      display: block;
      padding: 0 0px;
      height: 11px;
      font-size: 0.5em;
      visibility: hidden;
      max-width: 0.01px;
      transition: max-width 50ms cubic-bezier(0, 0, 0.2, 1) 0ms;
      white-space: nowrap;

      span {
        padding-left: 5px;
        padding-right: 5px;
        display: inline-block;
        opacity: 0;
        visibility: visible;
        font-size: 10px;
      }
    }
  }

  input,
  textarea {
    box-shadow: none;
    &:-webkit-autofill,
    &:-webkit-autofill:hover,
    &:-webkit-autofill:focus,
    &:-webkit-autofill:active {
      //-webkit-box-shadow: 0 0 0 30px white inset !important;
      -webkit-box-shadow: none !important;
    }
    &[data-autocompleted] {
      background-color: transparent !important;
    }
  }

  input,
  textarea,
  .fix-placeholder {
    outline: none;
    &:hover {
      & ~ label {
        //color: #2976e0;
      }
      & ~ fieldset {
        border-color: #1458b9;
        border-width: 2px;
      }
    }
    &:focus {
      & ~ fieldset {
        border-color: #164d92;
        border-width: 2px;
      }
    }
    .fix-placeholder {
      transform: translateY(0);
    }
    &:-webkit-autofill ~ label {
      font-size: 10px;
      top: 0;
      right: 9px;
      padding: 0px 5px 0px 5px;
      color: #cce6fd;
    }
    &:-webkit-autofill ~ fieldset {
      & > legend {
        max-width: initial;
      }
    }
    &:focus ~ label,
    &:not(:placeholder-shown) ~ label,
    &:-webkit-autofill ~ label {
      font-size: 10px;
      top: 0;
      right: 9px;
      padding: 0px 4px 0px 4px;
      color: #cce6fd;
    }

    &:focus ~ fieldset,
    &:not(:placeholder-shown) ~ fieldset,
    &:-webkit-autofill ~ fieldset {
      & > legend {
        max-width: initial;
      }
    }

    &:not(:focus) {
      &:not(:placeholder-shown) ~ fieldset {
        border-color: #f4f4f4;
        border-width: 1px !important;
      }
    }
  }

  label {
    position: absolute;
    top: 50%;
    height: auto;
    transform: translateY(-50%);
    transition: 0.2s ease all;
    -moz-transition: 0.2s ease all;
    -webkit-transition: 0.2s ease all;
    pointer-events: none;
  }

  textarea + label {
    top: 30%;
  }

  div.fix-placeholder {
    & > div {
      border: 0;
      flex-grow: 1;
    }
  }

  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
  input[type="number"] {
    -moz-appearance: textfield;
  }
}
/*
.keyPressEvent {
  input,
  textarea,
  .fix-placeholder {
    &:focus {
      outline: none;
      & ~ label {
        color: #2976e0;
      }
      & ~ fieldset {
        border-color: #2976e0;
      }
    }
    .fix-placeholder {
      transform: translateY(0);
    }

    &:focus ~ label,
    &:not(:placeholder-shown) ~ label,
    &:-webkit-autofill ~ label {
      font-size: 10px;
      top: 0;
      right: 9px;
      padding: 0px 5px 0px 5px;
      color: #cce6fd;
    }

    &:focus ~ fieldset,
    &:not(:placeholder-shown) ~ fieldset,
    &:-webkit-autofill ~ fieldset {
      & > legend {
        max-width: initial;
      }
    }

    &:not(:focus) {
      &:not(:placeholder-shown) ~ fieldset {
        border-color: #f4f4f4;
      }
    }
  }
}
*/
