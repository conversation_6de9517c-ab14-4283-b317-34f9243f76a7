import { DetailedHTMLProps, InputHTMLAttributes, ReactNode } from "react";
import { IInputWrapperProps } from "../inputWrapper/types";
import { bindSizeInputVariants } from "./utils";

export type TInputProps<InputType extends string | number = string> = Omit<
  DetailedHTMLProps<InputHTMLAttributes<HTMLInputElement>, HTMLInputElement>,
  "onChange"
> & {
  isRequired?: boolean;
  title?: string;
  autoComplete?: string;
  errorMessage?: string;
  clearIconClassName?: string;
  isDisabled?: boolean;
  isSuccess?: boolean;
  isError?: boolean;
  isClearable?: boolean;
  preserveErrorMessage?: boolean;
  inputWrapperProps?: IInputWrapperProps;
  hint?: ReactNode;
  placeHolderEndAdornment?: ReactNode;
  startAdornment?: ReactNode;
  endAdornment?: ReactNode;
  inputSize?: keyof typeof bindSizeInputVariants;
  wrapperClassName?: string;
  inputWrapperClassName?: string;
  onClear?: (value?: string) => void;
  onChange?: (v: InputType) => void;
  "data-test"?: string;
  max?: number;
  min?: number;
  placeHolder?: string;
};
