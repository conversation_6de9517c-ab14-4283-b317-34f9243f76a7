/* eslint-disable spaced-comment */
/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import Close from "@/assets/closeCircle.svg";
import EyeClose from "@/assets/eye-close.svg";
import Eye<PERSON><PERSON> from "@/assets/eye-open.svg";
import { InputWrapper } from "@/components/atoms/inputWrapper";
import { KeyboardEvent, Ref, forwardRef, useImperativeHandle, useRef, useState } from "react";
import { twMerge } from "tailwind-merge";
import { TInputProps } from "./types";
import { bindSizeInputVariants } from "./utils";

const Input = forwardRef(<T extends string | number>(props: TInputProps<T>, ref: Ref<HTMLInputElement | null>) => {
  const {
    title,
    value,
    type,
    hint,
    className,
    errorMessage,
    preserveErrorMessage = true,
    inputWrapperProps,
    isDisabled = false,
    isClearable = false,
    isSuccess,
    isError,
    clearIconClassName,
    startAdornment,
    endAdornment,
    isRequired,
    onClear,
    onChange,
    placeHolderEndAdornment,
    wrapperClassName,
    inputWrapperClassName = "",
    inputSize = "medium",
    max,
    min,
    placeHolder
  } = props;

  const [showPassword, setShowPassword] = useState(false);
  const refInput = useRef<HTMLInputElement>(null);

  useImperativeHandle(ref, () => refInput.current);

  //add comma to value if type equals to number
  const valWithComma = value && type === "number" ? `${value}`?.replace(/\B(?=(\d{3})+(?!\d))/g, ",") : value;

  const onInputChange = (e: any) => {
    if (type === "number") {
      if (
        (min && Number(e.target.value.replace(/,/g, "")) < min) ||
        (max && Number(e.target.value.replace(/,/g, "")) > max)
      )
        return;

      //remove comma to value if type equals to number
      const returnValue = e.target.value.replace(/,/g, "");
      onChange?.(returnValue as T);
    } else {
      onChange?.(e.target.value);
    }
  };

  const closeClick = () => {
    onClear?.("");
    if (refInput.current) {
      refInput.current.focus();
    }
  };

  // to avoid type string for tel inputs
  const onKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    const reg = /^[0-9\b]+$/;
    const allowed = ["Enter", "Backspace", "Delete", "Home", "End", "ArrowLeft", "ArrowRight"];

    if (type === "tel" && !allowed.includes(e.key) && !reg.test(e.key)) {
      e.preventDefault();
    }
  };

  let icon = null;
  let iconButton = null;

  // startAdornment (a react node which is placed before the input)
  if (startAdornment && !isClearable && type !== "password") {
    icon = startAdornment;
  } else if (startAdornment || type === "password" || (isClearable && value)) {
    icon = (
      <div className="flex items-center gap-1">
        {type === "password" && (
          <div
            className="top-2 flex cursor-pointer items-center justify-center gap-3"
            onClick={() => setShowPassword(prev => !prev)}
            data-test={`${props?.["data-test"]}-show-pass-icon`}
          >
            <div className="h-4 w-px bg-grayBorder" />
            {showPassword ? <EyeOpen /> : <EyeClose />}
          </div>
        )}
        {isClearable && value && (
          <span className=" flex cursor-pointer items-center justify-center">
            <Close
              className={clearIconClassName}
              onClick={closeClick}
              data-test={`${props?.["data-test"]}-clear-icon`}
            />
          </span>
        )}
        {startAdornment && startAdornment}
      </div>
    );
  }

  // endAdornment (a react node which is placed after the input)
  if (endAdornment && type !== "password") {
    iconButton = endAdornment;
  }

  return (
    <InputWrapper
      title={title}
      hint={hint}
      isRequired={isRequired}
      isDisabled={isDisabled}
      isSuccess={isSuccess}
      isError={isError}
      errorMessage={errorMessage}
      textLength={typeof value === "string" ? value?.length : 0}
      placeHolderEndAdornment={placeHolderEndAdornment}
      preserveErrorMessage={preserveErrorMessage}
      startAdornment={icon}
      endAdornment={iconButton}
      className={wrapperClassName}
      inputWrapperClassName={inputWrapperClassName}
      inputSize={inputSize}
      {...inputWrapperProps}
    >
      <input
        {...props}
        ref={refInput}
        value={valWithComma ?? ""}
        placeholder={placeHolder ?? " "}
        disabled={isDisabled}
        type={!showPassword ? (type === "number" ? "text" : type) : "text"}
        className={twMerge(
          bindSizeInputVariants[inputSize],
          "input input-bordered relative top-[1px]  w-full grow border-0 bg-transparent text-right text-white disabled:bg-transparent",
          isError && "caret-mediumRed",
          className
        )}
        onKeyDown={onKeyDown}
        onChange={e => onInputChange(e)}
      />
    </InputWrapper>
  );
}) as <T extends string | number>(props: TInputProps<T>, ref: Ref<HTMLInputElement | null>) => JSX.Element;

export default Input;
