import React, { useRef, useEffect } from "react";
import { IClickAwayListenerProps } from "./types";

function ClickAwayListener({ children, onClickAway, elementProps }: IClickAwayListenerProps) {
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (ref.current && !ref.current.contains(event.target as Node)) {
        onClickAway(event);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [onClickAway]);

  return (
    <div {...elementProps} ref={ref}>
      {children}
    </div>
  );
}

export default ClickAwayListener;
