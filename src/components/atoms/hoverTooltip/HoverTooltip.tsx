import Tippy from "@tippyjs/react";
import React, { ReactElement, ReactNode } from "react";
import { twMerge } from "tailwind-merge";

function HoverTooltip({
  children,
  content,
  offset,
  tooltipClassName
}: {
  offset?: [number, number];
  children: ReactElement;
  content?: ReactNode;
  tooltipClassName?: string;
}) {
  return (
    <Tippy
      zIndex={9999}
      maxWidth="none"
      className={twMerge(
        "border border-[#545454] mb-[-41px] w-[422px] !rounded-b-lg !rounded-t-none",
        "!overflow-visible",
        tooltipClassName
      )}
      content={content}
      placement="top-end"
      delay={100}
      arrow={false}
      interactive
      appendTo={document.body} // Append to body to avoid overflow constraints
      offset={offset || [0, 5]}
    >
      {children}
    </Tippy>
  );
}

export default HoverTooltip;
