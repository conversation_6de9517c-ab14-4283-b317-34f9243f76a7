import type { Meta, StoryObj } from "@storybook/react";
import Tooltip from "./Tooltip";

const RenderContent = <div>در محاسبه میان گین ساده YTM اوراق تا سررسید زیر 6 ماه در نظر گرفته نشده اند .</div>;

const meta: Meta<typeof Tooltip> = {
  component: Tooltip,
  title: "Components/atoms/Tooltip"
};

export default meta;
type Story = StoryObj<typeof Tooltip>;

export const Default: Story = {
  args: {
    content: RenderContent,
    placement: "left",
    children: (
      <div className="w-4 mt-[200px] h-4 mx-auto rounded-full text-center text-xs py-0.5 bg-gray-500 m-2 text-white">
        i
      </div>
    )
  }
};
