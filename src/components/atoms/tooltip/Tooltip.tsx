import Tippy from "@tippyjs/react";
import { twMerge } from "tailwind-merge";
import "tippy.js/dist/tippy.css";
import { ITooltipProps } from "./types";
import styles from "./Tooltip.module.scss";

function Tooltip({ children, content, variant = "default", ...restProps }: ITooltipProps) {
  return (
    <Tippy
      {...restProps}
      content={content}
      animation="fade"
      className={twMerge(
        "bg-[#28282C] !rounded-sm border-1 w-[218px] border-[#545454] leading-4 text-white px-2 py-1 !text-[10px]",
        restProps?.className,
        variant === "orange" && styles.orange,
        variant === "green" && styles.green
      )}
    >
      {children}
    </Tippy>
  );
}

export default Tooltip;
