"use client";

import React from "react";
import Loading from "@/assets/spinner.svg";
import { twMerge } from "tailwind-merge";

interface ISpinnerProps {
  className?: string;
  svgClassName?: string;
}

function Spinner({ svgClassName, className }: ISpinnerProps) {
  return (
    <div className={twMerge("flex items-center justify-center min-w-28", className)}>
      <Loading className={twMerge("h-6 w-6 animate-spin p-0.5 ", svgClassName)} />
    </div>
  );
}

export default Spinner;
