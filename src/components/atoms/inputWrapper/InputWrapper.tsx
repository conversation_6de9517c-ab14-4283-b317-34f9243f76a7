/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/label-has-associated-control */
import { isValidElement, useState } from "react";
import { twMerge } from "tailwind-merge";
import InfoIcon from "@/assets/info-silver.svg";
import SuccessIcon from "@/assets/success-login.svg";
import FieldTitle from "@/components/atoms/fieldTitle";

import styles from "../input/input.module.scss";
import { IInputWrapperProps } from "./types";
import { bindSizeInputHintVariants, bindSizeInputWrapperVariants } from "../input/utils";

export default function InputWrapper({
  title,
  children,
  errorMessage,
  preserveErrorMessage,
  titleProps,
  labelClass,
  borderClass,
  hint,
  startAdornment,
  endAdornment,
  isSuccess,
  isDisabled,
  isRequired,
  isError,
  textLength,
  placeHolderEndAdornment,
  disableAnimated,
  inputSize = "medium",
  inputWrapperClassName,
  hintClassName,
  ...restProps
}: IInputWrapperProps) {
  const hasError = isError || !!errorMessage;
  const right = endAdornment ? "right-[33px]" : "right-[12px]";

  const [isFocus, setIsFocus] = useState(false);

  const [isKeyDown, setIsKeyDown] = useState(false);

  const renderHint = isValidElement(hint) ? (
    hint
  ) : (
    <div
      className={twMerge(
        "text-darkGrayScale mt-1 flex gap-1 text-xs ",
        isSuccess && "text-mainGreen",
        isDisabled && "text-secondaryText"
      )}
    >
      {!isSuccess ? (
        <InfoIcon className={twMerge("text-dark_black2 h-4 w-4", isDisabled && "text-secondaryText")} />
      ) : (
        <SuccessIcon className="h-4 w-4" />
      )}
      {hint}
    </div>
  );

  return (
    <div
      {...restProps}
      className={twMerge("relative mb-2", restProps?.className)}
      onFocus={() => setIsFocus(true)}
      onBlur={() => setIsFocus(false)}
      onKeyDown={() => setIsKeyDown(true)}
    >
      <div
        className={twMerge(
          "!flex w-full",
          inputSize && bindSizeInputWrapperVariants[inputSize],
          inputWrapperClassName,
          styles.input_wrapper,
          isKeyDown && textLength && !disableAnimated && styles.keyPressEvent,
          isFocus && "!border-[#0C82F9]",
          hasError && "!border-warningBorder"
        )}
      >
        {endAdornment && <div className="pl-1">{endAdornment}</div>}

        {children}
        <label
          className={twMerge(
            "bg-darkBlue flex items-center text-sm font-normal text-[#BDBDBD]",
            right,
            labelClass,
            isSuccess && "!border-green-900",
            isDisabled && "!border-textDisabled !text-textDisabled",
            hasError && "!border-warningBorder",
            disableAnimated && isKeyDown && textLength && "hidden",
            inputSize === "small" && "text-xs"
          )}
        >
          <div className="flex gap-[2px]">
            {title}
            {placeHolderEndAdornment && placeHolderEndAdornment}
          </div>
          {!!isRequired && (
            <p className={twMerge("text-lg mt-2 text-warningText", (!isKeyDown || !textLength) && "pr-1")}>*</p>
          )}
        </label>
        <fieldset
          className={twMerge(
            " rounded-[4px] border ",
            textLength ? "border-borderBorderAndDivider" : "border-[#BDBDBD]",
            borderClass,
            isSuccess && "!border-green-900",
            isDisabled && "!border-textDisabled !text-secondaryText",
            hasError && "!border-warningBorder"
          )}
        >
          <legend className="text-base">
            {title && (
              <span>
                <div className="flex gap-[2px]">
                  {title}
                  {placeHolderEndAdornment && placeHolderEndAdornment}
                </div>
              </span>
            )}
            {!!isRequired && <span />}
          </legend>
        </fieldset>
        {startAdornment && <div> {startAdornment}</div>}
      </div>
      {/* ------------------------------ error message ----------------------------- */}
      {hint && !hasError && renderHint}
      {!hasError && !hint && preserveErrorMessage && <div className="h-4" />}
      {hasError && (
        <FieldTitle
          className={twMerge(
            "flex h-4 items-center gap-1 text-[10px] !text-warningText",
            bindSizeInputHintVariants[inputSize],
            hintClassName
          )}
        >
          {errorMessage || hint}
        </FieldTitle>
      )}
    </div>
  );
}
