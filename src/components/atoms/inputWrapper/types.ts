import { IFieldTitleProps } from "@/components/atoms/fieldTitle/types";
import { bindSizeInputVariants } from "../input/utils";

export interface IInputWrapperProps extends React.HTMLAttributes<HTMLDivElement> {
  title?: string;
  isRequired?: boolean;
  errorMessage?: string;
  labelClass?: string;
  borderClass?: string;
  inputWrapperClassName?: string;
  hintClassName?: string;
  children?: React.ReactNode;
  hint?: React.ReactNode | string;
  startAdornment?: React.ReactNode;
  endAdornment?: React.ReactNode;
  preserveErrorMessage?: boolean;
  titleProps?: Omit<IFieldTitleProps, "children">;
  isSuccess?: boolean;
  isError?: boolean;
  isDisabled?: boolean;
  textLength?: number | string;
  disableAnimated?: boolean;
  placeHolderEndAdornment?: React.ReactNode;
  inputSize?: keyof typeof bindSizeInputVariants;
}
