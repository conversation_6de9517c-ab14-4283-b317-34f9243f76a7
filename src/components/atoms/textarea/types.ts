import { DetailedHTMLProps, TextareaHTMLAttributes } from "react";
import { IInputWrapperProps } from "../inputWrapper/types";

export interface ITextareaProps
  extends DetailedHTMLProps<TextareaHTMLAttributes<HTMLTextAreaElement>, HTMLTextAreaElement> {
  title?: string;
  maxCharacter?: number;
  errorMessage?: string;
  inputWrapperProps?: IInputWrapperProps;
  wrapperClassName?: string;
  "data-test"?: string;
}
