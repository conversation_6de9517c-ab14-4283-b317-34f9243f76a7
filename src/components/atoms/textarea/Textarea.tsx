// Field Title should be removed after confirmation
// import FieldTitle from "@/components/Atoms/fieldTitle";
import { twMerge } from "tailwind-merge";

import { InputWrapper } from "@/components/atoms/inputWrapper";
import { ITextareaProps } from "./types";

function Textarea(props: ITextareaProps) {
  const { className, errorMessage, title, inputWrapperProps, wrapperClassName, maxCharacter, ...restProps } = props;

  return (
    <InputWrapper
      className={twMerge(wrapperClassName, "relative")}
      title=""
      inputWrapperClassName="px-0"
      errorMessage={errorMessage}
      hintClassName="ps-0"
      {...inputWrapperProps}
    >
      <textarea
        placeholder={title}
        className={twMerge(
          "textarea placeholder:text-[#CCE6FD] text-[#CCE6FD] text-sm font-semibold placeholder:text-sm  placeholder:font-semibold textarea-bordered min-h-[177px] rounded w-full border-1  bg-white/10 px-4 py-2 focus:border-primary",
          className
        )}
        maxLength={maxCharacter}
        {...restProps}
      />
      {!!maxCharacter && (
        <div className="absolute -bottom-4 text-[10px] leading-4 left-[3px] text-mediumGray">
          {maxCharacter} / {(restProps?.value as string)?.length ?? 0}
        </div>
      )}
    </InputWrapper>
  );
}
export default Textarea;
