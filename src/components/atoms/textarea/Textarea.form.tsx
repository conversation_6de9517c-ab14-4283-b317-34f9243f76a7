import { useDescription, useTsController } from "@ts-react/form";
import Textarea from "./Textarea";
import { ITextareaProps } from "./types";

export default function TextareaForm(props: ITextareaProps) {
  const {
    field: { onChange, value },
    error
  } = useTsController<string>();
  const { label } = useDescription();

  return (
    <Textarea
      {...props}
      value={value}
      title={label || ""}
      errorMessage={error?.errorMessage}
      onChange={e => onChange(e.target.value)}
    />
  );
}
