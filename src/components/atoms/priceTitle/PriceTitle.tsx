/* eslint-disable no-nested-ternary */
import Up from "@/assets/icons/arrow-up.svg";
import { numberWithCommas } from "@/utils/helpers";
import { twMerge } from "tailwind-merge";
import { IPriceTitleProps } from "./type";

function PriceTitle({ price, closePrice, changePrice }: IPriceTitleProps) {
  return (
    <div className=" text-base mt-1 mr-2">
      {price ? (
        <div>
          {numberWithCommas(price)} <br />
          <div className="flex items-center gap-[1px]">
            <p
              dir="ltr"
              className={twMerge(
                changePrice === 0 ? "text-white" : changePrice < 0 ? "text-[#FF5E5E]" : "text-[#A5F539]",
                "text-xs"
              )}
            >
              %<span>{closePrice?.toFixed(3)}</span>
            </p>
            {changePrice === 0 ? null : changePrice < 0 ? (
              <Up className="text-textDescending rotate-180 inline mr-0.5 h-3 w-3" />
            ) : (
              <Up className="text-textAscending inline mr-0.5 h-3 w-3" />
            )}
          </div>
        </div>
      ) : (
        "---"
      )}
    </div>
  );
}

export default PriceTitle;
