import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import Collapse from "./Collapse";

const meta: Meta<typeof Collapse> = {
  component: Collapse,
  title: "Components/atoms/Collapse"
};

export default meta;
type Story = StoryObj<typeof Collapse>;

export const Default: Story = {
  args: {
    children: (
      <div>
        <ul>
          <li>1</li>
          <li>2</li>
          <li>3</li>
        </ul>
      </div>
    ),
    title: "test"
  }
};
