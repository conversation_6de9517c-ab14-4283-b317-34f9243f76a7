/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import { useEffect, useState } from "react";
import useFilterStore from "@/store/FilterStore";
import { ICollapseProps } from "./type";
import Collapse from "./Collapse";

function FilterCollapse(props: ICollapseProps) {
  const { expandAll, setExpandAll } = useFilterStore();
  const { children, title, expand, id, className, onClearClick, showClear } = props;
  const [isExpanded, setIsExpanded] = useState(expand);
  useEffect(() => {
    if (expandAll) {
      setIsExpanded(false);
      setExpandAll(false);
    } else {
      setIsExpanded(expand);
    }
  }, [expand, expandAll]);
  return (
    <Collapse
      onClearClick={onClearClick}
      title={title}
      expand={isExpanded}
      showClear={showClear}
      id={id}
      className={className}
    >
      {children}
    </Collapse>
  );
}

export default FilterCollapse;
