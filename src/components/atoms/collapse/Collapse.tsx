/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import CollapseArrowDown from "@/assets/icons/collapseArrowDown.svg";
import CollapseArrowUp from "@/assets/icons/collapseArrowUp.svg";
import { useEffect, useState } from "react";
import { twMerge } from "tailwind-merge";
import { ICollapseProps } from "./type";

function Collapse(props: ICollapseProps) {
  const { children, title, expand, className, id, onClearClick, showClear } = props;
  const [isExpanded, setIsExpanded] = useState(expand);
  useEffect(() => {
    setIsExpanded(expand);
  }, [expand]);

  return (
    <div className="relative h-auto  cursor-pointer ">
      <div className="flex items-center justify-between pt-4 pb-[7px] pr-1">
        <div
          className="text-xs text-mainText w-32"
          onClick={() => {
            setIsExpanded(!isExpanded);
          }}
        >
          {title}
        </div>
        <div className="flex items-center">
          {showClear &&
            (isExpanded ? (
              <div className="text-warningText text-xs  border-b border-b-warningText" onClick={() => onClearClick()}>
                حذف
              </div>
            ) : (
              <div className="bg-warningText w-[6px] h-[6px] rounded-full" onClick={() => onClearClick()} />
            ))}

          <div
            className={twMerge("flex justify-between items-center", className)}
            onClick={() => {
              setIsExpanded(!isExpanded);
            }}
            data-test={`${id || ""}-collapse`}
          >
            {isExpanded ? <CollapseArrowUp className="w-4 h-4" /> : <CollapseArrowDown className="w-4 h-4" />}
          </div>
        </div>
      </div>

      <div className={twMerge("w-full hidden py-2", isExpanded && "block")}>{children}</div>
    </div>
  );
}

export default Collapse;
