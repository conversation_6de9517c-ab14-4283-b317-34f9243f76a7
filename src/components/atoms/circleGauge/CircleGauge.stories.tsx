import type { <PERSON><PERSON>, <PERSON>Ob<PERSON> } from "@storybook/react";
import CircleGauge from "@/components/atoms/circleGauge";

function CircleGaugeWrapper(props: any) {
  const mock2 = {
    data: [
      {
        id: "942ef6d0-50b2-44f3-8b72-43373b026050",
        type: 100,
        level: 0,
        min: -10,
        max: 1.0
      },
      {
        id: "942ef6d0-50b2-44f3-8b72-43373b026050",
        type: 100,
        level: 1,
        min: 1,
        max: 2
      },
      {
        id: "942ef6d0-50b2-44f3-8b72-43373b026050",
        type: 100,
        level: 2,
        min: 2,
        max: 5
      },
      {
        id: "c77d0bd5-4b18-4391-abfa-8f43d12a1bde",
        type: 100,
        level: 3,
        min: 5,
        max: 12
      },
      {
        id: "c77d0bd5-4b18-4391-abfa-8f43d12a1bde",
        type: 100,
        level: 4,
        min: 12,
        max: 14
      },
      {
        id: "6f396af8-189f-49dd-a686-95fed7133189",
        type: 100,
        level: 5,
        min: 14,
        max: 23
      },
      {
        id: "77f78ca9-9cff-482e-ae81-d2a0c5908567",
        type: 100,
        level: 6,
        min: 23,
        max: 24
      },
      {
        id: "725cd378-63f6-4db6-9dfe-e7af24591f5b",
        type: 100,
        level: 7,
        min: 24,
        max: 30
      }
    ],
    isSuccess: true,
    errorCode: null,
    errorMessage: null
  };

  const sortedData = mock2?.data?.sort((a, b) => a.min - b.min);

  const disableColorScheme = {
    0: "#28282C",
    1: "#28282C",
    2: "#28282C",
    3: "#28282C",
    4: "#28282C",
    5: "#28282C",
    6: "#28282C",
    7: "#28282C"
  };

  return (
    <div>
      <h1>با استفاده از پراپس size اندازه گیج بزرگ و کوچک میشود </h1>
      <div className="pb-2">تمامی سایز های موجود در پروژه وچود دارد است</div>
      <div className="pb-3 bg-backgroundCardBackground w-[200px]">
        <CircleGauge
          {...props}
          data={sortedData}
          colorScheme={disableColorScheme}
          labelFormatter={() => {}}
          lastLabel=""
          insideCircleClassName="bg-backgroundCardBackground"
          ringBorderPercentage={1}
        />
      </div>
      <div className="h-screen grid grid-cols-3">
        {sortedData?.map((item, index) => (
          <CircleGauge {...props} data={sortedData?.slice(0, index + 1)} value={[-9, -3, 1]} />
        ))}
        <CircleGauge {...props} data={sortedData} value={[-10, 0, 2]} />
        <CircleGauge {...props} data={sortedData} value={5} />
        <CircleGauge {...props} data={sortedData} value={-7} />
        <CircleGauge {...props} data={sortedData} value={[-7, 2]} />
      </div>
    </div>
  );
}

const meta: Meta<typeof CircleGauge> = {
  component: CircleGaugeWrapper,
  title: "Components/atoms/CircleGauge"
};

export default meta;
type Story = StoryObj<typeof CircleGauge>;

export const Default: Story = {
  args: {}
};

export const Small: Story = {
  args: {
    // data: mock2?.data,
    size: "small"
  }
};

export const Medium: Story = {
  args: {
    // data: mock2?.data,
    size: "medium"
  }
};
