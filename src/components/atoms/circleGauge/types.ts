export type TSize = "small" | "medium" | "large";

export interface IZone {
  id: string;
  type: number;
  level: number;
  min?: number;
  max?: number;
}

export interface ICircleGaugeProps {
  data: IZone[];
  size: TSize;
  labelFormatter?: (item?: IZone, index?: number, data?: IZone[]) => string | undefined;
  arrowColors?: string[];
  borderColor?: string;
  stripSize?: number;
  ringBorderPercentage?: number;
  lastLabel?: string | number;
  colorScheme?: { [key: number]: string };
  value?: number[] | number | null | null[] | (null | number)[];
  insideCircleClassName?: string;
  isSelected?: boolean;
}
