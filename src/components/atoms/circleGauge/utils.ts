import { IZone } from "@/components/atoms/circleGauge/types";
import { CSSProperties } from "react";

export function findArrowStyle(arrowWidth: number, arrowHeight: number, index: number, arrowColors: string[]) {
  return {
    width: arrowWidth,
    height: arrowHeight - (arrowHeight * index) / 5,
    top: (arrowHeight * index) / 5,
    background: arrowColors[index],
    ...(index ? { left: index * arrowWidth } : {}),
    zIndex: index * 10
  };
}

export const findPosition = (percent: number, gaugeSize: number) => {
  const xRadius = gaugeSize / 2 - 30;
  const yRadius = gaugeSize / 2 - 25;
  const xPosition = Math.cos(3.6 * percent * 0.01745) * xRadius;
  const yPosition = Math.sin(3.6 * percent * 0.01745) * yRadius;
  return [xPosition, yPosition];
};

export function defaultLabelFormatter(item?: IZone, index?: number, data?: IZone[]) {
  const count = data?.length;
  const eachPercent = 100 / Number(count);

  return `${Number(eachPercent * Number(index)).toFixed(0)}٪`;
}

export const emptyData = [
  {
    id: "0",
    type: 0,
    level: 0
  },
  {
    id: "1",
    type: 0,
    level: 0
  },
  {
    id: "2",
    type: 0,
    level: 0
  },
  {
    id: "3",
    type: 0,
    level: 0
  },
  {
    id: "4",
    type: 0,
    level: 0
  },
  {
    id: "5",
    type: 0,
    level: 0
  },
  {
    id: "6",
    type: 0,
    level: 0
  }
];

export const findStripeStyle = (gaugeSize: number, stripSize: number) => ({
  width: gaugeSize - stripSize,
  height: gaugeSize - stripSize - 8,
  bottom: gaugeSize - stripSize - 3,
  right: stripSize / 2
});

export function findLabelStyle(index: number, percent: number, gaugeSize: number, length?: number): CSSProperties {
  const radius = gaugeSize / 2 - 30;
  const sector = percent * index;
  return {
    width: Number(length) * 5.5,
    minWidth: 27,
    bottom: findPosition(sector, gaugeSize)?.[1],
    right: Number(findPosition(sector, gaugeSize)?.[0]) + radius,
    // eslint-disable-next-line no-nested-ternary
    textAlign: sector > 18 && sector < 32 ? "center" : sector < 25 ? "left" : "right"
  };
}

export function findDegree(data: IZone[], value?: number | null) {
  if (value === null) return null;
  const eachPercent = 100 / Number(data?.length);
  const index = data?.findIndex(item => Number(item?.min) <= Number(value) && Number(item?.max) >= Number(value));
  const includeRange = data?.[index];
  const initialPercent = eachPercent * index;
  const inRangePercent =
    ((Number(value) - Number(includeRange?.min)) * eachPercent) /
    (Number(includeRange?.max) - Number(includeRange?.min));
  if (index === -1) {
    if (Number(value) >= Number(data?.sort((a, b) => Number(a.max) - Number(b.max))?.[Number(data?.length) - 1]?.max))
      return 90;
  }
  return -90 + 1.8 * (initialPercent + inRangePercent);
}
