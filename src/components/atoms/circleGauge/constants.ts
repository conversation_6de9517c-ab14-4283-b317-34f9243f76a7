export const Sizes = {
  small: {
    gaugeSize: 144,
    pinSize: 14,
    arrowWidth: 2.6,
    arrowHeight: 34.7
  },
  medium: {
    gaugeSize: 166,
    pinSize: 14.5,
    arrowWidth: 3,
    arrowHeight: 40
  },
  large: {
    gaugeSize: 200,
    pinSize: 14.46,
    arrowWidth: 3.61,
    arrowHeight: 48.2
  }
};

interface KeyValuePairs {
  [name: number]: string;
}

export const DefaultColorScheme: KeyValuePairs = {
  0: "#76787A",
  1: "#841818",
  2: "#E51D1D",
  3: "#C2840C",
  4: "#F1C21B",
  5: "#65FF87",
  6: "#00D018",
  7: "#006C17"
};

export const DefaultTextColorScheme: KeyValuePairs = {
  0: "#F4F4F4",
  1: "#F4F4F4",
  2: "#252529",
  3: "#252529",
  4: "#252529",
  5: "#252529",
  6: "#252529",
  7: "#F4F4F4"
};

export const DisableScheme = {
  0: "#28282C",
  1: "#841818",
  2: "#E51D1D",
  3: "#C2840C",
  4: "#F1C21B",
  5: "#65FF87",
  6: "#00D018",
  7: "#006C17"
};

export const DefaultArrowColors = ["white", "#8871BA", "#B5179E"];
