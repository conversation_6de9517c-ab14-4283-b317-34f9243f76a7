import { motion } from "motion/react";
import { memo } from "react";
import { twMerge } from "tailwind-merge";
import { defaultLabelFormatter, emptyData, findArrowStyle, findDegree, findLabelStyle, findStripeStyle } from "./utils";
import { ICircleGaugeProps } from "./types";
import { DefaultArrowColors, Sizes, DefaultColorScheme, DisableScheme } from "./constants";

function CircleGauge({
  data,
  size = "large",
  labelFormatter = defaultLabelFormatter,
  borderColor = "#343438",
  lastLabel = "100%",
  stripSize = 10,
  ringBorderPercentage = 0.4,
  arrowColors = DefaultArrowColors,
  colorScheme = DefaultColorScheme,
  value,
  insideCircleClassName,
  isSelected
}: ICircleGaugeProps) {
  const { gaugeSize, pinSize, arrowWidth, arrowHeight } = Sizes[size];
  const tunedData = data?.length ? data : emptyData;
  const eachPercent = tunedData?.length ? 50 / Number(tunedData?.length) : 50;
  const tunedColorSchema = data?.length ? colorScheme : DisableScheme;
  const tunedBorderColorTw = isSelected ? "border-[#1F1F22]" : "border-[#343438]";
  const tunedBorderColor = isSelected ? "#1F1F22" : borderColor;

  function findValuesDegree() {
    if (Array?.isArray(value)) return value?.map(item => findDegree(tunedData, item));
    if (value !== null) return [findDegree(tunedData, value as number)];
    return null;
  }

  function findPercentageRange(index: number) {
    const isLast = index === data.length - 1;
    return !isLast
      ? ` ${index * eachPercent}%
               ${(index + 1) * eachPercent - ringBorderPercentage / 2}%,
                ${tunedBorderColor} ${(index + 1) * eachPercent - ringBorderPercentage / 2}%
                 ${(index + 1) * eachPercent}%`
      : ` ${index * eachPercent}%
               ${(index + 1) * eachPercent}%`;
  }

  function generateGradient() {
    return `conic-gradient(${tunedData?.map(
      (item, index) =>
        tunedColorSchema[Number(item?.level) as keyof typeof tunedColorSchema] + findPercentageRange(index)
    )} , transparent 50%)`;
  }

  return (
    <div className="overflow-hidden" style={{ width: gaugeSize, height: gaugeSize / 2 + 1 }}>
      <div style={{ top: arrowHeight + 1 }} className="w-full h-0 z-[1000] relative right-[calc(50%-1px)] flex">
        {(value || value === 0) &&
          findValuesDegree()?.map((item, index) =>
            item !== null ? (
              <motion.div
                className="origin-bottom -rotate-90 relative z-1000 rounded-2.5xl"
                style={findArrowStyle(arrowWidth, arrowHeight, index, arrowColors)}
                animate={{ rotate: [-90, item] }}
                transition={{ duration: 0.42, delay: 0.25 + index * 0.25 }}
              />
            ) : (
              ""
            )
          )}
      </div>
      <div className="w-full h-0 z-1000 relative justify-center flex" style={{ top: gaugeSize / 2 - pinSize / 2 + 1 }}>
        <div className="h-4 bg-white rounded-full relative z-[1000]" style={{ width: pinSize }} />
      </div>
      <div style={{ top: gaugeSize / 2 - 10 }} className="text-white z-50 h-0 w-full text-left relative text-xs px-4">
        {tunedData?.map((i, index) => (
          <div
            className="relative w-fit h-0 ltr"
            key={i?.id}
            style={findLabelStyle(
              index,
              eachPercent,
              gaugeSize,
              (labelFormatter(i, index, tunedData) as string)?.length
            )}
          >
            {data?.length ? labelFormatter(i, index, tunedData) : ""}
          </div>
        ))}
        {data?.length !== 0 && value !== null && (
          <div style={{ left: 2, bottom: 0 }} className="w-min relative h-0">
            {lastLabel ?? ""}
          </div>
        )}
      </div>
      <div
        className={twMerge("rounded-full bg-backgroundDisable -rotate-90 border", tunedBorderColorTw)}
        style={{
          background: generateGradient(),
          width: gaugeSize,
          height: gaugeSize
        }}
      />
      <div className="w-0 h-0">
        <div
          className={twMerge(
            "rounded-full -rotate-90 relative bg-backgroundDarkRow border",
            tunedBorderColorTw,
            insideCircleClassName
          )}
          style={findStripeStyle(gaugeSize, stripSize)}
        />
      </div>
    </div>
  );
}

export default memo(CircleGauge);
