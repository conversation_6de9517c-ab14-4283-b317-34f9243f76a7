/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import { twMerge } from "tailwind-merge";
import Tippy from "@tippyjs/react";
import { Placement } from "@popperjs/core";
import isBoolean from "lodash/isBoolean";
import { Dispatch, ReactNode, SetStateAction, useEffect, useRef, useState } from "react";

import { ClickAwayListener } from "../clickAwayListener";

interface IPopOverProps {
  children?: ReactNode;
  content: ReactNode;
  placement?: Placement;
  className?: string;
  isOpen?: boolean;
  setIsOpen?: Dispatch<SetStateAction<boolean>>;
}

function PopOver({ children, content, placement, className, setIsOpen, isOpen }: IPopOverProps) {
  const [isShown, setIsShown] = useState(false);
  const divRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (isBoolean(isOpen)) setIsShown(isOpen);
  }, [isOpen]);

  return (
    <Tippy
      visible={isShown}
      render={attrs =>
        isShown && (
          <ClickAwayListener
            onClickAway={event => {
              if (!divRef.current?.contains(event.target as Node)) {
                setIsShown(false);
                setIsOpen?.(false);
              }
            }}
          >
            <div {...attrs} className={twMerge("relative", className)} onClick={e => e.stopPropagation()}>
              {content}
            </div>
          </ClickAwayListener>
        )
      }
      placement={placement || "top-start"}
      animation="fade"
      className="!rounded-lg !bg-white !px-4"
    >
      <div
        ref={divRef}
        onClick={() => {
          setIsShown(prev => !prev);
          setIsOpen?.(!isOpen);
        }}
      >
        {children}
      </div>
    </Tippy>
  );
}

export default PopOver;
