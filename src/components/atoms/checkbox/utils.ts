/* eslint-disable import/prefer-default-export */
export const bindBgVariants = {
  filledGreen: "bg-[#26D5C0]  text-bodyBackground hover:bg-[#0F605B] hover:text-black",
  filledYellow: "bg-[#BCD526]  text-bodyBackground hover:bg-[#505D17] hover:text-black",
  filledBlue: "bg-[#2EA6FF]  text-bodyBackground hover:bg-[#1458B9] hover:text-black",
  filledLightGreen: "bg-lightGreen  text-bodyBackground hover:bg-jade800",
  filledDarkGreen: "bg-japaneseLaurel700 text-bodyBackground hover:bg-darkGreen",
  filledRed: "bg-textDescending text-bodyBackground  hover:bg-carnation900",
  filledMediumYellow: "bg-mediumYellow text-bodyBackground",
  filledLightYellow: "bg-semanticWithoutCoupon text-bodyBackground  hover:bg-[#505D17]",
  filledCyan: "bg-semanticWithCoupon text-bodyBackground",
  filledWhite: "bg-[#fff]  text-bodyBackground"
};

export const bindBorderVariants = {
  filledGreen: "border-[#26D5C0] hover:border-[#0F605B]",
  filledYellow: "border-[#BCD526] hover:border-[#505D17]",
  filledBlue: "border-[#2EA6FF] hover:border-[#1458B9]",
  filledLightGreen: "border-lightGreen hover:border-jade800",
  filledDarkGreen: "border-japaneseLaurel700 hover:border-darkGreen",
  filledRed: "border-textDescending hover:border-carnation900",
  filledMediumYellow: "border-mediumYellow",
  filledLightYellow: "border-semanticWithoutCoupon hover:border-[#505D17]",
  filledCyan: "border-semanticWithCoupon",
  filledWhite: "border-[#fff]"
};

export const bindSizeVariants = {
  extraLarge: "w-6 h-6 rounded-md border-[1.5px]",
  large: "w-5 h-5 rounded border",
  medium: "w-[18px] h-[18px] rounded-[4.5px] border",
  small: "w-4 h-4 rounded-sm border"
};

export const bindTickVariants = {
  extraLarge: "w-[18px] h-[18px] rounded",
  large: "w-[15px] h-[15px] rounded-sm",
  medium: "w-[13.5px] h-[13.5px] rounded-sm",
  small: "w-3 h-3 rounded-[1px]"
};

export const bindTickSizeVariants = {
  extraLarge: "w-3 h-2",
  large: "w-2 h-[6px]",
  medium: "w-2 h-[6px]",
  small: "w-[6px] h-[5px]"
};
