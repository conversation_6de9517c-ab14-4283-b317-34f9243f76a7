import { bindBgVariants, bindBorderVariants, bindSizeVariants } from "./utils";

export interface ICheckbox2Props {
  id?: string;
  checkboxClassName?: string;
  tickClassName?: string;
  textClassName?: string;
  text?: string;
  variant?: keyof typeof bindBgVariants | keyof typeof bindBorderVariants;
  size?: keyof typeof bindSizeVariants;
  disabled?: boolean;
  onChange?: (checked?: boolean) => void;
  checked?: boolean;
}
