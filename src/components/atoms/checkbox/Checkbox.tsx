"use client";

/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
import Tick from "@/assets/icons/tick.svg";
import { useEffect, useState } from "react";
import { twMerge } from "tailwind-merge";
import { ICheckbox2Props } from "./type";
import { bindBgVariants, bindBorderVariants, bindSizeVariants, bindTickSizeVariants, bindTickVariants } from "./utils";

function Checkbox(props: ICheckbox2Props) {
  const {
    id,
    textClassName,
    checkboxClassName,
    tickClassName,
    text,
    variant = "filledGreen",
    size = "extraLarge",
    disabled = false,
    onChange,
    checked
  } = props;

  const [internalChecked, setInternalChecked] = useState(checked ?? false);

  useEffect(() => {
    setInternalChecked(checked ?? false);
  }, [checked]);

  const handleOnChange = () => {
    if (disabled) return;

    if (onChange) {
      onChange(internalChecked);
    }
  };

  return (
    <div className="flex gap-1 items-center">
      <label htmlFor={id}>
        <div className="flex gap-1 items-center cursor-pointer" onClick={handleOnChange} data-test={id}>
          <div
            className={twMerge(
              bindBorderVariants[variant],
              bindSizeVariants[size],
              "flex justify-center items-center border-solid",
              disabled && "!border-textDisabled",
              !disabled && "cursor-pointer",
              checkboxClassName
            )}
            data-test="box"
          >
            {(internalChecked || disabled) && (
              <div
                className={twMerge(
                  bindBgVariants[variant],
                  bindTickVariants[size],
                  "flex justify-center items-center",
                  disabled && "!bg-textDisabled hover:text-bodyBackground",
                  disabled && !internalChecked && "hidden",
                  tickClassName
                )}
              >
                <Tick className={twMerge(bindTickSizeVariants[size])} />
              </div>
            )}
          </div>
          {text && (
            <div className={twMerge("text-xs leading-4 text-white200", textClassName)} data-test="label">
              {text}
            </div>
          )}
        </div>
        <input type="checkbox" hidden />
      </label>
    </div>
  );
}

export default Checkbox;
