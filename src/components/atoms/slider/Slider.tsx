import React from "react";
import Slider from "rc-slider";
import "rc-slider/assets/index.css";
import { IRangeSliderProps } from "./type";

export default function Index(props: IRangeSliderProps) {
  return (
    <div className="relative h-4 z-0">
      <div className="absolute right-1/2 bg-secondaryText h-3 w-[1px] top-[7px] z-10" />
      <Slider
        {...props}
        styles={{
          handle: {
            background: "#0C82F9",
            border: "2px solid #F4F4F4",
            opacity: 1,
            boxShadow: "none",
            marginTop: "1px",
            zIndex: "100"
          },
          rail: {
            background: "#676767",
            height: "16px"
          },
          track: {
            background: "#0c82f9",
            height: "12px",
            marginTop: "2px"
          }
        }}
      />
    </div>
  );
}
