import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import Select from "./Select";
import { ISelectFormItem, ISelectProps } from "./types";

const items = [
  {
    label: "فروردین",
    value: 1
  },
  {
    label: "اردیبهشت",
    value: 2
  },
  {
    label: "خرداد",
    value: 3
  },
  {
    label: "تیر",
    value: 4
  },
  {
    label: "مرداد",
    value: 5
  },
  {
    label: "شهریور",
    value: 6
  }
];

function SelectWrapper(props: ISelectProps<ISelectFormItem>) {
  return (
    <div className="h-screen">
      <div className=" w-36">
        <Select {...props} />
      </div>
    </div>
  );
}

const meta: Meta<typeof Select> = {
  component: SelectWrapper,
  title: "Components/atoms/Select"
};

export default meta;
type Story = StoryObj<typeof Select>;

export const SelectWithChild: Story = {
  args: {
    items,
    defaultValue: {
      label: "فروردین",
      value: "value1"
    },
    children: <button type="button">Open Menu</button>,
    variant: "dropdown"
  }
};

export const Default: Story = {
  args: {
    items,
    defaultValue: {
      label: "فروردین",
      value: "value1"
    },
    variant: "dropdown"
  }
};
