/* eslint-disable react/function-component-definition */
/* eslint-disable react/no-unstable-nested-components */
/* eslint-disable import/no-named-default */

import { Ref, forwardRef } from "react";
import { twMerge } from "tailwind-merge";
import { default as ReactSelect, components } from "react-select";
import selectStyles from "./styles";
import dropDownStyles from "./dropDownStyles";
import { ISelectFormItem, ISelectProps } from "./types";
import { SelectIndicator, DropDownIndicator } from "./utils";

const Select = forwardRef(<T extends ISelectFormItem>(props: ISelectProps<T>, ref?: Ref<any>) => {
  const { items, children, variant = "select", ...restProps } = props;

  const styles = {
    select: selectStyles,
    dropdown: dropDownStyles
  };

  const endAdornment = {
    select: SelectIndicator,
    dropdown: DropDownIndicator
  };

  const Control = (propsC: any) => {
    const Co = components.Control;

    return children ? (
      <div className="relative w-full h-full !p-0">
        {children}
        <div className="absolute left-0 top-0 w-full h-full opacity-0">
          <Co {...propsC} />
        </div>
      </div>
    ) : (
      <Co {...propsC} />
    );
  };

  return (
    <ReactSelect
      {...restProps}
      options={items}
      ref={ref}
      className={twMerge("flex items-center", restProps?.className)}
      components={{
        DropdownIndicator: endAdornment[variant],
        IndicatorSeparator: () => null,
        Control,
        ...restProps?.components
      }}
      styles={restProps?.styles || styles[variant]}
    />
  );
});

export default Select;
