/* eslint-disable no-nested-ternary */
import { StylesConfig } from "react-select";
import { ISelectItems } from "./types";

const selectStyles: StylesConfig<ISelectItems<any>, boolean> = {
  control: styles => ({
    ...styles,
    width: "100%",
    minHeight: "32px",
    borderRadius: "8px",
    border: "1px solid #858585",
    boxShadow: "none",
    backgroundColor: "transparent",
    color: "#F4F4F4",
    fontSize: "14px",
    cursor: "pointer"
  }),
  menu: styles => ({
    ...styles,
    zIndex: 20,
    fontSize: "10px",
    marginTop: "0",
    background: "#343438",
    borderRadius: "4px",
    border: "1px solid #545454",
    boxShadow: "0px 4px 12px 0px rgba(23, 28, 35, 0.20)"
  }),
  menuList: styles => ({
    ...styles,
    padding: 0
  }),
  container: styles => ({
    ...styles,
    "& *": {
      color: "#F4F4F4 !important"
    }
  }),
  input: styles => ({
    ...styles,
    color: "#F4F4F4"
  }),
  option: (styles, state) => ({
    ...styles,
    fontWeight: 400,
    fontSize: "14px",
    textAlign: "right",
    padding: "8px 6px",
    background: state?.isSelected ? "transparent" : "unset",
    "&:hover": {
      cursor: "pointer",
      background: "#1F1F22"
    },
    "&:active": {
      background: "transparent"
    },
    "&:focus": {
      background: "transparent"
    }
  })
};

export default selectStyles;
