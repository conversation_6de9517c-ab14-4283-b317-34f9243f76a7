/* eslint-disable no-nested-ternary */
import { StylesConfig } from "react-select";
import { ISelectItems } from "./types";

const selectStyles: StylesConfig<ISelectItems<any>, boolean> = {
  control: styles => ({
    ...styles,
    border: 0,
    width: "100%",
    display: "flex",
    minHeight: "24px",
    boxShadow: "none",
    cursor: "pointer",
    borderRadius: "8px",
    background: "transparent",
    justifyContent: "start !important",
    fontSize: "14px"
  }),
  menu: styles => ({
    ...styles,
    zIndex: 20,
    marginBottom: "-2px",
    padding: "0",
    minWidth: "43px",
    borderRadius: "4px",
    background: "#343438",
    right: "-7px",
    border: "1px solid #545454",
    boxShadow: "0px 4px 12px 0px rgba(23, 28, 35, 0.20)"
  }),
  menuList: styles => ({
    ...styles,
    padding: "0 !important",
    maxHeight: "170px",
    overflow: "auto",
    display: "flex",
    flexDirection: "column",
    fontSize: "10px",
    paddingTop: 0,
    paddingBottom: 0,
    gap: "0px",
    "::-webkit-scrollbar-thumb": {
      background: "#1477FF"
    }
  }),

  valueContainer: styles => ({
    ...styles,
    caretColor: "transparent",
    padding: 0
  }),
  container: styles => ({
    ...styles,
    "& *": {
      color: "#F4F4F4  !important"
    }
  }),
  option: (styles, state) => ({
    ...styles,
    fontWeight: 400,
    textAlign: "right",
    padding: "8px 6px",
    background: state?.isSelected ? "transparent" : "unset",
    "&:hover": {
      cursor: "pointer",
      background: "#ffffff1a"
    },
    "&:active": {
      background: "transparent"
    },
    "&:focus": {
      background: "transparent"
    }
  })
};

export default selectStyles;
