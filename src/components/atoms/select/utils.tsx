import { twMerge } from "tailwind-merge";
import Chevron from "@/assets/icons/chevron.svg";
import { DropdownIndicatorProps } from "react-select";
import { ISelectItems } from "./types";

export function SelectIndicator<T>(props: DropdownIndicatorProps<ISelectItems<T>>) {
  const { isFocused } = props;

  return (
    <Chevron
      className={twMerge(
        isFocused ? "!text-chevronFocus" : "",
        "ml-3 text-chevronLight hover:text-chevronHover focus:text-chevronFocus w-[7px] h-1"
      )}
    />
  );
}

export function DropDownIndicator<T>(props: DropdownIndicatorProps<ISelectItems<T>>) {
  const { isFocused } = props;

  return (
    <Chevron className={twMerge("mr-1 w-2 h-2", isFocused && "rotate-180", "transition-transform duration-500")} />
  );
}
