import { twMerge } from "tailwind-merge";
import FieldTitle from "../fieldTitle";

import { TSwitch } from "./types";
import { bindSizeVariant } from "./utils";

function Switch(props: TSwitch) {
  const { title, titleClassName, disabled = false, sizeVariant = "small" } = props;

  return (
    <div className="flex items-center gap-2">
      <FieldTitle className={twMerge(titleClassName, "text-xs")}>{title}</FieldTitle>
      <label
        data-test={props?.dataTest}
        className="relative inline-flex cursor-pointer items-center"
        htmlFor={props?.id}
      >
        <input {...props} disabled={disabled} type="checkbox" className={twMerge(props?.className, "peer sr-only")} />
        <div
          className={twMerge(
            bindSizeVariant[sizeVariant],
            "peer rotate-180 bg-bodyBackground after:absolute  after:rounded-sm after:border  after:bg-mainText after:transition-all after:content-[''] peer-checked:bg-primary peer-checked:hover:bg-radiance800 active:!shadow-backgroundDarkRow peer-checked:active:!shadow-radiance900  peer-checked:after:translate-x-full ",
            disabled &&
              "cursor-not-allowed after:border-none  bg-textDisabled peer-checked:after:border-none after:bg-secondaryText peer-checked:hover:bg-textDisabled peer-checked:bg-textDisabled peer-checked:after:bg-secondaryText "
          )}
        />
      </label>
    </div>
  );
}

export default Switch;
