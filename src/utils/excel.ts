import { utils, writeFile } from "xlsx";
import { TExportData } from "./types";

/**
 * Download excel or scv or ... file
 * (single sheet. if you want to download multiple sheet, use exportDataMultipleSheet)
 * @param data {array | object}
 * @param options {object}
 *
 * @example
 * ```ts
 *  exportData(
 *    [ { a: 1, b: 2 }, { a: 3, b: 4, c: 4 } ],
 *    { fileName: "testFile", sheetName: "test Sheet", extension: "csv"}
 *  )
 * ```
 */
const exportData: TExportData = (data: any, { sheetName, fileName, extension } = {}) => {
  const worksheet = utils.json_to_sheet(data);
  const workbook = utils.book_new();
  utils.book_append_sheet(workbook, worksheet, (sheetName as string)?.length < 31 ? sheetName || "Sheet1" : '"Sheet1"');
  writeFile(workbook, `${fileName || "DataSheet"}.${extension || "xlsx"}`);
};

export default exportData;
