/* eslint-disable react/require-default-props */
/* eslint-disable react/no-unused-prop-types */
import { toast } from "react-toastify";
import { twMerge } from "tailwind-merge";
import { ReactNode } from "react";
import Close from "@/assets/icons/toastClose.svg";
import WarningIcon from "@/assets/icons/toast-warn.svg";
import Button from "@/components/atoms/button";
import { CloseButtonProps } from "react-toastify/dist/components";

export interface IToast {
  title: string;
  subTitle?: ReactNode;
  successIcon?: ReactNode;
  errorIcon?: ReactNode;
  onClick?: () => void;
}

export interface IContent extends IToast {
  icon: ReactNode;
}

export const commonStyles = {
  hideProgressBar: true
};

export function Content({ title, subTitle, icon, onClick }: IContent) {
  return (
    <div className="text-white whitespace-pre-wrap">
      <div className="flex items-center gap-1 text-right leading-normal">
        {icon && <span className="p-[3px] rounded bg-inputFill">{icon}</span>}
        <h2>{title}</h2>
      </div>
      <span className={twMerge("block text-xs font-normal leading-4 my-1 xsm:text-base")}>{subTitle}</span>
      {!!onClick && (
        <Button className="mr-auto mt-4" size="small" variant="outLineWhite">
          مشاهده
        </Button>
      )}
    </div>
  );
}

export const successToast = ({
  title,
  subTitle,
  onClick,
  successIcon = <WarningIcon className="text-[#488506]" />
}: IToast) =>
  toast.success(<Content title={title} subTitle={subTitle} icon={successIcon} onClick={onClick} />, {
    ...commonStyles,
    closeButton: (props: CloseButtonProps) => <Close onClick={props?.closeToast} className="absolute left-4 top-4" />
  });

export const warningToast = ({
  title,
  subTitle,
  onClick,
  successIcon = <WarningIcon className="text-[#F1C21B]" />
}: IToast) =>
  toast.warn(<Content title={title} subTitle={subTitle} icon={successIcon} onClick={onClick} />, {
    ...commonStyles,
    closeButton: (props: CloseButtonProps) => <Close onClick={props?.closeToast} className="absolute left-4 top-4" />
  });

export const errorToast = ({
  title,
  subTitle,
  onClick,
  successIcon = <WarningIcon className="text-[#F83B3B]" />
}: IToast) =>
  toast.error(<Content title={title} subTitle={subTitle} icon={successIcon} onClick={onClick} />, {
    ...commonStyles,
    closeButton: (props: CloseButtonProps) => <Close onClick={props?.closeToast} className="absolute left-4 top-4" />
  });

export const infoToast = ({
  title,
  subTitle,
  onClick,
  successIcon = <WarningIcon className="text-[#006FFF]" />
}: IToast) =>
  toast.info(<Content title={title} subTitle={subTitle} icon={successIcon} onClick={onClick} />, {
    ...commonStyles,
    closeButton: (props: CloseButtonProps) => <Close onClick={props?.closeToast} className="absolute left-4 top-4" />
  });
