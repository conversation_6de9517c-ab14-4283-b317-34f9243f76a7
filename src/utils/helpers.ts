import { toast } from "react-toastify";
import { HubConnection } from "@microsoft/signalr";

export const MAX_ROWS = 99999999;

/** checks if param is string */
export const isString = (val: any) => typeof val === "string";
/** checks if param is number */
export const isNumber = (val: any) => typeof val === "number";

/** checks if param is number string */
export const isNumberString = (val: any) => isString(val) && !Number.isNaN(Number(val));
/** checks if param is number in value */
export const isNumberOrNumberStr = (val: any) => isNumber(val) || isNumberString(val);

// eslint-disable-next-line import/prefer-default-export
export const showServiceError = () => toast.error("خطای اتصال به سرویس");

export const START_SIGNALR_DELAY = 5000;
export async function startSignalRConnection(connection: HubConnection, streamName: string) {
  try {
    await connection.start();
  } catch (err) {
    setTimeout(startSignalRConnection, START_SIGNALR_DELAY);
  }

  connection?.onclose(async () => {
    await startSignalRConnection(connection, streamName);
  });
}

export function numberWithCommas(x?: number | string) {
  return x?.toString()?.replace(/\B(?=(\d{3})+(?!\d))/g, ",") || "-";
}

/**
 * Similar to `.toFixed() `.
 * Reduce Number to specific decimals but with options to control round or not
 * @param value
 * @param roundTheDecimals
 * @param decimalPlaces
 *
 * @returns {string}
 *
 * @example
 * toDecimals(1452.12569); // 1452.13
 * toDecimals(1452.12569, 2, false); // 1452.12
 * toDecimals(1452.12569, 3, true); // 1452.126
 * toDecimals(1452.12569, 3, false); // 1452.125
 */
export function toDecimals(value: number, decimalPlaces = 2, roundTheDecimals = true): string {
  if (roundTheDecimals) {
    // number with decimals with rounding last digit
    return value.toFixed(decimalPlaces);
  }
  // number with decimals without rounding
  return (Math.floor(value * 10 ** decimalPlaces) / 10 ** decimalPlaces).toFixed(decimalPlaces);
}

export function commaSeparator(
  number: string | number,
  decimalPlaces: number = 0,
  roundTheDecimals = true,
  maxDigits?: number // NEW PARAMETER
): string | undefined {
  if (!isNumberOrNumberStr(number)) return undefined;

  const num: number = Number(number);
  // eslint-disable-next-line no-restricted-globals
  if (isNaN(num)) return undefined;

  if (maxDigits !== undefined) {
    const integerLength = Math.floor(Math.abs(num)).toString().length;
    // eslint-disable-next-line no-param-reassign
    decimalPlaces = Math.max(0, maxDigits - integerLength);
  }

  const value: string = toDecimals(num, decimalPlaces, roundTheDecimals);
  const parts = value.toString().split(".");
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ",");

  return parts.join(".");
}

export const insertParamsToUrl = <T>(url: string, parameters: { [key: string]: T | undefined }) => {
  let urlObject = url;

  Object.entries(parameters).forEach(([key, value], index) => {
    if ((value || typeof value === "boolean" || typeof value === "number") && key) {
      urlObject += `${index === 0 ? "?" : "&"}${key}=${value}`;
    }
  });

  return urlObject;
};

export function convertIntToCurrency(
  val?: number,
  decimalPlaces = 2,
  roundTheDecimals = true
): { value: string; unit: string } {
  if (Math.abs(Number(val)) >= 1.0e12) {
    // twelve Zeroes for Trillion
    return {
      value: toDecimals(Math.abs(Number(val)) / 1.0e12, decimalPlaces, roundTheDecimals),
      unit: "T"
    };
  }
  if (Math.abs(Number(val)) >= 1.0e9) {
    // nine Zeroes for Billions
    return {
      value: toDecimals(Math.abs(Number(val)) / 1.0e9, decimalPlaces, roundTheDecimals),
      unit: "B"
    };
  }
  if (Math.abs(Number(val)) >= 1.0e6) {
    // Six Zeroes for Millions
    return {
      value: toDecimals(Math.abs(Number(val)) / 1.0e6, decimalPlaces, roundTheDecimals),
      unit: "M"
    };
  }
  if (Math.abs(Number(val)) >= 1.0e3) {
    return {
      value: toDecimals(Math.abs(Number(val)) / 1.0e3, decimalPlaces, roundTheDecimals),
      unit: "K"
    };
  }
  return {
    value: toDecimals(Math.abs(Number(val)), decimalPlaces, roundTheDecimals),
    unit: ""
  };
}

export function inMillion(value: number | string) {
  return Number(value) > 1000000
    ? ` ${commaSeparator((Number(value) / 1000000)?.toFixed(2))} M`
    : commaSeparator(value);
}

export function inBillion(value: number | string, fixedNumber = 2) {
  return Number(value) > 1000000000
    ? ` ${commaSeparator((Number(value) / 1000000000)?.toFixed(fixedNumber), 2, false)} B`
    : commaSeparator(value);
}

/**
 * Convert farsi letters to arabic
 * @param text
 * @returns {string}
 * @example
 * ArabicToPersian("یسنا");
 */
export function PersianToArabic(text?: string): string {
  if (!text) {
    return "";
  }

  let arabicText = text.replace(/\u06A9/g, "\u0643"); // ک
  arabicText = arabicText.replace(/\u06CC/g, "\u064A"); // ی

  return arabicText;
}

/**
 * @param strict
 * strict: true => empty(0) // true (it's empty!)
 * strict: false =>  empty(0) // false (it's NOT empty!)
 */
export function empty(data: any, strict: boolean = false): boolean {
  const dataType = typeof data;

  if (dataType === "string" && data.length === 0) {
    return true;
  }
  if (dataType === "number" && data === 0 && strict) {
    return true;
  }
  if (dataType === "boolean" && !data && strict) {
    return true;
  }
  if (dataType === "undefined" || data === null) {
    return true;
  }
  if (Array.isArray(data)) {
    return data.length === 0;
  }
  if (dataType === "object") {
    return Object.keys(data).length === 0;
  }

  return false;
}

const mapPersianCharKeys = [
  1711, 0, 0, 0, 0, 1608, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1705, 1572, 0, 1548, 1567, 0, 1616, 1571, 8250, 0,
  1615, 0, 0, 1570, 1577, 0, 0, 0, 1569, 1573, 0, 0, 1614, 1612, 1613, 0, 0, 8249, 1611, 171, 0, 187, 1580, 1688, 1670,
  0, 1600, 1662, 1588, 1584, 1586, 1740, 1579, 1576, 1604, 1575, 1607, 1578, 1606, 1605, 1574, 1583, 1582, 1581, 1590,
  1602, 1587, 1601, 1593, 1585, 1589, 1591, 1594, 1592
];

/**
 * Convert english letters to farsi.
 * @example
 * ConvertEnToFa("hldk") => امین
 * @reference
 * https://rohamweb.com/article/learning/473-فارسی-تایپ-کردن-در-فیلدهای-فرم.html
 */
export const ConvertEnToFa = (enText: string) =>
  enText
    .toLowerCase()
    .split("")
    .map((c: string) => {
      const key = c.charCodeAt(0);
      const code = mapPersianCharKeys[key - 39] ? mapPersianCharKeys[key - 39] : key;
      return String.fromCharCode(code);
    })
    .join("");

export const toFixedNumber = (n: number, r?: number) => (!Number.isNaN(n) ? +n.toFixed(r || 2) : 0);

export const isNullOrEmpty = (v?: number | null) => v == null || v === undefined;

export const toFixedInPercent = (n: number, r?: number) =>
  !Number.isNaN(n) ? `${+((n || 0) * 100).toFixed(r || 2)}٪` : "-";

export const toBillionToman = (n?: number) => Number(n) / 10000000000;

export const toFixed = (n: number, r?: number) => (!Number.isNaN(n) ? +(n || 0).toFixed(r || 2) : "-");

/**
 * converts number with symbol (K, M, B)
 * @returns 1,000 => 1K
 * @returns 1,000,000 => 1M
 * @returns 10,000,000 => 1B
 */
export function numbersUnit(value?: number, decimals = 2): string {
  if (value === 0) return "0";
  if (value === undefined) return "-";
  if (Math.abs(value) < 1000000 && Math.abs(value) >= 1000) return `${commaSeparator(Number(value) / 1000, decimals)}K`;
  if (Math.abs(value) < 1000000000 && Math.abs(value) >= 1000000)
    return `${commaSeparator(Number(value) / 1000000, decimals)}M`;
  if (Math.abs(value) < 1000000000000 && Math.abs(value) >= 1000000000)
    return `${commaSeparator(Number(value) / 1000000000, decimals)}B`;
  if (Math.abs(value) >= 1000000000000) return `${commaSeparator(Number(value) / 1000000000000, decimals)}T`;
  return `${commaSeparator(Number(value), decimals)}`;
}

/**
 * converts number in `TOMAN` with symbol (K, M, B) (!!removes one zero because of toman)
 * @returns 10,000 => 1K
 * @returns 100,000 => 10K
 * @returns 1,000,000 => 100K
 * @returns 10,000,000 => 1M
 * @returns 1,000,000,000 => 100M
 * @returns 10,000,000,000 => 1B
 */
export function numbersUnitInToman(initialValue?: number): string {
  if (initialValue === undefined) return "-";

  const value = initialValue / 10;

  return numbersUnit(value);
}

export const setMinChartRange = (n: number) => (n > 0 && n < 1 ? 1 : n);
