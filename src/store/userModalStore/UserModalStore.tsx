import { IDialogPropTypes } from "rc-dialog/lib/IDialogPropTypes";
import { ReactNode } from "react";
import { create } from "zustand";

export type TUserModalProps = Omit<IDialogPropTypes, "title"> & {
  center?: boolean;
  headerTitle?: ReactNode;
  titleClassName?: string;
};

interface IUseUserModalStore {
  isOpen: boolean;
  userModalContent: ReactNode | null;
  userModalProps?: TUserModalProps;

  openUserModal: (content: ReactNode, userModalProps?: TUserModalProps) => void;
  closeUserModal: () => void;
  setUserModalContent: (content: ReactNode) => void;
  setUserModalProps: (props?: TUserModalProps) => void;
}

const useUserModalStore = create<IUseUserModalStore>(set => ({
  isOpen: false,
  openUserModal: (content, props) => set({ isOpen: true, userModalContent: content, userModalProps: props }),
  closeUserModal: () => set({ isOpen: false }),

  userModalContent: null,
  setUserModalContent: content => set({ userModalContent: content }),

  userModalProps: undefined,
  setUserModalProps: props => set({ userModalProps: props })
}));

export default useUserModalStore;
