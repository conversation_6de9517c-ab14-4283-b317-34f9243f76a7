import { create } from "zustand";

interface IFilterStore {
  BondName: string;
  setBondName: (BondNameProp: string) => void;

  CouponBond: boolean;
  toggleWithCoupons: (withCouponsProp: boolean) => void;
  ZeroCouponBond: boolean;
  toggleWithOutCoupons: (withOutCouponsProp: boolean) => void;

  TotalTradedVolume: (number | undefined)[] | undefined;
  setMarketSizeRange: (marketSizeRangeProp: (number | undefined)[] | undefined) => void;

  MaturityDateRange: string[] | Date[] | undefined;
  setMaturityDateRange: (maturityDateRangeProp: string[] | Date[] | undefined) => void;

  LastTradePriceYtm: (number | undefined)[] | undefined;
  setYTMLastPriceRange: (YTMLastPriceProp: (number | undefined)[] | undefined) => void;

  BestBuyYtm: (number | undefined)[] | undefined;
  setYTMPurchaseQuote: (YTMPurchaseQuoteProp: (number | undefined)[] | undefined) => void;

  BestSellYtm: (number | undefined)[] | undefined;
  setYTMSalesQuote: (YTMSalesQuoteProp: (number | undefined)[] | undefined) => void;

  ClosePriceYtm: (number | undefined)[] | undefined;
  setYTMClosePrice: (YTMClosePriceProp: (number | undefined)[] | undefined) => void;

  NominalInterestRate: (number | undefined)[] | undefined;
  setNominalInterestRange: (NominalInterestRangeProp: (number | undefined)[] | undefined) => void;

  isConfirmFilterCount: number;
  setIsConfirmFilterCount: () => void;

  expandAll: boolean | undefined;
  setExpandAll: (expandAll: boolean | undefined) => void;

  disableForm: boolean;
  setDisableForm: (expandAll: boolean) => void;

  lastTradePriceYtmCollapse: boolean;
  setLastTradePriceYtmCollapse: (isCollapsed: boolean) => void;

  bestBuyYtmCollapse: boolean;
  setBestBuyYtmCollapse: (isCollapsed: boolean) => void;

  bestSellYtmCollapse: boolean;
  setBestSellYtmCollapse: (isCollapsed: boolean) => void;

  closePriceYtmCollapse: boolean;
  setClosePriceYtmCollapse: (isCollapsed: boolean) => void;

  resetAll: () => void;
}

export const filterDefaultValues = {
  BondName: "",
  CouponBond: true,
  ZeroCouponBond: true,
  TotalTradedVolume: undefined,
  MaturityDateRange: undefined,
  LastTradePriceYtm: undefined,
  BestBuyYtm: undefined,
  BestSellYtm: undefined,
  ClosePriceYtm: undefined,
  NominalInterestRate: undefined,
  isConfirmFilterCount: 0,
  expandAll: false,
  disableForm: true,
  lastTradePriceYtmCollapse: false,
  bestBuyYtmCollapse: false,
  bestSellYtmCollapse: false,
  closePriceYtmCollapse: false
};

const useFilterStore = create<IFilterStore>(set => ({
  ...filterDefaultValues,
  setBondName: BondNameProp => set({ BondName: BondNameProp }),
  toggleWithCoupons: withCouponsProp => set({ CouponBond: withCouponsProp }),
  toggleWithOutCoupons: withOutCouponsProp => set({ ZeroCouponBond: withOutCouponsProp }),
  setMarketSizeRange: marketSizeRangeProp => set({ TotalTradedVolume: marketSizeRangeProp }),
  setMaturityDateRange: maturityDateRangeProp => set({ MaturityDateRange: maturityDateRangeProp }),
  setYTMLastPriceRange: YTMLastPriceProp => set({ LastTradePriceYtm: YTMLastPriceProp }),
  setYTMPurchaseQuote: YTMPurchaseQuoteProp => set({ BestBuyYtm: YTMPurchaseQuoteProp }),
  setYTMSalesQuote: YTMSalesQuoteProp => set({ BestSellYtm: YTMSalesQuoteProp }),
  setYTMClosePrice: YTMClosePriceProp => set({ ClosePriceYtm: YTMClosePriceProp }),
  setNominalInterestRange: NominalInterestRangeProp => set({ NominalInterestRate: NominalInterestRangeProp }),
  setIsConfirmFilterCount: () =>
    set(({ isConfirmFilterCount }) => ({ isConfirmFilterCount: isConfirmFilterCount + 1 })),
  setExpandAll: expandAllProp => set({ expandAll: expandAllProp }),
  setDisableForm: disableFormProp => set({ disableForm: disableFormProp }),
  setLastTradePriceYtmCollapse: collapseLastPrice =>
    set({
      lastTradePriceYtmCollapse: collapseLastPrice
    }),
  setBestBuyYtmCollapse: collapseBestBuy => set({ bestBuyYtmCollapse: collapseBestBuy }),
  setBestSellYtmCollapse: collapseBestSell => set({ bestSellYtmCollapse: collapseBestSell }),
  setClosePriceYtmCollapse: collapseClosePrice => set({ closePriceYtmCollapse: collapseClosePrice }),

  resetAll: () => set(filterDefaultValues)
}));
export default useFilterStore;
