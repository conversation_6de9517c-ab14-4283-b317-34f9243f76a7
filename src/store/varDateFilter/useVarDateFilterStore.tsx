import { create } from "zustand";
import { getPrevDate } from "@/containers/tradeHistory/util";
import { FILTER_TIME } from "@/components/molecules/DateFilter/utils";

export type TPeriodFilter = { fromDate?: string; toDate?: string } | null;
export type TActiveTab = { id: string; title: string };

interface IVarDataFilterStore {
  period: TPeriodFilter;
  setPeriod: (content: TPeriodFilter) => void;
  resetFilter: () => void;
  activeTab?: { id: string | number; title: string | number };
  setActiveTab: (content: TActiveTab) => void;
}

const defaultActiveTab = { id: FILTER_TIME.Month3, title: "3 ماه" };

const useVarDateFilterStore = create<IVarDataFilterStore>(set => ({
  period: { fromDate: getPrevDate(FILTER_TIME.Month3), toDate: "" },
  setPeriod: content => set(state => ({ period: { ...state?.period, ...content } })),
  resetFilter: () =>
    set({ period: { fromDate: getPrevDate(FILTER_TIME.Month3), toDate: "" }, activeTab: defaultActiveTab }),
  activeTab: defaultActiveTab,
  setActiveTab: (content: TActiveTab) => set({ activeTab: content })
}));

export default useVarDateFilterStore;
