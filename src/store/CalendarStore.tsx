import { create } from "zustand";

type State = {
  isin: string;
  symbol: string;
  setTickerValues: (isin: string, symbol: string) => void;
};

const useCalculatorStore = create<State>(set => ({
  isin: "",
  symbol: "",
  setTickerValues: (isin, symbol) => set({ isin, symbol })
}));

type StateCalculatorStatus = {
  isShowCalculator: boolean;
  showCalculator: () => void;
  closeCalculator: () => void;
};

export const useCalculatorStatus = create<StateCalculatorStatus>(set => ({
  isShowCalculator: false,
  showCalculator: () => set({ isShowCalculator: true }),
  closeCalculator: () => set({ isShowCalculator: false })
}));

export default useCalculatorStore;
