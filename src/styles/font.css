@font-face {
  font-family: yekan-bakh;
  font-style: normal;
  font-weight: 100;
  src: url("./font/YekanBakhFaNum-Thin.woff") format("woff");
}

@font-face {
  font-family: yekan-bakh;
  font-style: normal;
  font-weight: 300;
  src: url("./font/YekanBakhFaNum-Light.woff") format("woff");
}

@font-face {
  font-family: yekan-bakh;
  font-style: normal;
  font-weight: 400;
  src: url("./font/YekanBakhFaNum-Regular.woff") format("woff");
}

@font-face {
  font-family: yekan-bakh;
  font-style: normal;
  font-weight: 600;
  src: url("./font/YekanBakhFaNum-SemiBold.woff") format("woff");
}

@font-face {
  font-family: yekan-bakh;
  font-style: normal;
  font-weight: 700;
  src: url("./font/YekanBakhFaNum-Bold.woff") format("woff");
}

@font-face {
  font-family: yekan-bakh;
  font-style: normal;
  font-weight: 800;
  src: url("./font/YekanBakhFaNum-ExtraBold.woff") format("woff");
}

@font-face {
  font-family: yekan-bakh;
  font-style: normal;
  font-weight: 900;
  src: url("./font/YekanBakhFaNum-Black.woff") format("woff");
}

@font-face {
  font-family: yekan-bakh;
  font-style: normal;
  font-weight: 950;
  src: url("./font/YekanBakhFaNum-ExtraBlack.woff") format("woff");
}
