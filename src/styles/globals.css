@import url(./font.css);
@import "./tippy.css";

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  input[type="number"]::-webkit-inner-spin-button,
  input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
}
:root {
  --max-width: 1100px;
  --border-radius: 12px;
  --font-mono: ui-monospace, Menlo, Monaco, "Cascadia Mono", "Segoe UI Mono", "Roboto Mono", "Oxygen Mono",
    "Ubuntu Monospace", "Source Code Pro", "Fira Mono", "Droid Sans Mono", "Courier New", monospace;
  color-scheme: only light !important;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

.grecaptcha-badge {
  @apply hidden;
}

body {
  overflow-x: hidden;
  @apply h-full w-full;

  direction: rtl;
  font-family: "yekan-bakh" !important;
  color: rgb(250 252 251);
  background-color: rgb(0 0 15);
}

/* width */
::-webkit-scrollbar {
  width: 1px;
  height: 4px;
}

/* Track */
::-webkit-scrollbar-track {
  @apply rounded-2xl bg-[#8F94A2];
}

/* Handle */
::-webkit-scrollbar-thumb {
  @apply rounded-2xl bg-[#E3E3E3];
}

a {
  color: inherit;
  text-decoration: none;
}

/* Change the white to any color */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  -webkit-background-clip: text;
  -webkit-text-fill-color: #ffffff;
  transition: background-color 5000s ease-in-out 0s;
  box-shadow: inset 0 0 20px 20px #23232329;
}

.ltr {
  direction: ltr;
}

[data-tippy-root] {
  pointer-events: auto !important;
}

.ag-tooltip.ag-tooltip-animate,
.ag-tooltip-custom.ag-tooltip-animate {
  transition: none !important;
}

.highcharts-tooltip-container {
  z-index: 99 !important;
}
