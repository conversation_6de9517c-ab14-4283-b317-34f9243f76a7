:root {
  --toastify-toast-width: auto;
  --toastify-font-family: yekan-bakh;

  --toastify-color-success: #002020;
  --toastify-color-warning: #241c00 !important;
  --toastify-color-error: #190000 !important;
  --toastify-color-info: #000a1d !important;
  --toastify-text-color-light: #f4f4f4;
  --toastify-text-color-success: #f4f4f4;
  --toastify-text-color-warning: #f4f4f4;
  --toastify-text-color-error: #f4f4f4;
  --toastify-text-color-info: #f4f4f4;

  --toastify-toast-bottom: 70px;
  --toastify-toast-right: 38px;
  --toastify-toast-bd-radius: 12px;
  --toastify-toast-min-height: 48px;
}

.Toastify__toast {
  padding: 0 !important;
  direction: rtl;

  &-body {
    padding: 16px 0 16px 16px !important;
    word-break: break-word;
    width: 360px;
  }

  &-icon {
    margin: 0 !important;
    max-width: 16px !important;
  }

  &--success {
    background-color: var(--toastify-color-success) !important;
    border: 1px solid #488506 !important;
  }

  &--warning {
    background-color: var(--toastify-color-warning) !important;
    border: 1px solid #f1c21b !important;
  }

  &--error {
    background-color: var(--toastify-color-error) !important;
    border: 1px solid #f83b3b !important;
  }

  &--info {
    background-color: var(--toastify-color-info) !important;
    border: 1px solid #006fff !important;
  }
}
