.tippy-box[data-placement^="left"] > .tippy-arrow:before {
  content: "";
  position: absolute;
  border-color: transparent;
  border-style: solid;
  border-width: 8px 0 8px 8px;
  border-left-color: initial;
  right: -7px;
  z-index: 3;
  transform-origin: center left;
}

.tippy-box[data-placement^="left"] > .tippy-arrow:after {
  content: "";
  position: absolute;
  border-color: transparent;
  border-style: solid;
  border-width: 8px 0 9px 9px;
  border-left-color: #545454;
  right: -9px;
  z-index: 2;
  transform-origin: center left;
}

.tippy-box[data-placement^="right"] > .tippy-arrow:before {
  content: "";
  position: absolute;
  border-color: transparent;
  border-style: solid;
  left: -7px;
  z-index: 3;
  border-width: 8px 8px 8px 0;
  border-right-color: initial;
  transform-origin: center right;
}

.tippy-box[data-placement^="right"] > .tippy-arrow:after {
  content: "";
  position: absolute;
  border-color: transparent;
  border-style: solid;
  left: -9px;
  z-index: 2;
  border-width: 8px 9px 9px 0;
  border-right-color: #545454;
  transform-origin: center right;
}

.tippy-box[data-placement^="bottom"] > .tippy-arrow:before {
  content: "";
  position: absolute;
  border-color: transparent;
  border-style: solid;
  top: -7px;
  left: 0;
  z-index: 3;
  border-width: 0 8px 8px;
  border-bottom-color: #545454;
  transform-origin: center bottom;
}

.tippy-box[data-placement^="bottom"] > .tippy-arrow:after {
  content: "";
  position: absolute;
  border-color: transparent;
  border-style: solid;
  top: -9px;
  left: -1px;
  z-index: 2;
  border-width: 0 9px 9px;
  border-bottom-color: #545454;
  transform-origin: center bottom;
}

.tippy-box[data-placement^="top"] > .tippy-arrow:before {
  content: "";
  position: absolute;
  border-color: transparent;
  border-style: solid;
  bottom: -7px;
  z-index: 3;
  left: 0;
  border-width: 8px 8px 0;
  border-top-color: initial;
  transform-origin: center top;
}

.tippy-box[data-placement^="top"] > .tippy-arrow:after {
  content: "";
  position: absolute;
  border-color: transparent;
  border-style: solid;
  bottom: -9px;
  z-index: 2;
  left: -1px;
  border-width: 9px 9px 0;
  border-top-color: #545454;
  transform-origin: center top;
}
