import endpoints from "@/constants/api";
import {
  ICalculateBuyPriceFormFields,
  ICalculateSellPriceFormFields,
  ICalculateYtmFormFields,
  IErrorType,
  ISearchIsinByName,
  ISearchIsinByNameFilterText
} from "@/queries/calculatorAPI/types";
import { apiService } from "@/services/apiService";
import { useMutation } from "@tanstack/react-query";
import { AxiosError, AxiosResponse } from "axios";

export const usePostCalculateYtmMutation = () =>
  useMutation<AxiosResponse, AxiosError<IErrorType>, ICalculateYtmFormFields, unknown>({
    mutationFn: (Body: ICalculateYtmFormFields) => apiService.post(endpoints.postCalculateYtm, Body)
  });

export const usePostCalculateBuyPriceMutation = () =>
  useMutation<AxiosResponse, AxiosError<IErrorType>, ICalculateBuyPriceFormFields, unknown>({
    mutationFn: (Body: ICalculateBuyPriceFormFields) => apiService.post(endpoints.postCalculateBuyPrice, Body)
  });

export const usePostCalculateSellPriceMutation = () =>
  useMutation<AxiosResponse, AxiosError<IErrorType>, ICalculateSellPriceFormFields, unknown>({
    mutationFn: (Body: ICalculateSellPriceFormFields) => apiService.post(endpoints.postCalculateSellPrice, Body)
  });

export const useGetIsinByNameQuery = () =>
  useMutation<AxiosResponse<ISearchIsinByName>, AxiosError<IErrorType>, ISearchIsinByNameFilterText, unknown>({
    mutationFn: (Body: ISearchIsinByNameFilterText) => apiService.get(endpoints.getRecommendedBonds(Body))
  });
