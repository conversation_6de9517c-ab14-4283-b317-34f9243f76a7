export interface IErrorType {
  errorMessage: string;
}

export interface ICalculateYtmFormFields {
  isin: string;
  buyingDate: string;
  buyingPrice: number;
  sellingDate: string;
  sellingPrice: number;
}

export interface ICalculateBuyPriceFormFields {
  isin: string;
  ytmInPercent: number;
  buyingDate: string;
  sellingDate: string;
  sellingPrice: number;
}

export interface ICalculateSellPriceFormFields {
  isin: string;
  ytmInPercent: number;
  buyingDate: string;
  buyingPrice: number;
  sellingDate: string;
}

export interface ISearchIsinByName {
  data: ISearchIsinByNameData[];
  isSuccess: boolean;
  errorCode: null;
  errorMessage: null;
}
export interface ISearchIsinByNameData {
  isin: string;
  symbol: string;
  instrumentName: string;
  issuanceDate: string;
  maturityDate: string;
  faceValue: number;
}
export interface ISearchIsinByNameFilterText {
  FilterText: string;
}
