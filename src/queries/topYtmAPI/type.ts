import { IBaseResponseType } from "@/types/globalTypes";

export interface IGetTopYtmItemDetail {
  isin: string;
  closePrice: number;
  lastTradePrice: number;
  bestSell: number;
  bestBuy: number;
  closePriceYtm: number;
  lastTradePriceYtm: number;
  bestSellYtm: number;
  bestBuyYtm: number;
  lastTradePriceYtmChangeRate: number;
  closePriceYtmChangeRate: number;
  symbol: string;
  closePriceYtmToPercent: number;
  lastTradePriceYtmToPercent: number;
  bestSellYtmToPercent: number;
  bestBuyYtmToPercent: number;
}

export interface IGetTopYtmItem {
  item?: IGetTopYtmItemDetail;
  index?: number;
}

export interface IGetListOfTopYtm extends IBaseResponseType {
  data: IGetTopYtmItemDetail[];
}
