/* eslint-disable import/prefer-default-export */
import endpoints from "@/constants/api";
import { useQuery } from "@tanstack/react-query";
import { TAverageYtmResponse, TMarketMapResponse } from "./types";

export const useGetMarketMap = () =>
  useQuery<TMarketMapResponse>({
    queryKey: [endpoints.getMarketMap]
  });

export const useGetTodayAveragePriceYtm = () =>
  useQuery<TAverageYtmResponse>({
    queryKey: [endpoints.getTodayAverageYtm]
  });
