import { TBaseResponseType } from "@/types/globalTypes";

export interface TMarketMapData {
  bondType: 100 | 200;
  closePrice: number;
  dayHighPrice: number;
  dayLowPrice: number;
  instrumentName: string;
  isin: string;
  lastTradePrice: number;
  lastTradePriceYtmDifferentInPercent: number;
  symbol: string;
  lastTradePriceYtmInPercent: number;
  totalNumberOfTrades: number;
  totalTradeValue: number;
  totalTradedVolume: number;
  value?: (number | null)[];
}

export type TMarketMapResponse = TBaseResponseType<TMarketMapData[]>;

export interface AverageYtm {
  couponBondTodayAverageYtmInPercent: number;
  zeroCouponBondTodayAverageYtmInPercent: number;
  couponBondLastDayAverageYtmInPercent: number;
  zeroCouponBondLastDayAverageYtmInPercent: number;
}

export type TAverageYtmResponse = TBaseResponseType<AverageYtm>;
