import { TBaseResponseType } from "@/types/globalTypes";

export type TAverageChart = {
  couponAverageYtm: number;
  zeroCouponAverageYtm: number;
  persianMonth: string;
};

export type TAverageChartResponse = TBaseResponseType<TAverageChart[]>;

export interface TTodayAverage {
  couponBondAverageYtmChangeInPercent: number;
  couponBondLastDayAverageYtmInPercent: number;
  couponBondTodayAverageYtmInPercent: number;
  zeroCouponBondAverageYtmChangeInPercent: number;
  zeroCouponBondLastDayAverageYtmInPercent: number;
  zeroCouponBondTodayAverageYtmInPercent: number;
}

export type TTodayAverageResponse = TBaseResponseType<TTodayAverage>;

export type TValueChart = {
  totalCouponTradeValue: number;
  totalZeroCouponTradeValue: number;
  persianMonth: string;
};

export type TValueChartResponse = TBaseResponseType<TValueChart[]>;

export type TValueDurationChart = {
  couponAverageDuration: number;
  zeroCouponAverageDuration: number;
  persianMonth: string;
};

export type TValueChartDurationResponse = TBaseResponseType<TValueDurationChart[]>;

export type TTotalValueData = {
  couponTotalValue: number;
  couponTotalValueChangePercent: number;
  couponTotalValueChangeRate: number;
  zeroCouponTotalValue: number;
  zeroCouponTotalValueChangePercent: number;
  zeroCouponTotalValueChangeRate: number;
};

export type TTotalValueDataResponse = TBaseResponseType<TTotalValueData>;

export type TTotalValueDurationData = {
  couponBondAverageDurationChangeInPercent: number;
  couponBondLastDayAverageDuration: number;
  couponBondTodayAverageDuration: number;
  zeroCouponBondAverageDurationChangeInPercent: number;
  zeroCouponBondLastDayAverageDuration: number;
  zeroCouponBondTodayAverageDuration: number;
};

export type TTotalValueDurationResponse = TBaseResponseType<TTotalValueDurationData>;
