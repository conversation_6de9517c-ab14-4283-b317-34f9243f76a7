/* eslint-disable import/prefer-default-export */
import endpoints from "@/constants/api";
import { useQuery } from "@tanstack/react-query";
import {
  TAverageChartResponse,
  TTodayAverageResponse,
  TTotalValueDataResponse,
  TValueChartDurationResponse,
  TValueChartResponse
} from "./types";

export const useGetLastYearMonthlyYtmAverageQuery = () =>
  useQuery<TAverageChartResponse>({
    queryKey: [endpoints.GetLastYearMonthlyYtmAverage]
  });

export const useGetTodayAverageYtmQuery = () =>
  useQuery<TTodayAverageResponse>({
    queryKey: [endpoints.GetTodayAverageYtm]
  });

export const useGetYearlyTradeValuesQuery = () =>
  useQuery<TTotalValueDataResponse>({
    queryKey: [endpoints.GetYearlyTradeValues]
  });

export const useGetDashboardTotalValueChartQuery = () =>
  useQuery<TValueChartResponse>({
    queryKey: [endpoints.GetDashboardTotalValueChart]
  });

export const useGetTotalChartValueDurationQuery = () =>
  useQuery<TValueChartDurationResponse>({
    queryKey: [endpoints.GetTotalChartValueDuration]
  });
