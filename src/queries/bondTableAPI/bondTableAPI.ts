/* eslint-disable import/prefer-default-export */
import endpoints from "@/constants/api";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { AxiosError, AxiosResponse } from "axios";
import { apiService } from "@/services/apiService";
import { TBondTableParams, TBondTableResponse, TTBondTablePinParams } from "./types";
import { IErrorType } from "../calculatorAPI/types";

export const BOND_TABLE = "bond_table";
export const BOND_TABLE_PIN = "bond_table_pin";

export const useGetBondTableQuery = (params: TBondTableParams) =>
  useQuery<TBondTableResponse, AxiosError<IErrorType>>({
    queryKey: [BOND_TABLE, params],
    queryFn: () => apiService.get(endpoints.bondTable(params)).then(res => res.data)
  });

export const useGetBondTableAllQuery = () => useMutation<AxiosResponse, AxiosError<IErrorType>>({
    mutationFn: () => apiService.get(endpoints.bondExcel).then(res => res.data)
  });

export const useGetBondTablePinQuery = (params: TBondTableParams) =>
  useQuery<TBondTableResponse, AxiosError<IErrorType>>({
    queryKey: [BOND_TABLE_PIN, params],
    queryFn: () => apiService.get(endpoints.bondTablePin(params)).then(res => res.data)
  });

export const useAddToPinMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<TBondTableResponse, AxiosError, TTBondTablePinParams>({
    mutationFn: ({ isin }: TTBondTablePinParams) =>
      apiService.post(endpoints.tablePin(), { isin })?.then(res => {
        queryClient.invalidateQueries({
          queryKey: [BOND_TABLE_PIN]
        });

        return res.data;
      })
  });
};
export const useRemovePinMutation = () => {
  const queryClient = useQueryClient();

  return useMutation<TBondTableResponse, AxiosError, TTBondTablePinParams>({
    mutationFn: ({ isin }: TTBondTablePinParams) =>
      apiService.delete(endpoints.tablePin(isin))?.then(res => {
        queryClient.invalidateQueries({
          queryKey: [BOND_TABLE_PIN]
        });

        return res.data;
      })
  });
};
