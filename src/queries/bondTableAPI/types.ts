import { TBaseResponseType } from "@/types/globalTypes";

export type TBondTableData = {
  bestBuyPrice: number;
  // بهترین قیمت خرید

  bestBuyPriceYtm: number;
  // بازدهی بهترین قیمت خرید ، برای نشان دادن کنار فلش رو به بالا یا پایین

  bestSellPrice: number;
  // بهترین قیمت فروش

  bestSellPriceYtm: number;
  // بازدهی بهترین قیمت فروش

  bondType: string;
  // نوع اوراق

  bondTypeName: string;
  // نام نوع اوراق

  closePrice: number;
  // قیمت پایانی

  closingPriceYtm: number;
  // بادهی قیمت پایانی ، برای نشان دادن کنار فلش رو به بالا یا پایین

  closePriceYtmYesterdayChange: number;
  //  قیمت چک کردن منفی و مثبت

  faceValue: number;
  // قیمت اسمی

  firstPaymentDate: string;
  // تاریخ اولین پرداخت

  instrumentName: string;
  // نام نماد

  isin: string;
  // ایزین

  issuanceDate: string;
  // زمان صدور

  lastTradePrice: number;
  // اخرین قیمت

  lastTradePriceYtm: number;
  // عدد زیر اخرین قیمت ytm

  lastTradePriceYtmYesterdayChange: number;
  // چک کردن مثبت منفی

  maturityDate: string;
  //  تاریخ سررسید

  daysToMaturityDate: number;
  // تعداد روز مانده

  nominalInterestRate: number;
  // نرخ بازدهی

  paymentCount: number;
  // تعداد پرداخت در سال

  publisher: string;
  // نام ناشر

  symbol: string;
  // نام اوراق

  totalSheetsAmount: number;
  // تعداد اوراق منتشر شده

  lastTradeDate: string;
  // تاریخ معامله

  lastTradeTime: string;
  // زمان مغامله

  totalTradedVolume: number;
  // حجم معامله

  duration: number;
  // دیرش

  modifiedDuration: number;
  // دیرش تعدیل شده

  convexity: number;
  // تحدب

  dayHighPrice: number;
  // بالاترین قیمت

  dayLowPrice: number;
  // کمترین قیمت

  totalNumberOfTrades: number;
  // تعداد معاملات

  totalTradeValue: number;
  // ارزش معاملات

  openPriceYtmInPercent: number;
  // قیمت بازگشایی به درصد

  closePriceYtmInPercent: number;
  // قیمت پایانی به درصد

  lastTradePriceYtmInPercent: number;
  // آخرین قیمت به درصد

  bestBuyYtmInPercent: number;
  // بهترین قیمت خرید به درصد

  bestSellYtmInPercent: number;
  // بهترین قیمت فروش به درصد

  dayHighPriceYtmInPercent: number;
  // بالاترین قیمت روز به درصد

  dayLowPriceYtmInPercent: number;
  // پایین ترین قیمت روز به درصد
};

export type TBondTableResponse = TBaseResponseType<{ items: TBondTableData[]; totalCount: number }>;

export type TBondTableParams = {
  pageSize?: number | null;
  pageNumber?: number | null;
};

export type TTBondTablePinParams = {
  isin: string;
};
