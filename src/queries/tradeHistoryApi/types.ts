export interface IDataMarketValueItem {
  tradeDate: string;
  totalCouponTradeValue: number;
  totalZeroCouponTradeValue: number;
}

export interface IGetListOfMarketValues {
  data: {
    yearTradeValueDailySum: {
      dailyTradeValueViewModels: IDataMarketValueItem[];
    };
  };
}

export interface IGetListOfDurationData {
  couponAverageDuration: number;
  couponAverageDurationInPercent?: number;
  date: string;
  persianDay?: string;
  zeroCouponAverageDuration: number;
  zeroCouponAverageDurationInPercent?: number;
}
export interface IGetListOfDurationValues {
  data: IGetListOfDurationData[];
}

export interface IDataMarketYtmItem {
  date: string;
  couponAverageYtm: number;
  couponAverageYtmInPercent: number;
  zeroCouponAverageYtm: number;
  zeroCouponAverageYtmInPercent: number;
}

export interface IGetListOfMarketYtm {
  data: IDataMarketYtmItem[];
}
