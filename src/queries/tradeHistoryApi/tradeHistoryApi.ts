import { AxiosError } from "axios";
import endpoints from "@/constants/api";
import { useQuery } from "@tanstack/react-query";
import { IDurationHistoryParams, ITradeHistoryParams } from "@/containers/tradeHistory/types";
import { IGetListOfDurationValues, IGetListOfMarketValues, IGetListOfMarketYtm } from "./types";
import { IErrorType } from "../calculatorAPI/types";

export const useGetTradeHistoryQuery = (p: ITradeHistoryParams) =>
  useQuery<IGetListOfMarketValues, AxiosError<IErrorType>>({
    queryKey: [endpoints.getTradeValues(p)],
    enabled: p.isEnabled
  });

export const useGetTradeYtmHistoryQuery = (p: ITradeHistoryParams) =>
  useQuery<IGetListOfMarketYtm>({
    queryKey: [endpoints.getTradeYtm(p)],
    enabled: p.isEnabled
  });

export const useGetDurationHistoryQuery = (p: IDurationHistoryParams) =>
  useQuery<IGetListOfDurationValues, AxiosError<IErrorType>>({
    queryKey: [endpoints.getDurationValues(p)],
    enabled: p.isEnabled
  });
