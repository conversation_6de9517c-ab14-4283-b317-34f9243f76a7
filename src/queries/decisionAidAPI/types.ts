import { TBaseResponseType } from "@/types/globalTypes";

export type TBondTableData = {
  bestBuyPrice: number;
  // بهترین قیمت خرید

  bestBuyPriceYtm: number;
  // بازدهی بهترین قیمت خرید ، برای نشان دادن کنار فلش رو به بالا یا پایین

  bestSellPrice: number;
  // بهترین قیمت فروش

  bestSellPriceYtm: number;
  // بازدهی بهترین قیمت فروش

  bondType: string;
  // نوع اوراق

  bondTypeName: string;
  // نام نوع اوراق

  closePrice: number;
  // قیمت پایانی

  closingPriceYtm: number;
  // بادهی قیمت پایانی ، برای نشان دادن کنار فلش رو به بالا یا پایین

  closePriceYtmYesterdayChange: number;
  //  قیمت چک کردن منفی و مثبت

  faceValue: number;
  // قیمت اسمی

  firstPaymentDate: string;
  // تاریخ اولین پرداخت

  instrumentName: string;
  // نام نماد

  isin: string;
  // ایزین

  issuanceDate: string;
  // زمان صدور

  lastTradePrice: number;
  // اخرین قیمت

  lastTradePriceYtm: number;
  // عدد زیر اخرین قیمت ytm

  lastTradePriceYtmYesterdayChange: number;
  // چک کردن مثبت منفی

  maturityDate: string;
  //  تاریخ سررسید

  daysToMaturityDate: number;
  // تعداد روز مانده

  nominalInterestRate: number;
  // نرخ بازدهی

  paymentCount: number;
  // تعداد پرداخت در سال

  publisher: string;
  // نام ناشر

  symbol: string;
  // نام اوراق

  totalSheetsAmount: number;
  // تعداد اوراق منتشر شده

  lastTradeDate: string;
  // تاریخ معامله

  lastTradeTime: string;
  // زمان مغامله

  totalTradedVolume: number;
  // حجم معامله

  duration: number;
  // دیرش

  modifiedDuration: number;
  // دیرش تعدیل شده

  convexity: number;
  // تحدب

  dayHighPrice: number;
  // بالاترین قیمت

  dayLowPrice: number;
  // کمترین قیمت

  totalNumberOfTrades: number;
  // تعداد معاملات

  totalTradeValue: number;
  // ارزش معاملات

  openPriceYtmInPercent: number;
  // قیمت بازگشایی به درصد

  closePriceYtmInPercent: number;
  // قیمت پایانی به درصد

  lastTradePriceYtmInPercent: number;
  // آخرین قیمت به درصد

  bestBuyYtmInPercent: number;
  // بهترین قیمت خرید به درصد

  bestSellYtmInPercent: number;
  // بهترین قیمت فروش به درصد

  dayHighPriceYtmInPercent: number;
  // بالاترین قیمت روز به درصد

  dayLowPriceYtmInPercent: number;
  // پایین ترین قیمت روز به درصد
};

export type TBondTableParams = {
  pageSize: number;
  pageNumber: number;
};

export interface IGetDecisionAidData {
  data: IGetDecisionAidDataItems[];
}
export interface IGetDecisionAidDataItems {
  key?: string | number;
  isin: string;
  symbol: string;
  bondType: number;
  callRecords: IGetDecisionAidDataItemsOrders[];
  daysToMaturity: number;
  totalVolume: number;
  originalOrderBookTotalVolume: number;
  orders: IGetDecisionAidDataItemsOrders[];
  totalTradeCost: number;
  totalCompoundInterest: number;
  accumulatedVolume: number;
  accumulatedCompoundInterest: number;
  accumulatedTotalTradePrice: number;
  maturityDate: string;
  ytm: number;
  ytmInPercent: number;
}

export interface IGetDecisionAidDataItemsOrders {
  isin: string;
  key?: string | number;
  ytm: number;
  ytmInPercent: number;
  symbol: string;
  price: number;
  originalOrderVolume: number;
  isinOrderBookYtm: number;
  isinOrderBookYtmInPercent: number;
  chosenVolume: number;
  isSuggested: boolean;
  isPurchasable: boolean;
  daysToMaturity: number;
  totalTradeCost: number;
  accumulatedVolume: number;
  accumulatedCompoundInterest: number;
  accumulatedTotalTradePrice: number;
  compoundInterestPrice: number;
}

export interface IParamsDecisionAidData {
  pageSize?: number | null;
  pageNumber?: number | null;
  MinimumYtm?: number | string | null;
  MinimumMaturityRemainedDays?: number | string | null;
  MaximumMaturityRemainedDays?: number | string | null;
  BondType?: number | null;
  MinimumInvestmentPerBond?: number | string | null;
  MaximumInvestmentBudget?: number | string | null;
}

export interface IGetSingleDecisionAidData {
  data: IGetSingleDecisionAidDataItems[];
}
export interface IGetSingleDecisionAidDataItems {
  isin: string;
  isSuggested: boolean;
  isPurchasable: boolean;
  symbol: string;
  price: number;
  bondType: number;
  daysToMaturity: number;
  totalVolume: number;
  chosenVolume: number;
  originalOrderBookTotalVolume: number;
  orders: IGetDecisionAidDataItemsOrders[];
  totalTradeCost: number;
  totalCompoundInterest: number;
  accumulatedVolume: number;
  accumulatedCompoundInterest: number;
  accumulatedTotalTradePrice: number;
  maturityDate: string;
  ytm: number;
  ytmInPercent: number;
}

export type TTotalValueDataResponse = TBaseResponseType<IGetDecisionAidData>;
export type TTotalValueSingleDataResponse = TBaseResponseType<IGetSingleDecisionAidData>;
