/* eslint-disable import/prefer-default-export */
import endpoints from "@/constants/api";
import { useMutation } from "@tanstack/react-query";
import { AxiosError } from "axios";
import { apiService } from "@/services/apiService";
import { IParams } from "@/app/(home)/(decisionAid)/components/Sidebar/type";
import { IParamsDecisionAidData, TTotalValueDataResponse, TTotalValueSingleDataResponse } from "./types";
import { IErrorType } from "../calculatorAPI/types";

export const BOND_TABLE = "bond_table";

export const useGetAggregatedInvestmentSuggestionsQuery = (params: IParams) =>
  useMutation<TTotalValueDataResponse, AxiosError<IErrorType>, IParamsDecisionAidData, unknown>({
    mutationFn: () => apiService.get(endpoints.getAggregatedInvestmentSuggestions(params))
  });

export const useGetSingleInvestmentSuggestionsQuery = (params: IParams) =>
  useMutation<TTotalValueSingleDataResponse, AxiosError<IErrorType>, IParamsDecisionAidData, unknown>({
    mutationFn: () => apiService.get(endpoints.getSingleInvestmentSuggestions(params))
  });
