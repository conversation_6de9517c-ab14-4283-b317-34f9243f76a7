/* eslint-disable import/no-cycle */
/* eslint-disable import/prefer-default-export */
import endpoints from "@/constants/api";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { AxiosError, AxiosResponse } from "axios";
import { apiService } from "@/services/apiService";
import { MAX_ROWS } from "@/utils/helpers";
import { IAddSymbolPost, IWatchListData, TBondTableParams, TBondTableResponse } from "./types";
import { IErrorType } from "../calculatorAPI/types";

export const BOND_TABLE = "bond_table";

export const useGetWatchListQuery = (params: TBondTableParams) =>
  useQuery<TBondTableResponse, AxiosError<IErrorType>>({
    queryKey: [BOND_TABLE, params],
    queryFn: () => apiService.get(endpoints.watchList(params)).then(res => res.data)
  });

export const useGetWatchListAllQuery = () => {
  const params: TBondTableParams = {
    pageNumber: 1,
    pageSize: MAX_ROWS
  };

  return useMutation<AxiosResponse, AxiosError<IErrorType>>({
    mutationFn: () => apiService.get(endpoints.watchList(params)).then(res => res.data)
  });
};

export const usePostAddSymbolToWatchListMutation = (params: TBondTableParams) => {
  const queryClient = useQueryClient();
  return useMutation<AxiosResponse, AxiosError<IErrorType>, IAddSymbolPost, unknown>({
    mutationFn: (Body: IAddSymbolPost) => apiService.post(endpoints.postAddSymbolToWatchList, Body),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [BOND_TABLE, params] });
    }
  });
};

export const useDeleteSymbolFromWatchListMutation = (params: TBondTableParams) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (id: string) => apiService.delete(endpoints.deleteOneSymbol(id)),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [BOND_TABLE, params] });
    }
  });
};

export const useGetWatchListSearchQuery = (params: TBondTableParams) =>
  useQuery<IWatchListData, AxiosError<IErrorType>>({
    queryKey: ["BOND_TABLE_SEARCH"],
    queryFn: () => apiService.get(endpoints.getWatchListSearch(params)).then(res => res.data)
  });
