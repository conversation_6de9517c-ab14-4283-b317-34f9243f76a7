import { TBaseResponseType } from "@/types/globalTypes";

export type TLoginBodyParams = {
  username: string;
  password: string;
  captchaToken?: string;
};

export type TLoginBody = TBaseResponseType<{ token: string }>;

export type TResetPassParams = {
  firstName: string;
  lastName: string;
  mobileNumber: string;
  description: string;
  captchaToken?: string;
};

export type TResetPassBody = TBaseResponseType<{}>;
