import { AxiosError } from "axios";
import endpoints from "@/constants/api";
import { apiService } from "@/services/apiService";
import { useMutation } from "@tanstack/react-query";
import { TLoginBody, TLoginBodyParams, TResetPassBody, TResetPassParams } from "./types";

export const useLoginMutation = () =>
  useMutation<TLoginBody, AxiosError, TLoginBodyParams>({
    mutationFn: ({ username, password, captchaToken }: TLoginBodyParams) =>
      apiService
        .post(
          endpoints.login,
          { username, password },
          {
            headers: {
              "Content-Type": "multipart/form-data",
              "captcha-code": captchaToken
            }
          }
        )
        ?.then(res => res.data)
  });

export const useAddTicketMutation = () =>
  useMutation<TResetPassBody, AxiosError, TResetPassParams>({
    mutationFn: ({ description, firstName, lastName, mobileNumber, captchaToken }: TResetPassParams) =>
      apiService
        .post(
          endpoints.addTicket,
          { description, firstName, lastName, mobileNumber },
          {
            headers: {
              "captcha-code": captchaToken
            }
          }
        )
        ?.then(res => res.data)
  });
