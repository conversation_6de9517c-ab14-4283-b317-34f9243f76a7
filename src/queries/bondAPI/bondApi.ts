/* eslint-disable import/prefer-default-export */
import endpoints from "@/constants/api";
import { useQuery } from "@tanstack/react-query";
import { apiService } from "@/services/apiService";
import { IGetBondChartParams, TBondResponse, TOrderBokResponse } from "./types";

export const ORDER_TABLE = "ORDER_TABLE";

// export const useGetAllBondsQuery = () =>
//   useQuery<TBondResponse>({
//     queryKey: [endpoints.bonds]
//   });

export const useGetBondQuery = (id?: string) =>
  useQuery<TBondResponse>({
    queryKey: [endpoints.bond(id)],
    enabled: !!id
  });

export const useGetOrderBookQuery = (id?: string) =>
  useQuery<TOrderBokResponse>({
    queryKey: [ORDER_TABLE, id],
    queryFn: () => apiService.get(endpoints.orderBook(id)).then(res => res.data),
    enabled: !!id
  });

export const useGetBondChartQuery = (params: IGetBondChartParams) =>
  useQuery<TBondResponse>({
    queryKey: [endpoints.getBondChart(params)]
  });
