import { TBaseResponseType } from "@/types/globalTypes";

export interface IBondLastDayTrade {
  tradesCount: number;
  value: number;
  volume: number;
  highPrice: number;
  highPriceYtm: number;
  lowPrice: number;
  lowPriceYtm: number;
  closePrice: number;
  closePriceYtm: number;
  openingPrice: number;
  openingPriceYtm: number;
  lastTradePrice: number;
  lastTradePriceYtm: number;
  tradeDate: string;
}

export type TBondData = {
  isin: string;
  symbol: string;
  instrumentName: string;
  publisher: string;
  issuanceDate: string;
  maturityDate: string;
  faceValue: number;
  nominalInterestRate: number;
  paymentCount: number;
  firstPaymentDate: string;
  totalSheetsAmount: number;
  bondType: 100 | 200;
  bondTypeName: string;
  bondLastDayTrade: IBondLastDayTrade;
  closePrice: number;
  lastTradePrice: number;
  lastTradeDate: string;
  lastTradeTime: string;
  lastTradePriceYtm: number;
  lastTradePriceYtmInPercent?: number;
};

export type TBondResponse = TBaseResponseType<TBondData>;

export interface IOrderBook {
  buyYtm: number;
  buyPriceYtmToPercent: number;
  buyVolume: number;
  buyPrice: number;
  buyPercent: number;
  sellPrice: number;
  sellVolume: number;
  sellYtm: number;
  sellPriceYtmToPercent: number;
  sellPercent: number;
  index: number;
  buyAmount?: number;
  buyTime?: string;
  sellAmount?: number;
  sellTime?: string;
}
export type TOrderBokResponse = TBaseResponseType<IOrderBook[]>;

export interface IGetBondChartParams {
  id: string;
  fromDate: string;
  toDate: string;
}
