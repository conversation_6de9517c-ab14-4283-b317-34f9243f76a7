import { useQuery } from "@tanstack/react-query";

import { useVarDateFilterStore } from "@/store/varDateFilter";
import endpoints from "@/constants/api";
import { IVarFilterParams } from "@/components/molecules/DateFilter/types";

import {
  IGetListOfFundConvexity,
  IGetListOfFundCouponMarketDuration,
  IGetListOfFundDuration,
  IGetListOfFundInterestSensitivity,
  IGetListOfFundInterestSensitivityByChange,
  IGetListOfFundMarketValueWeightedAverage,
  IGetListOfFundYtm,
  IGetListOfFundZeroCouponMarketDuration,
  IGetPiePortfolio,
  IGetPortfolioWithPurchaseDetail
} from "./types";

export const useGetFundYtmHistoryChart = (params: IVarFilterParams) => {
  const { period } = useVarDateFilterStore();
  return useQuery<IGetListOfFundYtm>({
    queryKey: [endpoints.getFundYtmHistoryChart({ ...params, ...period })]
  });
};

export const useGetFundConvexityHistoryChart = (params: IVarFilterParams) => {
  const { period } = useVarDateFilterStore();
  return useQuery<IGetListOfFundConvexity>({
    queryKey: [endpoints.getFundConvexityHistoryChart({ ...params, ...period })]
  });
};
export const useGetFundDurationHistoryChart = (params: IVarFilterParams) => {
  const { period } = useVarDateFilterStore();
  return useQuery<IGetListOfFundDuration>({
    queryKey: [endpoints.getFundDurationHistoryChart({ ...params, ...period })]
  });
};

export const useGetRiskFreeRateHistoryChart = () => {
  const { period } = useVarDateFilterStore();
  return useQuery<IGetListOfFundMarketValueWeightedAverage>({
    queryKey: [endpoints.getRiskFreeRateHistoryChart(period as IVarFilterParams)]
  });
};

export const useGetMarketCouponDurationHistoryChart = () => {
  const { period } = useVarDateFilterStore();
  return useQuery<IGetListOfFundCouponMarketDuration>({
    queryKey: [endpoints.getMarketCouponBondsDurationHistoryChart(period as IVarFilterParams)]
  });
};

export const useGetMarketZeroCouponDurationHistoryChart = () => {
  const { period } = useVarDateFilterStore();
  return useQuery<IGetListOfFundZeroCouponMarketDuration>({
    queryKey: [endpoints.getMarketZeroCouponBondsDurationHistoryChart(period as IVarFilterParams)]
  });
};

export const useGetInterestSensitivitiesChart = (interestChange: number) =>
  useQuery<IGetListOfFundInterestSensitivity>({
    queryKey: [endpoints.getFundInterestSensitivities(interestChange)]
  });

export const useGetPiePortfolio = () =>
  useQuery<IGetPiePortfolio>({
    queryKey: [endpoints.getPiePortfolio]
  });

export const useGetPortfolioWithPurchaseDetail = (params: any) =>
  useQuery<IGetPortfolioWithPurchaseDetail>({
    queryKey: [endpoints.getPortfolioWithPurchaseDetail({ ...params })]
  });

export const useGetInterestSensitivitiesByChangeChart = () =>
  useQuery<IGetListOfFundInterestSensitivityByChange>({
    queryKey: [endpoints.getFundInterestSensitivitiesByChange()]
  });
