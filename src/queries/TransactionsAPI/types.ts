export type ITransactionsParams = {
  fromDate?: string;
  toDate?: string;
  filterByPurchaseType?: string;
};

export type ITransactionsTableSort = "ASc" | "Desc";

export type ITransactionsTableParams = {
  fromDate?: string;
  toDate?: string;
  symbol?: string;
  filterByPurchaseType?: string;
  pageSize: number;
  pageNumber: number;
  SortCheckpointDate?: ITransactionsTableSort;
  SortReasonCategory?: ITransactionsTableSort;
  SortSymbol?: ITransactionsTableSort;
  SortAmount?: ITransactionsTableSort;
  SortPurchaseType?: ITransactionsTableSort;
  SortContractNumber?: ITransactionsTableSort;
  SortBondOriginType?: ITransactionsTableSort;
};

export type PurchaseType = 100 | 200;

export type Transaction = {
  reasonCategory: number;
  symbol: string;
  reasonDescription: string;
  contractNumber: string | null;
  bondOriginType: number;
  amount: number;
  purchaseType: PurchaseType;
};

type GraphPoint = {
  checkpointDate: string; // ISO date string
  totalDebitAmount: number;
  totalDepositAmount: number;
  depositTransactions: Transaction[];
  withdrawalTransactions: Transaction[];
};

export type FlatTransaction = Transaction & {
  flowType: 100 | 200;
  transactionDate: string;
};

export type ITransactionsResponse = {
  data: {
    graphPoints: GraphPoint[];
  };
  isSuccess: boolean;
  errorCode: string | null;
  errorMessage: string | null;
};

export type ITransactionsTableResponse = {
  data: {
    items: FlatTransaction[];
    totalCount: number;
  };
  isSuccess: boolean;
  errorCode: string | null;
  errorMessage: string | null;
};
