/* eslint-disable import/prefer-default-export */
// eslint-disable-next-line import/prefer-default-export

import endpoints from "@/constants/api";
import { useQuery } from "@tanstack/react-query";
import { useVarDateFilterStore } from "@/store/varDateFilter";
import {
  ITransactionsParams,
  ITransactionsResponse,
  ITransactionsTableParams,
  ITransactionsTableResponse
} from "./types";

export const useGetTransactionsChart = (params: ITransactionsParams) => {
  const { period } = useVarDateFilterStore();
  return useQuery<ITransactionsResponse>({
    queryKey: [endpoints.getTransactionsChart({ ...params, ...period })]
  });
};

export const useGetTransactionsTable = (params: ITransactionsTableParams) => {
  const { period } = useVarDateFilterStore();
  return useQuery<ITransactionsTableResponse>({
    queryKey: [endpoints.getTransactionsTable({ ...params, ...period })]
  });
};
