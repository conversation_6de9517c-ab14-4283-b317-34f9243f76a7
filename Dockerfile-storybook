FROM node:18-alpine AS base

# Set the working directory in the container

WORKDIR /app

# Copy package.json and package-lock.json to the working directory

COPY package\*.json ./

# Install project dependencies

# Build Storybook using the build-storybook command

RUN yarn install

# Copy the entire project directory into the container

COPY . .

RUN yarn build-storybook

# Expose the port where you want to serve Storybook (e.g., 3008)

EXPOSE 3008

# Serve the generated storybook-static folder

CMD ["npx", "http-server", "storybook-static", "-p", "3008"]
