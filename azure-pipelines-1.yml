---
trigger:
  - storybook
pool:
  name: default
stages:
  - stage: Build
    displayName: Build storybook
    jobs:
      - job: Build
        steps:
          - script: docker login --username test --password test nexus.otcsaba.ir:8082
          - script: docker build -f ./Dockerfile-storybook -t storybook:$(Build.BuildId) .
          - script: docker tag storybook:$(Build.BuildId)
              nexus.otcsaba.ir:8082/customerloyalty/develop/storybook:$(Build.BuildId)
          - script: docker push nexus.otcsaba.ir:8082/customerloyalty/develop/storybook:$(Build.BuildId)
  - stage: Deploy
    displayName: Deploy storybook
    jobs:
      - job: Deploy
        steps:
          - task: SSH@0
            inputs:
              sshEndpoint: CustomerLoyaltyDevelop
              runOptions: commands
              commands: >
                docker pull nexus.otcsaba.ir:8082/customerloyalty/develop/storybook:$(Build.BuildId);
                docker service update --image nexus.otcsaba.ir:8082/customerloyalty/develop/storybook:$(Build.BuildId) storybook --with-registry-auth
              readyTimeout: "20000"
            timeoutInMinutes: 20
