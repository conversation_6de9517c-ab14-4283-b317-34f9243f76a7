---
trigger:
  - demo
pool:
  name: default
stages:
  - stage: Build
    displayName: Build demo
    jobs:
      - job: Build
        steps:
          - script: docker login --username test --password test nexus.otcsaba.ir:8082
          - script: docker build -f ./Dockerfile -t frontend2:$(Build.BuildId) .
          - script: docker tag frontend2:$(Build.BuildId)
              nexus.otcsaba.ir:8082/bond/demo/frontend2:$(Build.BuildId)
          - script: docker push nexus.otcsaba.ir:8082/bond/demo/frontend2:$(Build.BuildId)
  - stage: Deploy
    displayName: Deploy demo
    jobs:
      - job: Deploy
        steps:
          - task: SSH@0
            inputs:
              sshEndpoint: BondDemo
              runOptions: commands
              commands: >
                docker pull nexus.otcsaba.ir:8082/bond/demo/frontend2:$(Build.BuildId);
                docker service update --image nexus.otcsaba.ir:8082/bond/demo/frontend2:$(Build.BuildId) frontend --with-registry-auth
              readyTimeout: "20000"
            timeoutInMinutes: 20
