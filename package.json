{"name": "nextjs13-boilerplate", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "yarn lint --fix", "prettier": "prettier --check .", "prettier:fix": "prettier --write .", "check-types": "tsc --pretty", "prepare": "husky install", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@ag-grid-community/client-side-row-model": "^31.3.2", "@ag-grid-community/core": "^31.3.2", "@ag-grid-community/react": "^31.3.2", "@ag-grid-community/styles": "^31.3.2", "@ag-grid-enterprise/column-tool-panel": "^31.3.2", "@ag-grid-enterprise/master-detail": "^31.3.2", "@hookform/resolvers": "^3.3.4", "@microsoft/signalr": "^8.0.0", "@popperjs/core": "^2.11.8", "@react-spring/web": "^9.7.3", "@reactutils/use-delayed-stack": "^1.0.3", "@svgr/webpack": "^8.1.0", "@tanstack/react-query": "^5.29.0", "@tippyjs/react": "^4.2.6", "@ts-react/form": "^1.8.3", "@types/lodash": "^4.17.0", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "ag-grid-community": "^29.3.4", "ag-grid-react": "^29.3.4", "axios": "^1.6.8", "dayjs": "^1.11.10", "echarts": "^5.5.0", "echarts-for-react": "^3.0.2", "eslint": "^8.56.0", "eslint-config-next": "14.0.4", "headless-react-datepicker": "^1.1.9", "highcharts": "^11.4.1", "highcharts-react-official": "^3.2.1", "jalaliday": "^2.3.0", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "motion": "^11.18.2", "next": "14.0.4", "nuqs": "^1.20.0", "path-to-regexp": "^6.2.2", "rc-dialog": "9.3.3", "rc-slider": "^10.5.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-draggable": "^4.4.6", "react-google-recaptcha-v3": "^1.10.1", "react-hook-form": "^7.51.2", "react-loading-skeleton": "^3.4.0", "react-paginate": "^8.2.0", "react-popper": "^2.3.0", "react-select": "^5.8.0", "react-toastify": "^10.0.5", "react-tooltip": "^5.28.0", "sass": "^1.74.1", "swiper": "^11.1.1", "tailwind-merge": "^2.2.2", "tippy.js": "^6.3.7", "typescript": "^5.3.3", "xlsx": "^0.18.5", "zod": "^3.22.4", "zustand": "^4.5.2"}, "devDependencies": {"@commitlint/cli": "^17.8.1", "@commitlint/config-conventional": "^17.8.1", "@storybook/addon-essentials": "^7.6.6", "@storybook/addon-interactions": "^7.6.6", "@storybook/addon-links": "^7.6.6", "@storybook/addon-onboarding": "^1.0.10", "@storybook/blocks": "^7.6.6", "@storybook/nextjs": "^7.6.6", "@storybook/react": "^7.6.6", "@storybook/test": "^7.6.6", "@types/js-cookie": "^3.0.3", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "autoprefixer": "^10.4.16", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^17.1.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-storybook": "^0.6.15", "husky": "^9.1.7", "lint-staged": "^14.0.1", "postcss": "^8.4.32", "prettier": "^3.1.1", "prettier-plugin-tailwindcss": "^0.5.9", "sass": "^1.58.3", "storybook": "^7.6.6", "tailwindcss": "^3.4.0", "validate-branch-name": "^1.3.0"}, "validate-branch-name": {"pattern": "^(master|main|develop){1}$|^(build|chore|ci|docs|feat|fix|perf|refactor|revert|style|test)/.+$", "errorMsg": "Please follow standard branch name, rename branch using: git branch -m <oldname> <newname>"}, "packageManager": "yarn@1.22.19+sha512.ff4579ab459bb25aa7c0ff75b62acebe576f6084b36aa842971cf250a5d8c6cd3bc9420b22ce63c7f93a0857bc6ef29291db39c3e7a23aab5adfd5a4dd6c5d71"}