{"extends": ["next/core-web-vitals", "airbnb", "airbnb-typescript", "airbnb/hooks", "prettier", "plugin:storybook/recommended"], "parserOptions": {"project": ["./tsconfig.json", "./jsconfig.json"]}, "rules": {"react/react-in-jsx-scope": 0, "react/jsx-props-no-spreading": 0, "react/require-default-props": 0, "react-hooks/exhaustive-deps": "warn", "jsx-a11y/click-events-have-key-events": "warn", "jsx-a11y/no-static-element-interactions": "warn", "no-unneeded-ternary": "warn", "import/prefer-default-export": "warn"}}